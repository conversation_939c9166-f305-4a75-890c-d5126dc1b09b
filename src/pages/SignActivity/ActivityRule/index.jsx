import React, {
  Fragment,
  useRef,
  useState,
} from 'react';
import {
  <PERSON><PERSON>, Divider, Space, Popconfirm, notification, Spin,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import CommonTableList from '@/components/CommonTableList';
import {
  getContinuousCampaignList,
  continuousCampaignOnline,
  deleteContinuousCampaign,
  getContinuousCampaignDetail,
} from '@/services/signActivity';
import DetailModal from './components/DetailModal';

const ActivityRule = () => {
  const actionRef = useRef();
  const [modalVisible, setModalVisible] = useState(false); // 弹窗可见性
  const [actionType, setActionType] = useState('');
  const [initialConfig, setInitialConfig] = useState({});
  const [loading, setLoading] = useState(false);

  /**
   * 列表刷新
   */
  const handleReload = () => {
    actionRef.current?.reload();
  };

  /**
   * 列表数据请求
   */
  const getTableData = async (params) => {
    const param = {
      page: params.current,
      ...params,
    };
    delete param.current;

    const res = await getContinuousCampaignList(param);
    if (res?.data) {
      const { data } = res;
      return {
        data: data.list,
        total: data.total,
      };
    }
    return {
      data: [],
      total: 0,
    };
  };

  /**
   * 活动上下线
   */
  const changeOnlineStatus = async (record) => {
    const params = {
      id: record.id,
      onlineOperation: record.onlineStatus === 1 ? 2 : 1,
    };
    const res = await continuousCampaignOnline(params);
    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
        description: res?.message || '',
      });
      handleReload();
    }
  };

  /**
   * 活动删除
   */
  const handelDelete = async (record) => {
    const param = {
      id: record.id,
    };
    const res = await deleteContinuousCampaign(param);
    if (res.code === '200') {
      notification.success({
        message: '删除成功',
      });
      handleReload();
    }
  };

  /**
   * 处理弹窗展示
   * 编辑 edit 审核 audit 详情 view 新增add 复制 copy
   */

  const handleAction = async (record, actType) => {
    const { scene, id } = record;
    if (scene) {
      // 请求详情信息
      setLoading(true);
      const res = await getContinuousCampaignDetail({ id });
      if (res.code === '200' && res.data) {
        const { startTime, endTime, rewardConfigDTOS } = res.data;
        const receiveDays = rewardConfigDTOS.map((item) => item.days);
        const tempRecord = {
          ...res.data,
          activityTime: [startTime, endTime],
          receiveDays,
        };
        if (['add', 'copy'].includes(actType)) {
          tempRecord.id = undefined;
          tempRecord.code = undefined;
          tempRecord.auditStatus = undefined;
          tempRecord.onlineStatus = undefined;
        }
        if (actType === 'copy') {
          const tempRewardConfigDTOS = rewardConfigDTOS.map((rewardConfig) => {
            const { necessaryRewardList = [], probabilityRewardList = [] } = rewardConfig;
            rewardConfig.necessaryRewardList = necessaryRewardList.map((necessaryReward) => {
              delete necessaryReward.rewardId;
              return necessaryReward;
            });
            rewardConfig.probabilityRewardList = probabilityRewardList.map((probabilityReward) => {
              delete probabilityReward.rewardId;
              return probabilityReward;
            });
            return rewardConfig;
          });
          tempRecord.rewardConfigDTOS = tempRewardConfigDTOS;
        }

        setInitialConfig(tempRecord);
      }
    } else {
      setInitialConfig({});
    }
    setActionType(actType);
    setLoading(false);
    setModalVisible(true);
  };

  const columns = [
    {
      title: '活动id',
      dataIndex: 'id',
      key: 'id',
      align: 'center',
      width: 90,
      fixed: 'left',
    },
    {
      title: '活动code',
      dataIndex: 'code',
      key: 'code',
      width: 200,
      align: 'center',
      fixed: 'left',
      ellipsis: true,
      copyable: true,
    },
    {
      title: '活动名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      align: 'center',
      fixed: 'left',
    },
    {
      title: '活动场景',
      dataIndex: 'scene',
      key: 'scene',
      valueType: 'select',
      width: 200,
      align: 'center',
      fieldProps: {
        options: [
          { label: '连续记账活动', value: '7' },
          { label: '钱豆签到活动', value: '8' },
          { label: '钱豆达标奖', value: '9' },
          { label: '累计活跃玩法', value: '10' },
          { label: '固定每日签到', value: '11' },
        ],
      },
    },
    {
      title: '活动发起时间',
      dataIndex: 'userTypeName',
      key: 'userTypeName',
      hideInSearch: true,
      width: 360,
      align: 'center',
      render: (text, record) => (
        <div>
          {record.startTime}
          {' '}
          ~
          {record.endTime}
        </div>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      hideInSearch: true,
      align: 'center',
      width: 200,
      // render: (text, record) => <span>{record.createTime && parseDate(record.createTime)}</span>
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      hideInSearch: true,
      align: 'center',
      width: 200,
      // render: (text, record) => <span>{record.updateTime && parseDate(record.updateTime)}</span>
    },
    {
      title: '状态',
      dataIndex: 'onlineStatus',
      key: 'onlineStatus',
      hideInSearch: true,
      valueEnum: {
        1: { text: '禁用', status: 'Error' },
        2: { text: '启用', status: 'Success' },
        3: { text: '已失效', status: 'Default' },
        6: { text: '已冻结', status: 'Warning' },
      },
      width: 150,
      align: 'center',
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      key: 'auditStatus',
      width: 150,
      align: 'center',
      valueEnum: {
        1: { text: '待审核', status: 'Processing' },
        2: { text: '审核通过', status: 'Success' },
        3: { text: '审核驳回', status: 'Error' },
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      hideInSearch: true,
      align: 'center',
      fixed: 'right',
      width: 380,
      render: (text, record) => (
        <Space split={<Divider type="vertical" />} key={record.id}>
          {record.supportEdit === 1 && (
            <Button
              key="edit"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'edit');
              }}
            >
              编辑
            </Button>
          )}
          {record.supportDelete === 1 && (
            <Popconfirm
              title="删除后无法恢复，确认删除吗？"
              onConfirm={() => {
                handelDelete(record);
              }}
              key="del"
              onCancel={() => { }}
              okText="确定"
              cancelText="取消"
            >
              <Button key="delete" type="link" size="small" onClick={() => { }}>
                删除
              </Button>
            </Popconfirm>
          )}
          {record.supportView === 1 && (
            <Button
              key="detail"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'view');
              }}
            >
              详情
            </Button>
          )}
          {record.supportApprove === 1 && (
            <Button
              key="audit"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'audit');
              }}
            >
              审核
            </Button>
          )}

          {record.supportOffline === 1 && (
            <Popconfirm
              title="紧急下线操作"
              description="下线操作会导致C端用户活动不可见，确认要下线吗？"
              onConfirm={() => changeOnlineStatus(record)}
              key="offline"
              onCancel={() => { }}
              okText="确定"
              cancelText="取消"
            >
              <Button
                key="offlinebtn"
                type="link"
                size="small"
                onClick={() => { }}
              >
                下线
              </Button>
            </Popconfirm>
          )}
          {record.supportOnline === 1 && (
            <Button
              key="online"
              type="link"
              size="small"
              onClick={() => changeOnlineStatus(record)}
            >
              上线
            </Button>
          )}
          {record.onlineStatus !== 2 && (
            <Button
              key="copy"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'copy');
              }}
            >
              复制
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <>
      <Spin spinning={loading}>
        <CommonTableList
          key="activityList"
          toolBarRender={() => [
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                handleAction({}, 'add');
              }}
            >
              新增
            </Button>,
          ]}
          columns={columns}
          request={getTableData}
          actionRef={actionRef}
          search={{
            defaultCollapsed: false,
          }}
        />
        <DetailModal
          modalVisible={modalVisible}
          actionType={actionType}
          setActionType={setActionType}
          setModalVisible={setModalVisible}
          handleReload={handleReload}
          setInitialConfig={setInitialConfig}
          initialConfig={initialConfig}
        />
      </Spin>
    </>
  );
};

export default ActivityRule;
