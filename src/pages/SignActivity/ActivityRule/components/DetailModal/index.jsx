import React, { useState, useRef, useEffect } from 'react';
import { BetaSchemaForm } from '@ant-design/pro-components';
import {
  getDmpTag,
  saveContinuousCampaign,
  auditContinuousCampaign,
} from '@/services/signActivity.js';
import CommonDrawer from '@/components/CommonDrawer';
import dayjs from 'dayjs';
import {
  Tabs, message, notification, Button,
} from 'antd';
import { cloneDeep } from 'lodash';
import OperateHistory from "../OperateHistory"; // eslint-disable-line
import ActRewardConfig from '../ActRewardConfig';

const debounce = (func, delay) => {
  let timer;
  return function (...args) {
    const context = this;
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(context, args);
    }, delay);
  };
};

const titleMap = {
  edit: '编辑',
  audit: '审核',
  view: '查看详情',
  add: '新增',
  copy: '复制',
};

const btnMap = {
  edit: '提交',
  audit: '审核通过',
  view: '确认',
  add: '提交',
  copy: '提交',
};

const DetailModal = ({
  modalVisible,
  setModalVisible,
  actionType,
  setActionType,
  width = '60%',
  handleReload,
  setInitialConfig,
  initialConfig,
  noHistoryTable = false,
}) => {
  const [tagList, setTagList] = useState([]);
  const [tagOptions, setTagOptions] = useState([]);
  const [rewardConfigDTOS, setRewardConfigDTOS] = useState([]);
  const formRef = useRef();
  const formDisabled = actionType === 'view' || actionType === 'audit';

  const getDmpTagData = async (params) => {
    if (!params.tagName && !params.tagId) return;
    const res = await getDmpTag(params);
    if (res?.code === '200') {
      setTagList(res.data);
      const options = res.data.map((item) => ({
        value: item.tagIdWithName,
        label: item.tagIdWithName,
      }));
      setTagOptions(options);
    }
  };

  useEffect(() => {
    initialConfig.rewardConfigDTOS
      && setRewardConfigDTOS(initialConfig.rewardConfigDTOS);
    initialConfig.audienceTag
      && getDmpTagData({ tagId: Number(initialConfig.audienceTag) });
  }, [initialConfig]);

  const onReceiveDaysChange = (value) => {
    value.sort((a, b) => a - b);
    const tempRewardConfigDTOS = value?.map((item) => {
      const preData = rewardConfigDTOS?.find((config) => config.days === item);
      return {
        days: item,
        specialMark: preData?.specialMark || false,
        necessaryRewardList: preData?.necessaryRewardList || [],
        probabilityRewardList: preData?.probabilityRewardList || [],
      };
    });
    setRewardConfigDTOS(tempRewardConfigDTOS);
  };

  /**
   * 礼品数据变化
   */

  const onConfigChange = (payload, index) => {
    rewardConfigDTOS[index] = payload;
    const tempRewardConfigDTOS = [...rewardConfigDTOS];
    setRewardConfigDTOS(tempRewardConfigDTOS);
  };

  /**
 * 礼品数据复制
 */

  const onConfigCopy = (index) => {
    const cur = rewardConfigDTOS[index];
    console.log('cur', cur);

    const tempRewardConfigDTOS = rewardConfigDTOS.map((item) => ({
      specialMark: cur.specialMark,
      necessaryRewardList: cur.necessaryRewardList ? cloneDeep(cur.necessaryRewardList) : [],
      probabilityRewardList: cur.probabilityRewardList ? cloneDeep(cur.probabilityRewardList) : [],
      days: item.days,
    }));

    setRewardConfigDTOS(tempRewardConfigDTOS);
    notification.success({ message: '已成功复制该奖品配置到其他日' });
  };

  /**
   * 礼品数据校验
   */

  const commonCheck = (item, day) => {
    const typeText = item.type === 'necessary' ? '必中奖品' : '概率奖品';
    if (!item.rewardType) {
      message.error(`请配置第${day}天${typeText}的奖品类型`);
      return false;
    }
    if (!item.rewardName) {
      message.error(`请配置第${day}天${typeText}的奖品名称`);
      return false;
    }
    if (
      item.rewardType !== 'qd'
      && item.rewardType !== 'cashCard'
      && !item.quotaId
    ) {
      message.error(`请配置第${day}天${typeText}的库存id`);
      return false;
    }
    if (item.rewardType === 'redPacketCoupon' && !item.templateId) {
      message.error(`请配置第${day}天${typeText}的返现券规则id`);
      return false;
    }
    if (!item.rewardAmount) {
      message.error(`请配置第${day}天${typeText}的发放数量`);
      return false;
    }
    if (!item.sendLimit) {
      message.error(`请配置第${day}天${typeText}的发放上限`);
      return false;
    }
    return true;
  };

  const checkRewardConfig = ({ scene, receiveDays }) => {
    if (receiveDays?.length !== rewardConfigDTOS?.length) {
      message.error('领取天数对应天数需配置对应奖品信息');
      return false;
    }
    /* eslint-disable */
    for (const reward of rewardConfigDTOS) {
      if (
        scene === "7" &&
        (!reward.necessaryRewardList || !reward.necessaryRewardList.length) &&
        (!reward.probabilityRewardList || !reward.probabilityRewardList.length)
      ) {
        message.error("必中奖品和概率奖品至少要填写一种");
        return false;
      }

      if (
        ["8", "9", "10", '11'].includes(scene)
        &&
        (!reward.necessaryRewardList || !reward.necessaryRewardList.length)
      ) {
        message.error("请配置必中奖品");
        return false;
      }
      // 有必中奖品
      if (reward.necessaryRewardList && reward.necessaryRewardList.length) {
        for (const item of reward.necessaryRewardList) {
          if (!commonCheck(item, reward.days)) {
            return false;
          }
          if (!item.probability || Number(item.probability) !== 100) {
            message.error(`第${reward.days}天的必中奖品概率应配置为100%`);
            return false;
          }
        }
      }

      if (
        scene === "7" &&
        reward.probabilityRewardList &&
        reward.probabilityRewardList.length
      ) {
        for (const item of reward.probabilityRewardList) {
          if (!commonCheck(item, reward.days)) {
            return false;
          }
        }
        const probabilitys = reward.probabilityRewardList.map((item) =>
          Number(item.probability)
        );
        let sum = probabilitys.reduce((total, curVal) => total + curVal, 0);
        sum = sum.toFixed(7);
        if (sum > 100) {
          message.error(`第${reward.days}天的概率奖品，总概率超过100%，请修改`);
          return false;
        }
        if (
          (!reward.necessaryRewardList || !reward.necessaryRewardList.length) &&
          sum !== 100
        ) {
          message.error("只配置概率奖品时，总概率必须等于100%");
          return false;
        }
      }
    }
    return true;
    /* eslint-enable */
  };

  /**
   * 重置数据 关闭弹窗
   */
  const resetDialog = () => {
    setTagList([]);
    setTagOptions([]);
    setModalVisible(false);
    setActionType('');
    setInitialConfig({});
    setRewardConfigDTOS([]);
    // 刷新列表
    handleReload && handleReload();
  };

  /**
  * 修改审核状态
  */
  const handleAudit = async (params) => {
    const res = await auditContinuousCampaign(params);

    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
      });
      resetDialog();
    }
  };

  /**
   * 保存设置
   */
  const saveSetting = async (params) => {
    const res = await saveContinuousCampaign(params);
    if (res?.code === '200') {
      notification.success({
        message: '保存成功',
      });
      resetDialog();
    }
  };

  /**
   * 提交
   */
  const handleSubmit = (values) => {
    const { activityTime, userSelect, audienceName } = values;

    if (!checkRewardConfig(values)) return;
    // 审核通过
    if (actionType === 'audit') {
      handleAudit({ id: initialConfig?.id, auditOperation: 2 });
      return;
    }

    // 提交审核
    if (['add', 'copy', 'edit'].includes(actionType)) {
      const params = {
        ...initialConfig,
        ...values,
        rewardConfigDTOS,
        startTime: activityTime[0],
        endTime: activityTime[1],
      };
      delete params.activityTime;

      // dmp相关数据
      if (userSelect === 'dmpTag') {
        const dmpItem = tagList.find(
          (item) => item.tagIdWithName === audienceName,
        );

        dmpItem && (params.audienceTag = dmpItem?.id);
      }
      saveSetting(params);
    }
  };

  const columns = [
    {
      title: '活动名称',
      dataIndex: 'name',
      key: 'name',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          { max: 20, message: '限制20个字符' },
        ],
        extra: '内部命名，限制20个字符',
      },
      fieldProps: () => ({
        maxLength: 20,
      }),
    },
    {
      title: '活动场景',
      dataIndex: 'scene',
      key: 'scene',
      valueType: 'select',
      width: '100%',
      align: 'center',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      fieldProps: (form) => ({
        options: [
          { label: '连续记账活动', value: '7' },
          { label: '钱豆签到活动', value: '8' },
          { label: '钱豆达标奖', value: '9' },
          { label: '累计活跃玩法', value: '10' },
          { label: '固定每日签到', value: '11' },
        ],
        disabled: (actionType === 'edit' && initialConfig.id) || formDisabled,
        onChange: (value) => {
          form.setFieldValue('receiveDays', []);
          form.setFieldValue('consecutiveDays', undefined);
          form.setFieldValue('validDays', undefined);
          form.setFieldValue('attainmentTimes', undefined);
          form.setFieldValue('activityTime', undefined);
          console.log('rewardConfigDTOS', rewardConfigDTOS, 'value', value);
          if (rewardConfigDTOS?.length && value !== '7') {
            const tempRewardConfigDTOS = rewardConfigDTOS.map((item) => {
              item.probabilityRewardList = [];
              return item;
            });
            setRewardConfigDTOS(tempRewardConfigDTOS);
          }
        },
      }),
    },
    {
      title: '活动code码',
      dataIndex: 'code',
      key: 'code',
      fieldProps: {
        disabled: true,
      },
    },
    {
      title: '活动用户',
      dataIndex: 'userSelect',
      key: 'userSelect',
      valueType: 'radio',
      fieldProps: (form) => ({
        options: [
          { label: '按用户标签', value: 'dmpTag' },
          { label: '所有人', value: 'allUsers' },
        ],
        onChange: () => {
          form.setFieldValue('audienceName', '');
        },
      }),
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      valueType: 'dependency',
      name: ['userSelect'],
      columns: ({ userSelect }) => (userSelect === 'dmpTag'
        ? [
          {
            title: '用户标签',
            dataIndex: 'audienceName',
            key: 'audienceName',
            valueType: 'select',
            fieldProps: () => ({
              onSearch: debounce((value) => {
                getDmpTagData({ tagName: value });
              }, 500),
              showSearch: true,
              options: tagOptions,
            }),
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
              ],
            },
          },
        ]
        : []),
    },
    {
      title: '活动规则详情',
      dataIndex: 'ruleUrl',
      key: 'ruleUrl',
    },
    {
      title: '活动时间',
      valueType: 'dateTimeRange',
      dataIndex: 'activityTime',
      key: 'activityTime',
      fieldProps: (form) => ({
        onChange: (value) => {
          const curScene = form.getFieldValue('scene');
          if (curScene === '11') {
            // 计算日期范围内的天数
            if (value?.[0] && value?.[1]) {
              console.log('2222');
              const startDay = dayjs(value[0]).startOf('day');
              const endDay = dayjs(value[1]).startOf('day');
              // 计算天数差（包含首尾两天）
              const days = endDay.diff(startDay, 'days') + 1;

              // 设置连续任务天数
              form.setFieldValue('consecutiveDays', days);

              // 生成天数数组并设置 receiveDays
              const arr = Array.from(
                { length: days },
                (_, index) => index + 1,
              );
              form.setFieldValue('receiveDays', arr);

              // 触发奖励配置更新
              onReceiveDaysChange(arr);
            }
          }
        },
      }),
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          () => ({
            validator(_, value) {
              if (value && dayjs(value[1]) > dayjs()) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('结束时间必须晚于当前时间'));
            },
          }),
        ],
        extra: '活动时间编辑修改后即时生效',
      },
    },
    {
      valueType: 'dependency',
      name: ['scene'],
      columns: ({ scene }) => [
        {
          title: (() => {
            switch (scene) {
              case '10':
                return '累计任务天数';
              case '11':
                return '签到天数';
              default:
                return '连续任务天数';
            }
          })(),
          dataIndex: 'consecutiveDays',
          ellipsis: true,
          hideInTable: true,
          align: 'center',
          valueType: 'digit',
          fieldProps: (form) => ({
            onBlur: (value) => {
              const list = form.getFieldValue('receiveDays');
              if (['10', '11'].indexOf(scene) !== -1) {
                const arr = Array.from(
                  { length: value },
                  (_, index) => index + 1,
                );
                form.setFieldValue('receiveDays', arr);

                onReceiveDaysChange(arr);
              } else {
                if (!list?.length) return;
                form.setFieldValue(
                  'receiveDays',
                  list.filter((item) => item <= value),
                );
              }
            },
            precision: 0,
            min: 1,
            style: { width: '100%' },
            disabled: ['11'].indexOf(scene) !== -1 || formDisabled,
          }),
          formItemProps: {
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
            ],
            extra: (() => {
              switch (scene) {
                case '10':
                  return <>累计标准：签到成功，则累计一天</>;
                case '11':
                  return <>签到标准：用户行为符合签到定义则任务完成</>;
                default:
                  return (
                    <>
                      <div>
                        连续任务天数,为保证用户体验，挑战天数调整后，不会立刻生效，会从下一轮周期开始生效
                      </div>
                      <div>【钱豆达标奖】配置7天以上奖励 C端UI不支持</div>
                      <div> 【记账挑战赛】【签到活动】只支持7天</div>
                    </>
                  );
              }
            })(),
          },
        },
      ],
    },
    {
      valueType: 'dependency',
      name: ['scene'],
      columns: ({ scene }) => {
        if (scene === '9') {
          return [
            {
              title: '达标任务数',
              dataIndex: 'attainmentTimes',
              key: 'attainmentTimes',
              ellipsis: true,
              hideInTable: true,
              align: 'center',
              valueType: 'digit',
              fieldProps: () => ({
                precision: 0,
                min: 1,
                max: 10,
                style: { width: 200 },
              }),
              formItemProps: {
                addonBefore: '每日完成',
                addonAfter: '个任务（填正整数）',
                rules: [
                  {
                    required: true,
                    message: '此项为必填项',
                  },
                ],
                extra: '最小值为1，最大不超过10',
              },
            },
          ];
        }
        if (scene === '7') {
          return [
            {
              title: '领取有效期',
              dataIndex: 'validDays',
              key: 'validDays',
              ellipsis: true,
              hideInTable: true,
              align: 'center',
              valueType: 'digit',
              fieldProps: () => ({
                precision: 0,
                min: 1,
                style: { width: 200 },
              }),
              formItemProps: (form) => ({
                addonBefore: '达成后',
                addonAfter: '天内可领取奖励 （填正整数)',
                rules: [
                  {
                    required: true,
                    message: '此项为必填项',
                  },
                  () => ({
                    validator(_, value) {
                      const consecutiveDays = form.getFieldValue('consecutiveDays');
                      if (value <= consecutiveDays) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error(`请输入1-${consecutiveDays}之间的数字`),
                      );
                    },
                  }),
                ],
                extra:
                  '为保证用户体验，有效期调整后，已获得礼包的按原有效期，新获得按最新有效期',
              }),
            },
          ];
        }
        return [];
      },
    },
    {
      valueType: 'dependency',
      name: ['consecutiveDays', 'scene'],
      columns: ({ consecutiveDays, scene }) => [
        {
          title: '领取天数',
          dataIndex: 'receiveDays',
          valueType: 'select',
          ellipsis: true,
          hideInTable: true,
          align: 'center',
          formItemProps: {
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
            ],
            extra:
              '指定完成XX天获得奖励,如配置2、4，则指定第2天、第4天完成获得奖励',
          },
          fieldProps: () => ({
            disabled: ['10', '11'].indexOf(scene) !== -1 || formDisabled,
            sorter: (a, b) => a - b,
            options: Array.from(
              { length: consecutiveDays },
              (_, i) => i + 1,
            ).map((num) => ({ value: num, label: num })),
            mode: 'multiple',
            onChange: (value) => {
              onReceiveDaysChange(value);
            },
          }),
        },
        {
          title: '奖品对应天数',
          // dataIndex: 'rewardConfigDTOS',
          key: 'rewardConfigDTOS',
          formItemProps: {
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
            ],
          },
          renderFormItem: () => {
            const items = rewardConfigDTOS.map((config, index) => ({
              key: config.days,
              label: config.days,
              config,
              children: (
                <ActRewardConfig
                  key={config.days}
                  actionType={actionType}
                  scene={scene}
                  config={{
                    ...config,
                  }}
                  onChange={(payload) => onConfigChange(payload, index)}
                  onCopy={() => onConfigCopy(index)}
                />
              ),
            }));

            return (
              <div>
                <Tabs
                  defaultActiveKey="1"
                  onChange={() => { }}
                  type="card"
                  items={items}
                />
              </div>
            );
          },
        },
      ],
    },
  ];

  return (
    <CommonDrawer
      formRef={formRef}
      title={titleMap[actionType]}
      width={width}
      open={modalVisible}
      onOpenChange={(value) => {
        setModalVisible(value);
        !value && resetDialog();
      }}
      disabled={actionType === 'view' || actionType === 'audit'}
      submitter={{
        searchConfig: {
          submitText: btnMap[actionType],
          resetText: '取消',
        },
        submitButtonProps: {
          disabled: false,
          style: {
            display: actionType === 'view' ? 'none' : '',
          },
        },
        render: (props, defaultDoms) => [
          ...defaultDoms,
          actionType === 'audit' && (
            <Button
              key="extra-reset"
              onClick={() => {
                handleAudit({ id: initialConfig?.id, auditOperation: 3 });
              }}
              disabled={false}
            >
              审核驳回
            </Button>
          ),
        ],
      }}
      onFinish={(values) => {
        handleSubmit(values, rewardConfigDTOS);
      }}
      drawerProps={{
        destroyOnClose: true,
      }}
      layout="horizontal"
      formLayoutType={{
        labelCol: { span: 3 },
        wrapperCol: { span: 21 },
      }}
      initialValues={initialConfig}
      formRender={(
        <>
          <BetaSchemaForm layoutType="Embed" columns={columns} />
          {/* 操作记录 */}
          {!noHistoryTable
            && (actionType === 'audit' || actionType === 'view') && (
              <OperateHistory
                key="operateHistory"
                id={initialConfig?.id}
                bizCode="checkInCampaign"
                disabled={false}
              />
          )}
        </>
      )}
    />
  );
};

export default DetailModal;
