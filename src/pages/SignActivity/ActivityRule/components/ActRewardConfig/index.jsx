import React, { useEffect, useState, Fragment } from 'react';
import {
  Input,
  Select,
  Checkbox,
  Button,
  message,
  Table,
  Popconfirm,
  Row,
} from 'antd';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import { Upload } from 'finance-busi-components-toB';
import { rewardTypeOptions } from '@/constant';

const ActRewardConfig = (props) => {
  const {
    actionType, scene, config = {}, onChange, onCopy,
  } = props;

  const [data, setData] = useState({
    necessaryRewardList: [],
    probabilityRewardList: [],
    specialMark: false,
    days: -1,
  });

  useEffect(() => {
    const {
      necessaryRewardList = [],
      probabilityRewardList = [],
      specialMark,
      days,
    } = config;

    setData({
      days,
      specialMark,
      necessaryRewardList: necessaryRewardList.map((item) => ({
        ...item,
        type: 'necessary',
      })),
      probabilityRewardList: probabilityRewardList.map((item) => ({
        ...item,
        type: 'probability',
      })),
    });
  }, [config]);

  const formDisabled = actionType === 'view' || actionType === 'audit';

  const validNum = (value, param, rewardType) => {
    message.destroy();
    const checkMap = {
      rewardAmount: () => {
        if (value < 0) {
          message.error('请输入非负数');
          return false;
        }
        if (rewardType !== 'cashCoupon') {
          const reg = /^[1-9]\d*$/;
          if (value && !reg.test(value)) {
            message.error('请输入正整数');
            return false;
          }
        }
        return true;
      },
      sendLimit: () => {
        if (value && String(value).indexOf('.') > -1) {
          message.error('请输入整数');
          return false;
        }
        return true;
      },
      probability: () => {
        if (value < 0) {
          message.error('请输入非负数');
          return false;
        }
        const reg = /^\d+(\.\d{0,7})?$/;
        if (value && !reg.test(value)) {
          return false;
        }
        return true;
      },
    };
    if (checkMap[param]) {
      return checkMap[param]();
    }
    return true;
  };

  // inputType: 1输入框，2选择框
  const handleChange = (e, inputType, record, param, index) => {
    const val = inputType === 1 ? e.target.value : e;
    if (!validNum(val, param, record.rewardType)) return;
    record[param] = val;
    const necessaryList = data.necessaryRewardList;
    const probabilityList = data.probabilityRewardList;
    if (param === 'rewardType') {
      if (record.quotaId) {
        delete record.quotaId;
      }
      if (record.templateId) {
        delete record.templateId;
      }
    }
    if (record.type === 'necessary') {
      necessaryList[index] = record;
    } else {
      probabilityList[index] = record;
      let sum = 0;
      probabilityList.forEach((item) => {
        sum += Number(item.probability);
      });
      sum = sum.toFixed(7);
      if (sum > 100) {
        message.error('总概率不能超过100%哦');
        return;
      }
    }

    setData({
      ...data,
      necessaryRewardList: necessaryList,
      probabilityRewardList: probabilityList,
    });
    onChange({
      ...data,
      necessaryRewardList: necessaryList,
      probabilityRewardList: probabilityList,
    });
  };

  const deleteReward = (record, index) => {
    let list = [];
    if (record.type === 'necessary') {
      list = data.necessaryRewardList;
      list.splice(index, 1);
      setData({ ...data, necessaryRewardList: list });
      onChange({ ...data, necessaryRewardList: list });
    } else {
      list = data.probabilityRewardList;
      list.splice(index, 1);
      setData({ ...data, probabilityRewardList: list });
      onChange({ ...data, probabilityRewardList: list });
    }
  };

  // rewardType: 1添加必中奖品， 2添加概率奖品
  const addReward = (rewardType) => {
    const list = (rewardType === 1
      ? data.necessaryRewardList
      : data.probabilityRewardList) || [];
    list.push({
      type: rewardType === 1 ? 'necessary' : 'probability',
      rewardId: '',
      rewardType: '',
      rewardPic: '',
      rewardName: '',
      rewardAmount: '',
      quotaId: '',
      templateId: '',
      sendLimit: '',
      probability: rewardType === 1 ? 100 : null,
    });
    if (rewardType === 1) {
      setData({ ...data, necessaryRewardList: list });
      onChange({ ...data, necessaryRewardList: list });
    } else if (rewardType === 2) {
      setData({ ...data, probabilityRewardList: list });
      onChange({ ...data, probabilityRewardList: list });
    }
  };

  const onCheckboxChange = (e) => {
    setData({ ...data, specialMark: e.target.checked });
    onChange({ ...data, specialMark: e.target.checked });
  };

  const getRewardType = (rewardType) => rewardTypeOptions?.find((item) => item?.value === rewardType)?.label;

  // flag: 0代表库存，1代表发放数量
  const getPlaceHolder = (rewardType, flag) => {
    const typeMap = {
      cashCoupon: ['请输入红包库存id', '请输入红包面额，保留小数点后两位'],
      points: ['请输入积分库存id', '请输入单人发放积分数量，正整数'],
      payCoupon: ['请输入支付券库存id', '请输入支付券单人发放数量'],
      redPacketCoupon: ['请输入借款返现券库存id', '请输入单人发放券数，正整数'],
      qd: ['', '请输入单人发放钱豆数，正整数'],
      cashCard: ['', '请输入提现卡发放面额'],
      virtual: ['请输入虚拟奖品库存id', '请输入虚拟奖品发放数量，正整数'],
      activityCoin: ['请输入活动货币库存id', '请输入活动货币发放数量，正整数'],
    };
    return rewardType && typeMap[rewardType][flag];
  };

  const getRewardPicTitle = () => (
    <div>
      <div>奖品图片</div>
      <div style={{ fontSize: '12px', color: 'rgba(0,0,0,0.4)' }}>
        （尺寸：120*120）
      </div>
    </div>
  );

  const getTips = (column, rewardType) => {
    switch (rewardType) {
      case 'cashCoupon':
        return column === 'rewardAmount'
          ? '填红包发放面额，单位是“分”，如发放1元，则填100'
          : '填红包上限总金额，不是上限数量';
      case 'points':
        return column === 'rewardAmount'
          ? '填积分发放面额，如发积分1个，则填1'
          : '填积分上限总量';
      case 'payCoupon':
        return column === 'rewardAmount'
          ? '填发券数量，如发券1张，则填1'
          : '填该券发放上限数量';
      case 'qd':
        return column === 'rewardAmount'
          ? '填钱豆发放面额，如发豆1个，则填1'
          : '填钱豆上限总量';
      case 'redPacketCoupon':
        return column === 'rewardAmount'
          ? '填发券数量，如发券1张，则填1'
          : '填该券发放上限数量';
      case 'cashCard':
        return column === 'rewardAmount'
          ? '填提现卡发放面额，单位【分】，如发放1元，则填写100'
          : '填提现卡限总量';
      case 'virtual':
        return column === 'rewardAmount'
          ? '填奖品发放数量，发放1个虚拟奖品则填1'
          : '填奖品上限总量';
      case 'activityCoin':
        return column === 'rewardAmount'
          ? '填奖品发放数量，发放1个抽奖机会则填1'
          : '填奖品上限总量';
      default:
        return '';
    }
  };

  const getFooter = () => {
    let sum = 0;
    data.probabilityRewardList?.forEach((item) => {
      if (item.probability) {
        sum += Number(item.probability);
      }
    });
    sum = sum.toFixed(7);
    return (
      <div style={{ textAlign: 'center' }}>
        总概率：
        {sum}
        %
      </div>
    );
  };

  const columns = [
    {
      title: '奖品序号',
      dataIndex: 'index',
      key: 'index',
      align: 'center',
      width: 100,
      fixed: 'right',
      render: (text, record, index) => index + 1,
    },
    {
      title: '奖品类型',
      dataIndex: 'rewardType',
      key: 'rewardType',
      width: 200,
      align: 'center',
      render: (text, record, index) => (!formDisabled ? (
        <Select
          style={{ width: '120px' }}
          value={record.rewardType}
          onChange={(e) => handleChange(e, 2, record, 'rewardType', index)}
          options={rewardTypeOptions}
        />
      ) : (
        <div>{getRewardType(text)}</div>
      )),
    },
    {
      title: getRewardPicTitle(),
      dataIndex: 'rewardPic',
      key: 'rewardPic',
      width: 200,
      align: 'center',
      render: (text, record, index) => {
        if (!formDisabled) {
          return (
            <>
              <Upload
                listType="picture-card"
                limit={{
                  width: 120,
                  height: 120,
                  size: 100,
                  type: ['png', 'jpg', 'jpeg'],
                  maxCount: 1,
                  description: false,
                }}
                fileList={
                  record.rewardPic
                    ? [
                      {
                        uid: '-1',
                        name: 'image.png',
                        status: 'done',
                        url: record.rewardPic,
                      },
                    ]
                    : null
                }
                appKey="finance-operation-admin.vmic.xyz"
                appName="finance-operation-admin"
                // supportUrlInput={true}
                onChange={(fileInfo) => {
                  const picUrl = fileInfo.file?.response?.data?.filePath;
                  handleChange(picUrl, 2, record, 'rewardPic', index);
                }}
              />
            </>
          );
        }
        if (text) {
          return <img src={text} alt="" style={{ width: '50px' }} />;
        }
        return '-';
      },
    },
    {
      title: '奖品名称',
      dataIndex: 'rewardName',
      key: 'rewardName',
      width: 200,
      align: 'center',
      render: (text, record, index) => (!formDisabled ? (
        <Input
          value={text}
          placeholder="12个字以内"
          maxLength={12}
          onChange={(e) => handleChange(e, 1, record, 'rewardName', index)}
        />
      ) : (
        <div>{text}</div>
      )),
    },
    {
      title: '库存id',
      dataIndex: 'quotaId',
      key: 'quotaId',
      width: 200,
      align: 'center',
      render: (text, record, index) => (!formDisabled ? (
        <Input
          disabled={
            record.rewardType === 'qd' || record.rewardType === 'cashCard'
          }
          type="number"
          value={text}
          placeholder={getPlaceHolder(record.rewardType, 0)}
          maxLength={12}
          onChange={(e) => handleChange(e, 1, record, 'quotaId', index)}
        />
      ) : (
        <div>{text}</div>
      )),
    },
    {
      title: '返现券规则id',
      dataIndex: 'templateId',
      key: 'templateId',
      width: 200,
      align: 'center',
      render: (text, record, index) => (!formDisabled ? (
        <Input
          disabled={record.rewardType !== 'redPacketCoupon'}
          value={text}
          placeholder="请输入返现券规则id"
          maxLength={12}
          onChange={(e) => handleChange(e, 1, record, 'templateId', index)}
        />
      ) : (
        <div>{text}</div>
      )),
    },
    {
      title: '发放数量',
      dataIndex: 'rewardAmount',
      key: 'rewardAmount',
      width: 300,
      align: 'center',
      render: (text, record, index) => (!formDisabled ? (
        <div style={{ position: 'relative' }}>
          <Input
            value={text}
            type="number"
            placeholder={getPlaceHolder(record.rewardType, 1)}
            maxLength={12}
            onChange={(e) => handleChange(e, 1, record, 'rewardAmount', index)}
          />
          <div style={{ position: 'absolute', textAlign: 'left' }}>
            {getTips('rewardAmount', record.rewardType)}
          </div>
        </div>
      ) : (
        <div>
          <div>
            {text}
            {' '}
            {record.rewardType === 'cashCoupon' && <span>(分)</span>}
          </div>
          <div style={{ textAlign: 'center', color: '#666' }}>
            {getTips('rewardAmount', record.rewardType)}
          </div>
        </div>
      )),
    },
    {
      title: '发放上限',
      dataIndex: 'sendLimit',
      key: 'sendLimit',
      width: 300,
      align: 'center',
      render: (text, record, index) => (!formDisabled ? (
        <div style={{ position: 'relative' }}>
          <Input
            value={text}
            type="number"
            placeholder="请输入发放上限数量，-1为无上限"
            maxLength={12}
            onChange={(e) => handleChange(e, 1, record, 'sendLimit', index)}
          />
          <div style={{ position: 'absolute', textAlign: 'left' }}>
            {getTips('sendLimit', record.rewardType)}
          </div>
        </div>
      ) : (
        <div>
          <div>{text}</div>
          <div style={{ textAlign: 'center', color: '#666' }}>
            {getTips('sendLimit', record.rewardType)}
          </div>
        </div>
      )),
    },
    {
      title: '概率',
      dataIndex: 'probability',
      key: 'probability',
      align: 'center',
      width: 260,
      render: (text, record, index) => (!formDisabled ? (
        <div>
          <Input
            style={{
              display: 'inline-block',
              width: 'calc(100% - 60px)',
              margin: 'auto 10px',
            }}
            value={text}
            placeholder={record.type === 'necessary' ? '100.00' : ''}
            onChange={(e) => handleChange(e, 1, record, 'probability', index)}
            disabled={record.type === 'necessary'}
          />
          <span>%</span>
        </div>
      ) : (
        text && (
          <div>
            {text}
            %
          </div>
        )
      )),
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      align: 'center',
      fixed: 'right',
      width: 250,
      render: (text, record, index) => (
        <Popconfirm
          title="确认删除吗"
          onConfirm={() => deleteReward(record, index)}
          onCancel={() => { }}
          disabled={formDisabled}
          okText="删除"
          cancelText="取消"
        >
          <Button type="text" danger icon={<MinusCircleOutlined />} />
        </Popconfirm>
      ),
    },
  ];

  const copyConfirm = () => {
    onCopy();
  };

  return (
    <div>
      {
        scene === '11' && ['copy', 'add'].indexOf(actionType) !== -1 && (
          <Row>
            <Popconfirm
              title="是否确定复制这个奖品配置"
              description="复制这个奖品配置，当前奖品配置将被复制到并覆盖到其他日的奖品?"
              onConfirm={copyConfirm}
              okText="确定"
              cancelText="取消"
            >
              <Button>复制当前配置</Button>
            </Popconfirm>
          </Row>
        )
      }
      <Checkbox
        checked={data.specialMark}
        onChange={onCheckboxChange}
        disabled={formDisabled}
      >
        标记为特殊奖励
      </Checkbox>
      <div style={{ display: 'inline-block', verticalAlign: 'middle' }}>
        【钱豆达标奖】【钱豆签到】【累计活跃】【固定每日签到】活动场景下为高亮显示高价值奖励;
        {' '}
        <br />
        【连续记账】活动场景下为盲盒奖励
      </div>
      <div style={{ overflow: 'auto' }}>
        {!formDisabled ? (
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => addReward(1)}
          >
            添加必中奖品
          </Button>
        ) : (
          <h4 style={{ margin: '15px auto' }}>必中奖品</h4>
        )}
        <Table
          dataSource={(data.necessaryRewardList || []).map((item, index) => ({
            ...item,
            key: index,
          }))}
          columns={columns}
          pagination={false}
          scroll={{ x: '100%' }}
          style={{ marginTop: '20px' }}
        />
      </div>

      {scene === '7' && (
        <div style={{ overflow: 'auto' }}>
          {!formDisabled ? (
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => addReward(2)}
            >
              添加概率奖品
            </Button>
          ) : (
            <h4 style={{ margin: '15px auto' }}>概率奖品</h4>
          )}
          <Table
            dataSource={(data.probabilityRewardList || []).map((item, index) => ({
              ...item,
              key: index,
            }))}
            columns={columns}
            pagination={false}
            footer={getFooter}
            scroll={{ x: '100%' }}
            style={{ marginTop: '20px' }}
          />
        </div>
      )}
    </div>
  );
};

export default ActRewardConfig;
