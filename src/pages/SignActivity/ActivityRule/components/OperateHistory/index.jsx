/* eslint-disable */
import React, { useState } from 'react';
import CommonTableList from '@/components/CommonTableList';
import { queryLogList } from '@/services/signActivity.js';
import { Button } from 'antd';
import dayjs from 'dayjs';
import DetailModal from '../DetailModal';

const OperateHistory = ({ id, bizCode }) => {
  const [modalVisible, setModalVisible] = useState(false); // 弹窗可见性
  const [actionType, setActionType] = useState('');
  const [initialConfig, setInitialConfig] = useState({});

  // 操作记录列表数据请求
  const getOperationHistory = async (params) => {
    const param = {
      page: params.current,
      pageSize: params.pageSize,
      operationId: id,
      bizCode,
    };
    const res = await queryLogList(param);

    if (res?.code === '200' && res.data) {
      return {
        data: res.data.list,
        total: res.data.total,
      };
    }
  };

  const columns = [
    {
      title: '操作编号',
      key: 'id',
      dataIndex: 'id',
      align: 'center',
      hideInSearch: true,
      render: (text, record) => (
        <span style={{ color: '#1890ff' }}>{record.id}</span>
      ),
    },
    {
      title: '操作员',
      key: 'operatorName',
      dataIndex: 'operatorName',
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '操作内容',
      key: 'operatorType',
      dataIndex: 'operatorType',
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '操作时间',
      key: 'createTime',
      dataIndex: 'createTime',
      hideInSearch: true,
      align: 'center',
      // render: (text, record) => <span>{parseDate(record.createTime)}</span>
      renderText: (text) => {
        return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
      },
    },
    {
      title: '操作备注',
      key: 'auditRemark',
      dataIndex: 'auditRemark',
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '操作详情',
      dataIndex: 'actionType',
      key: 'actionType',
      align: 'center',
      fixed: 'right',
      hideInSearch: true,
      render: (text, record) => (
        <Button
          key="edit"
          type="link"
          size="small"
          onClick={() => {
            const { startTime, endTime, rewardConfigDTOS } =
              record.contentBefore;
            const receiveDays = rewardConfigDTOS.map((item) => item.days);
            const tempRecord = {
              ...record.contentBefore,
              activityTime: [startTime, endTime],
              receiveDays,
            };
            setInitialConfig(tempRecord);
            setActionType('view');
            setModalVisible(true);
          }}
          disabled={false}
        >
          查看详情
        </Button>
      ),
    },
  ];
  return (
    <>
      <CommonTableList
        key="OperateHistory"
        columns={columns}
        search={false}
        request={getOperationHistory}
        pagination={{
          pageSize: 10,
          showSizeChanger: false,
          showTotal: (total) => {
            return `共${total}项`;
          },
        }}
        headerTitle="操作记录"
      />
      <DetailModal
        key="historyDetailModal"
        modalVisible={modalVisible}
        actionType={actionType}
        setActionType={setActionType}
        setModalVisible={setModalVisible}
        setInitialConfig={setInitialConfig}
        initialConfig={initialConfig}
        noHistoryTable={true}
      />
    </>
  );
};

export default OperateHistory;
