import { ProCard } from '@ant-design/pro-components';
import SceneCardForm from '../../ClearanceConfig/components/SceneCardForm';
import React, { useRef, forwardRef, useState } from 'react';
import Coupon from '../../ClearanceConfig/components/Coupon';

const DragItem = forwardRef(({ id, isReadOnly }, formRef) => {
  return (
    <ProCard style={{ marginBlockStart: 16 }}>
      <ProCard boxShadow colSpan={20}>
        <ProCard layout="left">
          <SceneCardForm index={id} isReadOnly={isReadOnly} />
        </ProCard>
      </ProCard>
    </ProCard>
  );
});

export default DragItem;
