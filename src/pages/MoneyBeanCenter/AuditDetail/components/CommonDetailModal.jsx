import { Drawer } from 'antd';
import AuditInfoCard from '@/components/AuditInfoCard';
import React from 'react';
import CommonDetail from './CommonDetail';

const CommonDetailModal = ({
  commonModalVisible,
  setVisible,
  detailData,
  itemOperationLog,
}) => (
  <Drawer
    title="操作详情"
    width={1500}
    open={commonModalVisible}
    onClose={() => {
      setVisible(false);
    }}
  >
    <CommonDetail detailData={detailData} />
    <AuditInfoCard itemOperationLog={itemOperationLog} />
  </Drawer>
);

export default CommonDetailModal;
