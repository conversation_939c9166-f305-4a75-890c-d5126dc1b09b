import { useState, useEffect } from 'react';
import { Modal, Drawer } from 'antd';
import { ProCard, ProDescriptions } from '@ant-design/pro-components';
import TaskDetail from './TaskDeatil';
import AuditInfoCard from '@/components/AuditInfoCard';

const TaskDetailModal = ({ modalVisible, setVisible, detailData }) => {
  return (
    <Drawer
      title="操作详情"
      width={1500}
      open={modalVisible}
      onClose={() => {
        setVisible(false);
      }}
    >
      <TaskDetail detailData={detailData} />
      <AuditInfoCard itemOperationLog={detailData} />
    </Drawer>
  );
};

export default TaskDetailModal;
