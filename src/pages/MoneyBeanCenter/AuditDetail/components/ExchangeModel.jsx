import React, { useState } from 'react';
import { Drawer, Modal } from 'antd';

import ExchangeDetail from './ExchangeDetail';
import AuditInfoCard from '@/components/AuditInfoCard';

const ExchangeModel = ({ modalVisible, setVisible, detailData }) => {
  return (
    <Drawer
      title="操作详情"
      width={1500}
      open={modalVisible}
      onClose={() => {
        setVisible(false);
      }}
    >
      <ExchangeDetail detailData={detailData} />
      <AuditInfoCard itemOperationLog={detailData} />
    </Drawer>
  );
};

export default ExchangeModel;
