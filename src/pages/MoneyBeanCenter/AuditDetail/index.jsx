import React, {
  Fragment,
  useState,
  useEffect,
} from 'react';
import {
  ProCard,
  ProTable,
} from '@ant-design/pro-components';
import {
  Space, Button, Modal, message, Input,
} from 'antd';
import { useParams } from 'react-router-dom';
import { parseDate } from '@/utils/util';
import { Link } from 'umi';
import {
  getCommonDetail,
  getExchangeDetail,
  auditCommonConfig,
  auditTaskConfig,
  auditExchangeConfig,
  getOperationLogList,
} from '@/services/moneyBeanCenter';
import ExchangeModel from './components/ExchangeModel';
import CommonDetail from './components/CommonDetail';
import CommonDetailModal from './components/CommonDetailModal';
import TaskDetail from './components/TaskDeatil';
import TaskModal from '../TaskConfig/components/TaskModal';
import ExchangeDetail from './components/ExchangeDetail';

const AuditDetail = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [taskModalVisible, setTaskModalVisible] = useState(false);
  const [commonModalVisible, setCommonModalVisible] = useState(false);
  const { scene } = useParams();
  const [detailData, setDetailData] = useState(null);
  const [operationLogList, setOperationLogList] = useState([]);
  const [itemOperationLog, setItemOperationLog] = useState([]);
  const [auditRemark, setAuditRemark] = useState('');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pass, setPass] = useState(false);
  const [selectedData, setSelectedData] = useState({});

  const onGetOperationLogList = async (id) => {
    const sceneToBizCode = {
      common: 'qdCommonConfig',
      exchange: 'qdExchangeConfig',
      task: 'qdTaskConfig',
    };
    const params = {
      operationId: id || '',
      bizCode: sceneToBizCode[scene],
      channelType: scene === 'task' ? 'qd' : '',
      page: 1,
      pageSize: 10,
    };
    const res = await getOperationLogList(params);
    if (res && res.code === '200') {
      setOperationLogList(res.data.list);
    }
  };

  useEffect(() => {
    if (scene === 'exchange') {
      getExchangeDetail().then((res) => {
        if (res && res.code === '200' && res.data) {
          setDetailData(res.data);
          onGetOperationLogList(res.data.id);
        }
      });
    }
    if (scene === 'common') {
      getCommonDetail().then((res) => {
        if (res && res.code === '200' && res.data) {
          setDetailData(res.data);
          onGetOperationLogList(res.data.id);
        }
      });
      // onGetOperationLogList();
    }
    if (scene === 'task') {
      onGetOperationLogList();
    }
  }, []);

  const onPass = async () => {
    const id = scene === 'task' ? '' : detailData.id;
    const params = {
      id,
      auditOperation: 2,
      auditRemark: auditRemark || '审核通过',
    };
    let res;
    if (scene === 'common') {
      res = await auditCommonConfig(params);
    } else if (scene === 'exchange') {
      res = await auditExchangeConfig(params);
    } else {
      res = await auditTaskConfig(params);
    }
    if (res && res.code === '200') {
      setShowConfirmModal(false);
      message.success('审核成功');
      onGetOperationLogList(id);
    }
  };

  const onReject = async () => {
    const id = scene === 'task' ? '' : detailData.id;
    const params = {
      id,
      auditOperation: 3,
      auditRemark: auditRemark || '审核失败',
    };
    let res;
    if (scene === 'common') {
      res = await auditCommonConfig(params);
    } else if (scene === 'exchange') {
      res = await auditExchangeConfig(params);
    } else {
      res = await auditTaskConfig(params);
    }
    if (res && res.code === '200') {
      setShowConfirmModal(false);
      message.success('驳回成功');
      onGetOperationLogList(id);
    }
  };

  const onAudit = (val) => {
    setShowConfirmModal(true);
    setPass(val);
  };

  const configType = {
    common: 'CommonConfig',
    exchange: 'ClearanceConfig',
    task: 'TaskConfig',
  };

  let detailComponent;
  if (scene === 'exchange') {
    detailComponent = <ExchangeDetail />;
  } else if (scene === 'common') {
    detailComponent = <CommonDetail detailData={detailData} />;
  } else {
    detailComponent = <TaskDetail />;
  }

  return (
    <>
      <Space direction="vertical" size="middle">
        <Link
          to={`/MoneyBeanCenter/${configType[scene]}`}
          key={0}
        >
          返回
        </Link>
        <Link
          to={{
            pathname: `/MoneyBeanCenter/${configType[scene]}`,
          }}
        />
        <ProCard>
          {detailComponent}
        </ProCard>
        <ProCard>
          <ProTable
            defaultData={[]}
            headerTitle="操作记录"
            search={false}
            options={false}
            dataSource={operationLogList}
            columns={[
              {
                title: '操作编号',
                dataIndex: 'id',
                ellipsis: true,
              },
              {
                title: '操作员',
                dataIndex: 'operatorName',
                ellipsis: true,
              },
              {
                title: '操作内容',
                dataIndex: 'operatorType',
                ellipsis: true,
              },
              {
                title: '操作时间',
                dataIndex: 'createTime',
                ellipsis: true,
                render: (text, record) => (
                  <span>
                    {record.createTime && parseDate(record.createTime)}
                  </span>
                ),
              },
              {
                title: '操作备注',
                dataIndex: 'auditRemark',
                ellipsis: true,
              },
              {
                title: '操作详情',
                dataIndex: 'contentBefore',
                ellipsis: true,

                render: (text, record) => [
                  <a
                    key="editable"
                    onClick={() => {
                      if (scene === 'exchange') {
                        setModalVisible(true);
                        setItemOperationLog(record);
                      } else if (scene === 'common') {
                        setCommonModalVisible(true);
                        setItemOperationLog(record);
                      } else if (scene === 'task') {
                        setTaskModalVisible(true);
                        setItemOperationLog(record);
                        setSelectedData(record?.contentBefore);
                      }
                    }}
                  >
                    查看详情
                  </a>,
                ],
              },
            ]}
            toolBarRender={() => [
              <Button
                key="pass"
                onClick={() => {
                  onAudit(true);
                }}
                type="primary"
              >
                审核通过
              </Button>,
              <Button
                key="reject"
                onClick={() => {
                  onAudit(false);
                }}
                type="primary"
              >
                审核驳回
              </Button>,
            ]}
          />
        </ProCard>
      </Space>
      <ExchangeModel
        modalVisible={modalVisible}
        setVisible={setModalVisible}
        detailData={itemOperationLog}
      />
      <CommonDetailModal
        commonModalVisible={commonModalVisible}
        detailData={detailData}
        setVisible={setCommonModalVisible}
        itemOperationLog={itemOperationLog}
      />
      <TaskModal
        modalVisible={taskModalVisible}
        setVisible={setTaskModalVisible}
        detailParams={selectedData}
        itemOperationLog={itemOperationLog}
        configType={1}
        isAudit
      />
      {showConfirmModal && (
        <Modal
          title={pass ? '审核通过备注' : '审核驳回意见'}
          open
          onOk={pass ? onPass : onReject}
          onCancel={() => {
            setShowConfirmModal(false);
          }}
        >
          <Input.TextArea
            onChange={(e) => {
              setAuditRemark(e.target.value);
            }}
          />
        </Modal>
      )}
    </>
  );
};

export default AuditDetail;
