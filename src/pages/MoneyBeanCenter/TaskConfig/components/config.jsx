import BraftEditor from 'braft-editor';
import 'braft-editor/dist/index.css';
import './BraftEditor.css';
import {
  validateTrialDuration,
  validateMoneyNotNegativeInteger,
  validateNotNegativeInteger,
  validatePositiveInteger,
  validateAlphanumeric,
} from '@/utils/validate';
import React from 'react';
// 公共
export const FORM_ITEMS_CONFIG_COMMON = {
  copyWriting: {
    title: '引导文案',
    dataIndex: 'guideText',
    valueType: 'input',
    width: 'md',
    formItemProps: {
      help: '（仅可配置一行，不超过20个字）',
      rules: [
        {
          required: true,
          message: '请填写引导文案',
        },
      ],
    },
    render: (val) => <div dangerouslySetInnerHTML={{ __html: val }} />,
    renderFormItem: (schema, config, form) => {
      const id = config.id;
      const data = form.getFieldsValue();
      let valueText = '';
      let key = '';
      let index = '';

      if (id) {
        // 对照组
        const [listKey, listIndex, listField] = id.split('_');
        valueText = data?.[listKey]?.[+listIndex]?.[listField] || config.value;
        key = listKey;
        index = listIndex;
      }

      const onBlur = (value) => {
        const domStr = value.toHTML();
        const ele = document.createElement('div');
        ele.innerHTML = domStr;
        form.setFieldValue([key, index, 'guideText'], domStr);
      };

      valueText = BraftEditor.createEditorState(valueText);
      return (
        <BraftEditor
          value={valueText}
          controls={['text-color']}
          colors={['#000000', '#EA7509', '#FF3636']}
          textBackgroundColor={false}
          onBlur={onBlur}
        />
      );
    },
  },
  count: {
    valueType: 'group',
    columns: [
      {
        title: '每日可观看次数',
        dataIndex: 'dailyLimit',
        valueType: 'input',
        width: 'md',
        fieldProps: {
          suffix: '次',
        },
        formItemProps: {
          rules: [
            {
              required: true,
              message: '请填写每日可观看次数',
            },
            { validator: validateTrialDuration },
          ],
        },
      },
    ],
  },
  duration: {
    valueType: 'group',
    columns: [
      {
        title: '体验时长',
        dataIndex: 'trialDuration',
        valueType: 'input',
        width: 'md',
        fieldProps: {
          suffix: '秒',
        },
        formItemProps: {
          rules: [
            {
              required: true,
              message: '请填写体验时长',
            },
            { validator: validatePositiveInteger },
          ],
        },
      },
    ],
  },
  durations: {
    valueType: 'group',
    columns: [
      {
        dataIndex: '1',
        valueType: 'input',
        title: '体验时长',
        width: 'md',
        fieldProps: {
          suffix: '秒',
          prefix: '第一日',
        },
        formItemProps: {
          rules: [
            {
              required: true,
              message: '请填写第一日体验时长',
            },
            { validator: validatePositiveInteger },
          ],
        },
      },
      {
        title: '体验时长',
        dataIndex: '2',
        valueType: 'input',
        width: 'md',
        fieldProps: {
          suffix: '秒',
          prefix: '第二日',
        },
        formItemProps: {
          rules: [
            {
              required: true,
              message: '请填写第二日体验时长',
            },
            { validator: validatePositiveInteger },
          ],
        },
      },
    ],
  },
  moneyBeanAmount: {
    valueType: 'group',
    columns: [
      {
        title: '发放钱豆数',
        dataIndex: 'eachAppReward',
        valueType: 'input',
        width: 'sm',
        fieldProps: {
          suffix: '个',
        },
        formItemProps: {
          extra:
            '为保证用户体验，发放当日调整后，不会立刻生效，会从次日0点起生效',
          rules: [
            {
              required: true,
              message: '请填写任务钱豆发放奖励数量',
            },
            { validator: validatePositiveInteger },
          ],
        },
      },
      {
        title: '发放提现卡数',
        dataIndex: 'eachAppCashCardReward',
        valueType: 'money',
        width: 'sm',
        fieldProps: {
          suffix: '元',
        },
        formItemProps: {
          extra:
            '为保证用户体验，发放额当日调整后，不会立刻生效，会从次日0点起生效',
          rules: [
            {
              required: true,
              message: '请填写任务提现卡发放奖励数量',
            },
            { validator: validateMoneyNotNegativeInteger },
          ],
        },
      },
    ],
  },
  showAmount: {
    valueType: 'group',
    columns: [
      {
        title: '展示个数',
        dataIndex: 'dailyLimit',
        valueType: 'input',
        width: 'md',
        fieldProps: {
          suffix: '个',
        },
        formItemProps: {
          extra:
            '为保证用户体验，发放当日调整后，不会立刻生效，会从次日0点起生效',
          rules: [
            {
              required: true,
              message: '请填写展示个数',
            },
            { validator: validateTrialDuration },
          ],
        },
      },
    ],
  },
  showSwitch: {
    valueType: 'group',
    columns: [
      {
        dataIndex: 'taskSwitch',
        title: '是否展示',
        valueType: 'switch',
      },
    ],
  },
  showPriority: {
    valueType: 'group',
    columns: [
      {
        dataIndex: 'priority',
        title: '排序',
        valueType: 'input',
        formItemProps: {
          rules: [
            {
              required: true,
              message: '请填写排序',
            },
          ],
        },
      },
    ],
  },
};
// 新人任务专用
export const FORM_ITEMS_CONFIG_NEW_COMER = {
  title: {
    valueType: 'group',
    columns: [
      {
        title: '默认标签',
        dataIndex: 'guideTaskTitle',
        valueType: 'input',
        width: 'md',
        formItemProps: {
          extra: '最多输入4个字',
          rules: [
            {
              required: true,
              message: '不能为空',
            },
            { validator: validateAlphanumeric(4) },
          ],
        },
      },
      {
        title: '次新用户标签',
        dataIndex: 'guideTaskTitle',
        valueType: 'input',
        width: 'md',
        formItemProps: {
          extra: '最多输入4个字',
          rules: [
            {
              required: true,
              message: '不能为空',
            },
            { validator: validateAlphanumeric(4) },
          ],
        },
      },
      {
        title: '气泡',
        dataIndex: 'bubbleText',
        valueType: 'input',
        width: 'md',
        formItemProps: {
          help: '最多输入4个字',
          rules: [
            { validator: validateAlphanumeric(4) },
          ],
        },
      },
    ],
  },
  // 教学任务配置
  teachingReward: {
    valueType: 'group',
    columns: [
      {
        title: '发放钱豆数（教学任务）',
        dataIndex: 'guideAppQdAmount',
        valueType: 'input',
        width: 'sm',
        fieldProps: {
          suffix: '个',
        },
        formItemProps: {
          extra:
            '为保证用户体验，发放额当日调整后，不会立刻生效，会从次日0点起生效',
          rules: [
            {
              required: true,
              message: '请填写教学任务钱豆数',
            },
            { validator: validateMoneyNotNegativeInteger },
          ],
        },
      },
      {
        title: '发放提现卡数（教学任务）',
        dataIndex: 'guideAppCashCardAmount',
        valueType: 'money',
        width: 'sm',
        fieldProps: {
          suffix: '元',
        },
        formItemProps: {
          extra:
            '为保证用户体验，发放额当日调整后，不会立刻生效，会从次日0点起生效',
          rules: [
            {
              required: true,
              message: '请填写教学任务提现卡数',
            },
            { validator: validateMoneyNotNegativeInteger },
          ],
        },
      },
    ],
  },
  moneyBeanAmountNewcomer: {
    valueType: 'group',
    columns: [
      {
        title: '发放钱豆数（下载任务）',
        dataIndex: 'eachAppReward',
        valueType: 'input',
        width: 'sm',
        fieldProps: {
          suffix: '个',
        },
        formItemProps: {
          extra:
            '为保证用户体验，发放当日调整后，不会立刻生效，会从次日0点起生效',
          rules: [
            {
              required: true,
              message: '请填写下载任务钱豆数',
            },
            { validator: validatePositiveInteger },
          ],
        },
      },
      {
        title: '发放提现卡数（下载任务）',
        dataIndex: 'eachAppCashCardReward',
        valueType: 'money',
        width: 'sm',
        fieldProps: {
          suffix: '元',
        },
        formItemProps: {
          extra:
            '为保证用户体验，发放额当日调整后，不会立刻生效，会从次日0点起生效',
          rules: [
            {
              required: true,
              message: '请填写下载任务提现卡数',
            },
            { validator: validateMoneyNotNegativeInteger },
          ],
        },
      },
    ],
  },
  subNewcomer: {
    valueType: 'group',
    columns: [
      {
        dataIndex: ['subNewUserConfig', 'openSwitch'],
        title: '是否开启次新用户翻倍任务开关',
        valueType: 'switch',
      },
      {
        valueType: 'dependency',
        name: ['subNewUserConfig', 'openSwitch'],
        columns: ({ subNewUserConfig }) => {
          const openSwitch = subNewUserConfig?.openSwitch;
          return [
            {
              valueType: 'group',
              columns: [
                {
                  title: '次新用户间隔开始天数',
                  dataIndex: ['subNewUserConfig', 'startDays'],
                  valueType: 'input',
                  width: 'sm',
                  colProps: { span: 12 },

                  fieldProps: {
                    suffix: '天',
                  },
                  formItemProps: {
                    extra:
                        '该数值调整后不会影响此前已生效的次新用户',
                    rules: [
                      {
                        required: openSwitch,
                        message: '请填写次新用户间隔开始天数',
                      },
                      { validator: validatePositiveInteger },
                    ],
                    hidden: !openSwitch,
                  },
                },
                {
                  title: '次新用户间隔结束天数',
                  dataIndex: ['subNewUserConfig', 'endDays'],
                  valueType: 'input',
                  width: 'sm',
                  colProps: { span: 12 },
                  fieldProps: {
                    suffix: '天',
                  },
                  formItemProps: {
                    extra:
                        '该数值调整后不会影响此前已生效的次新用户',
                    rules: [
                      {
                        required: openSwitch,
                        message: '请填写次新用户间隔结束天数',
                      },
                      { validator: validatePositiveInteger },
                    ],
                    hidden: !openSwitch,
                  },
                },
                {
                  title: '次新用户翻倍倒计时',
                  dataIndex: ['subNewUserConfig', 'countDownHours'],
                  valueType: 'input',
                  width: 'sm',
                  colProps: { span: 12 },
                  fieldProps: {
                    suffix: '小时',
                  },
                  formItemProps: {
                    extra:
                        '该数值调整后不会影响此前已生效的次新用户',
                    rules: [
                      {
                        required: openSwitch,
                        message: '请填写次新用户翻倍倒计时',
                      },
                      { validator: validatePositiveInteger },
                    ],
                    hidden: !openSwitch,
                  },
                },
                {
                  title: '次新用户翻倍任务倍率',
                  dataIndex: ['subNewUserConfig', 'taskRate'],
                  valueType: 'digit',
                  fieldProps: {
                    min: 0,
                    precision: 1,
                    step: '0.1',
                  },
                  width: 'sm',
                  colProps: { span: 12 },
                  formItemProps: {
                    extra:
                        '该数值调整后不会影响此前已生效的次新用户',
                    rules: [
                      {
                        required: openSwitch,
                        message: '请填写次新用户翻倍任务倍率',
                      },
                    ],
                    hidden: !openSwitch,
                  },
                },
              ],
            },
          ];
        },
      },
    ],
  },
};
// 付费任务专用
export const FORM_ITEMS_CONFIG_PAY = {
  payRewardListGroup: {
    title: '奖励配置',
    valueType: 'formList',
    width: 1000,
    fieldProps: {
      max: 2,
      min: 2,
      initialValue: [
        { qdRewardAmount: '', cashCardRewardAmount: '', stage: 1 },
        { qdRewardAmount: '', cashCardRewardAmount: '', stage: 2 },
      ],
      copyIconProps: false,
      alwaysShowItemLabel: true,
      tooltip: '如果要只展示第二阶段，那就第一阶段都配成0即可',
    },
    dataIndex: 'cpdRewardConfigDTOList',
    columns: [
      {
        valueType: 'group',
        columns: [
          {
            title: '阶段序号（1-完成安装 2-完成付费）',
            dataIndex: 'stage',
            valueType: 'input',
            width: 'sm',
            colProps: {
              xs: 24,
              sm: 12,
            },
            fieldProps: {
              disabled: true,
            },
            formItemProps: {
              rules: [
                {
                  required: true,
                },
              ],
            },
          },
          {
            title: '发放钱豆数',
            dataIndex: 'qdRewardAmount',
            valueType: 'digit',
            width: 'sm',
            colProps: {
              xs: 24,
              sm: 12,
            },
            fieldProps: {
              suffix: '个',
            },
            formItemProps: {
              extra:
                '为保证用户体验，发放当日调整后，不会立刻生效，会从次日0点起生效',
              rules: [
                {
                  required: true,
                  message: '请填写本阶段连续任务钱豆发放奖励数量',
                },
                { validator: validateNotNegativeInteger },
              ],
            },
          },
          {
            title: '发放提现卡数',
            dataIndex: 'cashCardRewardAmount',
            valueType: 'input',
            width: 'sm',
            colProps: {
              xs: 24,
              sm: 12,
            },
            fieldProps: {
              suffix: '元',
            },
            formItemProps: {
              // extra: '为保证用户体验，发放额当日调整后，不会立刻生效，会从次日0点起生效',
              rules: [
                {
                  required: true,
                  message: '请填写本阶段连续任务提现卡发放奖励数量',
                },
                { validator: validateMoneyNotNegativeInteger },
              ],
            },
          },
        ],
      },
    ],
  },
};
// 连续任务专用
export const FORM_ITEMS_CONFIG_CONTINUOUS = {
  // rewardListGroup: {
  //   valueType: 'group',
  //   tooltip: '如果是一项，则c端页面上展示总的；如果是两项，则C端分2阶段依次展示',
  continuousRewardListGroup: {
    title: '奖励配置',
    valueType: 'formList',
    width: 1000,
    fieldProps: {
      max: 2,
      min: 2,
      initialValue: [
        { qdRewardAmount: '', cashCardRewardAmount: '', stage: 1 },
        { qdRewardAmount: '', cashCardRewardAmount: '', stage: 2 },
      ],
      copyIconProps: false,
      alwaysShowItemLabel: true,
      tooltip: '如果要只展示第二阶段，那就第一阶段都配成0即可',
    },
    dataIndex: 'cpdRewardConfigDTOList',
    columns: [
      {
        valueType: 'group',
        columns: [
          {
            title: '阶段序号（1-完成第一阶段 2-完成第二阶段）',
            dataIndex: 'stage',
            valueType: 'input',
            width: 'sm',
            colProps: {
              xs: 24,
              sm: 12,
            },
            fieldProps: {
              disabled: true,
            },
            formItemProps: {
              rules: [
                {
                  required: true,
                },
              ],
            },
          },
          {
            title: '发放钱豆数',
            dataIndex: 'qdRewardAmount',
            valueType: 'digit',
            width: 'sm',
            colProps: {
              xs: 24,
              sm: 12,
            },
            fieldProps: {
              suffix: '个',
            },
            formItemProps: {
              extra:
                '为保证用户体验，发放当日调整后，不会立刻生效，会从次日0点起生效',
              rules: [
                {
                  required: true,
                  message: '请填写本阶段连续任务钱豆发放奖励数量',
                },
                { validator: validateNotNegativeInteger },
              ],
            },
          },
          {
            title: '发放提现卡数',
            dataIndex: 'cashCardRewardAmount',
            valueType: 'input',
            width: 'sm',
            colProps: {
              xs: 24,
              sm: 12,
            },
            fieldProps: {
              suffix: '元',
            },
            formItemProps: {
              // extra: '为保证用户体验，发放额当日调整后，不会立刻生效，会从次日0点起生效',
              rules: [
                {
                  required: true,
                  message: '请填写本阶段连续任务提现卡发放奖励数量',
                },
                { validator: validateMoneyNotNegativeInteger },
              ],
            },
          },
        ],
      },
    ],
  },
  // },
};
// 注册任务专用
export const FORM_ITEMS_CONFIG_REGISTERED = {
  registeredRewardListGroup: {
    title: '奖励配置',
    valueType: 'formList',
    width: 1000,
    fieldProps: {
      max: 2,
      min: 2,
      initialValue: [
        { qdRewardAmount: '', cashCardRewardAmount: '', stage: 1 },
        { qdRewardAmount: '', cashCardRewardAmount: '', stage: 2 },
      ],
      copyIconProps: false,
      alwaysShowItemLabel: true,
      tooltip: '如果要只展示第二阶段，那就第一阶段都配成0即可',
    },
    dataIndex: 'cpdRewardConfigDTOList',
    columns: [
      {
        valueType: 'group',
        columns: [
          {
            title: '阶段序号（1-完成安装 2-完成注册）',
            dataIndex: 'stage',
            valueType: 'input',
            width: 'sm',
            colProps: {
              xs: 24,
              sm: 12,
            },
            fieldProps: {
              disabled: true,
            },
            formItemProps: {
              rules: [
                {
                  required: true,
                },
              ],
            },
          },
          {
            title: '发放钱豆数',
            dataIndex: 'qdRewardAmount',
            valueType: 'digit',
            width: 'sm',
            colProps: {
              xs: 24,
              sm: 12,
            },
            fieldProps: {
              suffix: '个',
            },
            formItemProps: {
              extra:
                '为保证用户体验，发放当日调整后，不会立刻生效，会从次日0点起生效',
              rules: [
                {
                  required: true,
                  message: '请填写本阶段连续任务钱豆发放奖励数量',
                },
                { validator: validateNotNegativeInteger },
              ],
            },
          },
          {
            title: '发放提现卡数',
            dataIndex: 'cashCardRewardAmount',
            valueType: 'input',
            width: 'sm',
            colProps: {
              xs: 24,
              sm: 12,
            },
            fieldProps: {
              suffix: '元',
            },
            formItemProps: {
              // extra: '为保证用户体验，发放额当日调整后，不会立刻生效，会从次日0点起生效',
              rules: [
                {
                  required: true,
                  message: '请填写本阶段连续任务提现卡发放奖励数量',
                },
                { validator: validateMoneyNotNegativeInteger },
              ],
            },
          },
        ],
      },
    ],
  },
};
export const FORM_ITEMS_CONFIG = {
  ...FORM_ITEMS_CONFIG_COMMON,
  ...FORM_ITEMS_CONFIG_CONTINUOUS,
  ...FORM_ITEMS_CONFIG_NEW_COMER,
  ...FORM_ITEMS_CONFIG_PAY,
  ...FORM_ITEMS_CONFIG_REGISTERED,
};
export const TASK_CONFIG = {
  activityRuleUrl: {
    text: '活动规则详情',
    formItems: [],
  },
  payAppConfig: {
    text: '付费任务',
    formItems: [
      'copyWriting',
      'duration',
      'showSwitch',
      'payRewardListGroup',
      'showAmount',
      'showPriority',
    ],
  },
  downloadAppConfig: {
    text: '下载任务',
    formItems: ['copyWriting', 'duration', 'showSwitch', 'moneyBeanAmount', 'showAmount', 'showPriority'],
  },
  continuousAppConfig: {
    text: '连续任务',
    formItems: ['copyWriting', 'showSwitch', 'durations', 'showAmount', 'continuousRewardListGroup', 'showPriority'],
  },
  promotionalAppConfig: {
    text: '体验任务',
    formItems: ['copyWriting', 'duration', 'showSwitch', 'moneyBeanAmount', 'showAmount', 'showPriority'],
  },
  adVideoConfig: {
    text: '视频任务',
    formItems: ['copyWriting', 'count', 'showSwitch', 'moneyBeanAmount', 'showPriority'],
  },
  registerAppConfig: {
    text: '注册任务',
    formItems: [
      'copyWriting',
      'duration',
      'showSwitch',
      'registeredRewardListGroup',
      'showAmount',
      'showPriority',
    ],
  },
  newcomerAppConfig: {
    text: '新人任务',
    formItems: [
      // 'title',
      'showSwitch',
      // 'teachingReward',
      'moneyBeanAmountNewcomer',
      'showPriority',
      'subNewcomer',
    ],
  },
};

export const getFormItemsByTaskType = (type) => {
  const task = TASK_CONFIG[type];
  const formItemNames = task.formItems;
  return formItemNames.map((name) => FORM_ITEMS_CONFIG[name]);
};
export const getFormItemsByShowList = (showItem) => {
  const res = TASK_CONFIG[showItem].formItems.map((name) => FORM_ITEMS_CONFIG[name]);
  return res;
};

export const getTitle = (item) => TASK_CONFIG[item].text;
