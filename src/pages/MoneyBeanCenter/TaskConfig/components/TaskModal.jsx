/* eslint-disable */
import { createRef, Fragment, useEffect, useRef, useState } from 'react';
import AuditCard from '@/components/AuditCard';
import TaskCardAbtest from './TaskCardAbtest';
import TaskCard from './TaskCard';
import { message, Space, Tabs, Spin, Drawer, Button } from 'antd';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { cloneDeep } from 'lodash';
import { debounce } from '@/utils/util.js';
import { getDmpTag } from '@/services/activityCommon.js';
import AuditInfoCard from '@/components/AuditInfoCard';

import {
  BetaSchemaForm,
  ProCard,
  ProForm,
  ProFormGroup,
  ProFormList,
  ProFormTextArea,
  ProFormSwitch,
  ProFormDigit,
  ProFormDependency
} from '@ant-design/pro-components';
import {
  getTaskConfig,
  getTaskDetail,
  saveTaskConfig,
} from '@/services/moneyBeanCenter';
import {
  validateAlphanumeric,
} from '@/utils/validate';

const taskArr = [
  {
    text: '付费任务',
    cpdTaskType: 'pay_app',
    configType: 'payAppConfig',
  },
  {
    text: '下载任务',
    cpdTaskType: 'app',
    configType: 'downloadAppConfig',
  },
  {
    text: '连续任务',
    cpdTaskType: 'continuous_app',
    configType: 'continuousAppConfig',
  },
  {
    text: '体验任务',
    cpdTaskType: 'promotional_app',
    configType: 'promotionalAppConfig',
  },
  {
    text: '视频任务',
    cpdTaskType: 'adVideo',
    configType: 'adVideoConfig',
  },
  {
    text: '注册任务',
    cpdTaskType: 'register_app',
    configType: 'registerAppConfig',
  },
  {
    text: '新人任务(不参与排序，无论如何移动，在C端都是固定位置)',
    cpdTaskType: 'newcomer_app',
    configType: 'newcomerAppConfig',
  },
];

const cpdTaskType2ConfigType = (cpdTaskType) => {
  const configType = taskArr.find(
    (task) => task.cpdTaskType === cpdTaskType,
  ).configType;
  return configType;
};

const configType2CpdTaskType = (configType) => {
  const cpdTaskType = taskArr.find(
    (task) => task.configType === configType,
  ).cpdTaskType;
  return cpdTaskType;
};

const TaskModal = ({ modalVisible, setVisible, detailParams, configType, isAudit, itemOperationLog, handleReload }) => {
  const baseFormRef = useRef();
  const defaultFormRef = useRef();
  const [loading, setLoading] = useState(true);
  const [checkedCase, setCheckedCase] = useState([
    {
      key: 'downloadAppConfig',
      value: false,
    },
    {
      key: 'continuousAppConfig',
      value: false,
    },
    {
      key: 'promotionalAppConfig',
      value: false,
    },
    {
      key: 'adVideoConfig',
      value: false,
    },
    {
      key: 'registerAppConfig',
      value: false,
    },
    {
      key: 'newcomerAppConfig',
      value: false,
    },
    {
      key: 'payAppConfig',
      value: false,
    }
  ]);
  const [tabList, setTabList] = useState([]);
  const [showTab, setShowTab] = useState(false);
  const [auditData, setAuditData] = useState();
  const [tagOptions, setTagOptions] = useState([]);

  const initData = () => {
    // 操作记录详情
    if (configType === 1) {
      return handleData(detailParams)
    }
    // 新增
    if (configType === 2) {
      const data = getTaskInitConfig()
      return handleData(data)
    }
    // 编辑 & 详情
    const func = configType === 3 ? getTaskDetail : getTaskConfig;
    func(detailParams).then((res) => {
      if (!res) return
      handleData(res.data)
    });
  };

  const handleData = (data) => {
    setAuditData(data);
    const {cpdConfigClassifyDTO, experimentConfigDTO, activityRuleUrl, cpdCommonTaskDTOList, foldSwitch, foldTaskNum } = data;
    let classifyName = '',
    classifyValue = '',
    classifyType = ''
    if (cpdConfigClassifyDTO) {
      classifyName = cpdConfigClassifyDTO.classifyName
      classifyValue = cpdConfigClassifyDTO.classifyValue
      classifyType = cpdConfigClassifyDTO.classifyType
      classifyType === 'dmpTag' && classifyValue && getDmpTagData({ tagId: Number(classifyValue) });
    }
    const {
      enable,
      experimentContent,
      experimentContentDTOS = [],
    } = experimentConfigDTO;

    const experimentConfig = {
      ...experimentConfigDTO,
      enable: enable.toString(),
      classifyName,
      classifyValue,
      classifyType,
    };
    if (experimentContent) {
      const checkedCaseArr = experimentContent.split(',');
      experimentConfig.experimentContent = checkedCaseArr;

      const temp = checkedCase.map((obj) => {
        return {
          key: obj.key,
          value: checkedCaseArr.includes(obj.key),
        };
      });
      setCheckedCase(temp);
    }
    baseFormRef.current?.setFieldsValue(experimentConfig);

    defaultFormRef.current?.setFieldsValue({
      activityRuleUrl,
      foldSwitch,
      foldTaskNum,
      cpdCommonTaskDTOList: cpdCommonTaskDTOList.map((item) => {
        let data = { ...item };
        if (data.cpdTaskType !== 'continuous_app') {
          data = {
            ...data,
            eachAppCashCardReward: (
              +item.eachAppCashCardReward / 100
            ).toFixed(2),
          };
        }
        if (data.cpdTaskType === 'newcomer_app') {
          data = {
            ...data,
            guideAppCashCardAmount: (
              +item.guideAppCashCardAmount / 100
            ).toFixed(2),
          };
        }
        if (data.cpdTaskType === 'continuous_app') {
          data['1'] = item.trialDurationConfigDTOList[0].trialDuration;
          data['2'] = item.trialDurationConfigDTOList[1].trialDuration;
        }
        if (['continuous_app', 'pay_app', 'register_app'].includes(data.cpdTaskType)) {
          // 处理钱豆、提现卡金额
          data.cpdRewardConfigDTOList = data.cpdRewardConfigDTOList?.map(
            (item) => {
              return {
                ...item,
                cashCardRewardAmount: (
                  +item.cashCardRewardAmount / 100
                ).toFixed(2),
              };
            },
          );
        }
        return data;
      }),
    });

    setShowTab(experimentConfig.enable === '1');

    const experimentTabList = experimentContentDTOS.map((exp, index) => {
      const temp = [];
      exp.downloadAppConfig &&
        temp.push({
          ...exp.downloadAppConfig,
          eachAppCashCardReward: (
            +exp.downloadAppConfig.eachAppCashCardReward / 100
          ).toFixed(2),
          cpdTaskType: 'app',
        });
      if (exp.continuousAppConfig) {
        let data = {
          ...exp.continuousAppConfig,
          cpdTaskType: 'continuous_app',
          1: exp.continuousAppConfig.trialDurationConfigDTOList[0]
            .trialDuration,
          2: exp.continuousAppConfig.trialDurationConfigDTOList[1]
            .trialDuration,
        };
        // 处理提现卡金额
        data.cpdRewardConfigDTOList = data.cpdRewardConfigDTOList?.map(
          (item) => {
            return {
              ...item,
              cashCardRewardAmount: (
                +item.cashCardRewardAmount / 100
              ).toFixed(2),
            };
          },
        );
        temp.push(data);
      }
      if (exp.payAppConfig) {
        let data = {
          ...exp.payAppConfig,
          cpdTaskType: 'pay_app',
        };
        // 处理提现卡金额
        data.cpdRewardConfigDTOList = data.cpdRewardConfigDTOList?.map(
          (item) => {
            return {
              ...item,
              cashCardRewardAmount: (
                +item.cashCardRewardAmount / 100
              ).toFixed(2),
            };
          },
        );
        temp.push(data);
      }
      exp.promotionalAppConfig &&
        temp.push({
          ...exp.promotionalAppConfig,
          cpdTaskType: 'promotional_app',
          eachAppCashCardReward: (
            +exp.promotionalAppConfig.eachAppCashCardReward / 100
          ).toFixed(2),
        });
      exp.adVideoConfig &&
        temp.push({
          ...exp.adVideoConfig,
          cpdTaskType: 'adVideo',
          eachAppCashCardReward: (
            +exp.adVideoConfig.eachAppCashCardReward / 100
          ).toFixed(2),
        });
      if (exp.registerAppConfig) {
        let data = {
          ...exp.registerAppConfig,
          cpdTaskType: 'register_app',
        };
        // 处理提现卡金额
        data.cpdRewardConfigDTOList = data.cpdRewardConfigDTOList?.map(
          (item) => {
            return {
              ...item,
              cashCardRewardAmount: (
                +item.cashCardRewardAmount / 100
              ).toFixed(2),
            };
          },
        );
        temp.push(data);
      }
      exp.newcomerAppConfig &&
        temp.push({
          ...exp.newcomerAppConfig,
          guideAppCashCardAmount: (
            +exp.newcomerAppConfig.guideAppCashCardAmount / 100
          ).toFixed(2),
          eachAppCashCardReward: (
            +exp.newcomerAppConfig.eachAppCashCardReward / 100
          ).toFixed(2),
        });
      return {
        key: index + 1,
        formRef: createRef(),
        actionRef: createRef(),
        initialValues: {
          activityRuleUrl: exp.activityRuleUrl,
          foldSwitch: exp.foldSwitch,
          foldTaskNum: exp.foldTaskNum || 0,
          expCardList: temp,
        },
      };
    });

    setTabList(experimentTabList);
    setLoading(false);
  }

  const getTaskInitConfig = () => {
    return {
      "id": null,
      "cpdConfigClassifyDTO": {
        "id": '',
        "plug": '',
        "classifyType": "fallback",
        "classifyValue": "",
        "classifyName": "",
        "target": ""
      },
      "experimentConfigDTO": {
        "enable": 0
      },
      "activityRuleUrl": "",
      "foldSwitch": false,
      "foldTaskNum": undefined,
      "cpdCommonTaskDTOList": [
        {
          "cpdTaskType": "pay_app",
          "guideText": "",
          "cpdRewardConfigDTOList": [
            {
                "stage": 1,
                "qdRewardAmount": null,
                "cashCardRewardAmount": null
            },
            {
                "stage": 2,
                "qdRewardAmount": null,
                "cashCardRewardAmount": null
            }
          ],
          "dailyLimit": null,
          "trialDuration": null,
          "taskSwitch":  true,
          "priority": null,
        },
        {
          "cpdTaskType": "promotional_app",
          "guideText": "",
          "eachAppReward": null,
          "eachAppCashCardReward": null,
          "dailyLimit": null,
          "trialDuration": null,
          "taskSwitch": true,
          "priority": null,
        },
        {
          "cpdTaskType": "continuous_app",
          "guideText": "",
          "eachAppReward": null,
          "eachAppCashCardReward": null,
          "cpdRewardConfigDTOList": [
              {
                  "stage": 1,
                  "qdRewardAmount": null,
                  "cashCardRewardAmount": null
              },
              {
                  "stage": 2,
                  "qdRewardAmount": null,
                  "cashCardRewardAmount": null
              }
          ],
          "dailyLimit": null,
          "trialDurationConfigDTOList": [
              {
                  "day": null,
                  "trialDuration": null,
              },
              {
                  "day": null,
                  "trialDuration": null,
              }
          ],
          "taskSwitch": true,
          "priority": null,
        },
        {
          "cpdTaskType": "adVideo",
          "guideText": "",
          "eachAppReward": null,
          "eachAppCashCardReward": null,
          "dailyLimit": null,
          "trialDuration": null,
          "taskSwitch": true,
          "priority": null,
        },
        {
          "cpdTaskType": "app",
          "guideText": "",
          "eachAppReward": null,
          "eachAppCashCardReward": null,
          "dailyLimit": null,
          "trialDuration": null,
          "taskSwitch": true,
          "priority": null,
        },
        {
          "cpdTaskType": "newcomer_app",
          "eachAppReward": null,
          "eachAppCashCardReward": null,
          "guideAppQdAmount": null,
          "guideAppCashCardAmount": null,
          "guideTaskTitle": "",
          "bubbleText": "",
          "taskSwitch": true,
          "priority": null,
          "subNewUserConfig.openSwitch": true,
        },
        {
          "cpdTaskType": "register_app",
          "guideText": "",
          "cpdRewardConfigDTOList": [
            {
                "stage": 1,
                "qdRewardAmount": null,
                "cashCardRewardAmount": null
            },
            {
                "stage": 2,
                "qdRewardAmount": null,
                "cashCardRewardAmount": null
            }
          ],
          "dailyLimit": null,
          "trialDuration": null,
          "taskSwitch":  true,
          "priority": null,
        }
      ]
    }
  }

  /**
 * 获取dmp详情
 */
  const getDmpTagData = async (params) => {
    if (!params.tagName && !params.tagId) return;
    const res = await getDmpTag(params);
    if (res?.code === '200') {
      const options = res.data.map((item) => ({
        value: item.id + '',
        label: item.tagIdWithName,
      }));
      setTagOptions(options);
    }
  };

  useEffect(() => {
    if (modalVisible) {
      initData();
    }
  }, [modalVisible]);

  const columns = [
    {
      title: '分层名称',
      dataIndex: 'classifyName',
      valueType: 'input',
      width: 'md',
      formItemProps: {
        extra: '最多输入20个字',
        rules: [
          {
            required: true,
            message: '请填写分层名称',
          },
          { validator: validateAlphanumeric(20) },
        ],
      },
    },
    {
      title: '目标人群',
      dataIndex: 'classifyType',
      key: 'classifyType',
      valueType: 'radio',
      fieldProps: (form) => {
        return {
          options: [
            { label: '兜底用户', value: 'fallback' },
            { label: '非兜底用户', value: 'dmpTag' },
          ],
          onChange: (e) => {
            form.setFieldValue('classifyValue', '');
          },
        };
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      valueType: 'dependency',
      name: ['classifyType'],
      columns: ({ classifyType }) => {
        return classifyType === 'dmpTag'
          ? [
              {
                title: '用户标签',
                dataIndex: 'classifyValue',
                key: 'classifyValue',
                width: 'md',
                valueType: 'select',
                fieldProps: (form) => {
                  return {
                    onSearch: debounce((value) => {
                      getDmpTagData({ tagName: value });
                    }, 500),
                    showSearch: true,
                    options: tagOptions,
                  };
              },
              formItemProps: {
                extra: 'DMP标签尽量配置IMEI维度',
                  rules: [
                    {
                      required: true,
                      message: '此项为必填项',
                    },
                  ],
                },
              },
            ]
          : [];
      },
    },
    {
      title: '是否abtest实验',
      dataIndex: 'enable',
      valueType: 'radio',
      valueEnum: {
        0: '否（全量默认以下配置）',
        1: '是（仅对照组为以下实验）',
      },
      formItemProps: {
        onChange: (value) => {
          setShowTab(value.target.defaultValue === '1');
        },
      },
    },
    {
      valueType: 'dependency',
      name: ['enable'],
      columns: ({ enable }) => {
        return enable === '1'
          ? [
              {
                valueType: 'group',
                columns: [
                  {
                    title: '实验code',
                    dataIndex: 'experimentCode',
                    valueType: 'input',
                    width: 'md',
                    formItemProps: {
                      rules: [
                        {
                          required: true,
                          message: '请填写实验code',
                        },
                      ],
                    },
                  },
                  {
                    title: '实验场景选择',
                    dataIndex: 'experimentContent',
                    valueType: 'checkbox',
                    fieldProps: {
                      labelInValue: true,
                      style: {
                        minWidth: 140,
                      },
                    },
                    formItemProps: {
                      onChange: (e) => {
                        const target = e.target.defaultValue;
                        const temp = checkedCase.map((obj, index) => {
                          let newVal = obj.value;
                          if (obj.key === target) {
                            newVal = !newVal;
                            if (newVal === true) {
                              // add todo
                              tabList.map((exp) => {
                                exp.actionRef.current.add(
                                  {
                                    cpdTaskType: taskArr.find(
                                      (task) => task.configType === target,
                                    ).cpdTaskType,
                                  },
                                  index,
                                );
                              });
                            } else {
                              // remove
                              tabList.map((exp) => {
                                const res =
                                  exp.formRef.current.getFieldsValue();
                                const i = res.expCardList.findIndex((a1) => {
                                  return (
                                    cpdTaskType2ConfigType(a1.cpdTaskType) ===
                                    target
                                  );
                                });
                                exp.actionRef.current.remove(i);
                              });
                            }
                          }
                          return {
                            key: obj.key,
                            value: newVal,
                          };
                        });
                        console.log(8989, temp);
                        setCheckedCase(temp);
                      },
                      rules: [
                        {
                          required: false,
                          message: '请选择实验场景',
                        },
                      ],
                    },
                    valueEnum: {
                      downloadAppConfig: {
                        text: '下载任务',
                      },
                      continuousAppConfig: {
                        text: '连续任务',
                      },
                      promotionalAppConfig: {
                        text: '体验任务',
                      },
                      adVideoConfig: {
                        text: '视频任务',
                      },
                      newcomerAppConfig: {
                        text: '新人任务',
                      },
                      registerAppConfig: {
                        text: '注册任务',
                      },
                      payAppConfig: {
                        text: '付费任务',
                      },
                    },
                  },
                ],
              },
            ]
          : [];
      },
    },
  ];

  const changePosition = (dragIndex, hoverIndex) => {
    // 交换位置
    const res = defaultFormRef.current.getFieldValue('cpdCommonTaskDTOList');
    const a = res[dragIndex],
      b = res[hoverIndex];

    res.splice(dragIndex, 1, b);
    res.splice(hoverIndex, 1, a);
    defaultFormRef.current.setFieldValue('cpdCommonTaskDTOList', res);
  };

  const onSubmit = async () => {
    const res = await Promise.all([
      baseFormRef.current.validateFields(),
      defaultFormRef.current.validateFields(),
    ]).catch((e) => {
      message.error('请完善必填配置');
      throw e;
    });
    const experimentRes = await Promise.all(
      tabList.map((tab) => {
        return tab.formRef.current.validateFields();
      }),
    ).catch((e) => {
      message.error('请完善必填配置');
      throw e;
    });
    let cpdConfigClassifyDTO = {},
      experimentConfigDTO = {},
      activityRuleUrl,
      foldSwitch,
      foldTaskNum,
      cpdCommonTaskDTOList = [];
    cpdConfigClassifyDTO.id = detailParams? auditData?.cpdConfigClassifyDTO?.id : '',
    cpdConfigClassifyDTO.classifyName = res[0].classifyName;
    cpdConfigClassifyDTO.classifyValue = res[0].classifyValue;
    experimentConfigDTO.enable = +res[0].enable;
    if (experimentConfigDTO.enable) {
      experimentConfigDTO = {
        ...experimentConfigDTO,
        experimentCode: res[0].experimentCode,
        experimentContent: res[0].experimentContent?.join(','),
        experimentContentDTOS: experimentRes.map((exp, index) => {
          const obj = {
            group: index + 1,
            activityRuleUrl: exp.activityRuleUrl,
            foldSwitch: exp.foldSwitch,
            foldTaskNum: exp.foldTaskNum || 0,
          };
          exp.expCardList.map((a) => {
            let temp = { ...a };
            if (temp.cpdTaskType !== 'continuous_app' && temp.cpdTaskType !== 'pay_app' && temp.cpdTaskType !== 'register_app') {
              temp = {
                ...temp,
                eachAppCashCardReward: +a.eachAppCashCardReward * 100,
              };
            }
            if (temp.cpdTaskType === 'newcomer_app') {
              temp = {
                ...temp,
                guideAppCashCardAmount: +a.guideAppCashCardAmount * 100,
                subNewUserConfig: !a?.subNewUserConfig?.openSwitch ? {
                  countDownHours: "",
                  endDays: "",
                  truestartDays: "",
                  taskRate: "",
                  openSwitch: false
                } : a?.subNewUserConfig
              };
            }
            if (temp.cpdTaskType === 'continuous_app') {
              temp.trialDurationConfigDTOList = [
                {
                  day: 1,
                  trialDuration: temp['1'],
                },
                {
                  day: 2,
                  trialDuration: temp['2'],
                },
              ];
            }
            if (['continuous_app', 'pay_app', 'register_app'].includes(temp.cpdTaskType)) {
              // 处理钱豆、提现卡金额
              temp.cpdRewardConfigDTOList = a.cpdRewardConfigDTOList?.map(
                (item) => {
                  return {
                    ...item,
                    cashCardRewardAmount: item.cashCardRewardAmount * 100,
                  };
                },
              );
            }
            obj[
              taskArr.find(
                (task) => task.cpdTaskType === a.cpdTaskType,
              ).configType
            ] = temp;
          });
          return obj;
        }),
      };
    }
    activityRuleUrl = res[1].activityRuleUrl;
    foldSwitch = res[1].foldSwitch || false;
    foldTaskNum = res[1].foldTaskNum || 0;
    cpdCommonTaskDTOList = res[1].cpdCommonTaskDTOList.map((item, index) => {
      let data = { ...item };

      if (data.cpdTaskType !== 'continuous_app') {
        data = {
          ...data,
          eachAppCashCardReward: +item.eachAppCashCardReward * 100,
        };
      }
      if (data.cpdTaskType === 'newcomer_app') {
        data = {
          ...data,
          guideAppCashCardAmount: +item.guideAppCashCardAmount * 100,
          subNewUserConfig: !item?.subNewUserConfig?.openSwitch ? {
            countDownHours: "",
            endDays: "",
            truestartDays: "",
            taskRate: "",
            openSwitch: false
          } : item?.subNewUserConfig
        };
      }
      if (['continuous_app', 'pay_app', 'register_app'].includes(item.cpdTaskType)) {
        data.cpdRewardConfigDTOList = data.cpdRewardConfigDTOList?.map(
          (item) => {
            return {
              ...item,
              cashCardRewardAmount: item.cashCardRewardAmount * 100,
            };
          },
        );
      }
      if (item.cpdTaskType === 'continuous_app') {
        data.trialDurationConfigDTOList = [
          {
            day: 1,
            trialDuration: item['1'],
          },
          {
            day: 2,
            trialDuration: item['2'],
          },
        ];
      }
      return data;
    });

    const submitRes = await saveTaskConfig({
      id: detailParams? auditData.id : '',
      cpdConfigClassifyDTO,
      experimentConfigDTO,
      activityRuleUrl,
      foldSwitch,
      foldTaskNum,
      cpdCommonTaskDTOList,
    });
    if (submitRes && submitRes.code === '200') {
      message.success('提交成功');
      setVisible(false)
      // 刷新列表
      handleReload && handleReload();
    }
  };

  const onTabEditHandle = (targetKey, type) => {
    const experimentContentLength = (baseFormRef.current.getFieldValue('experimentContent') || []).length
    const list = cloneDeep(tabList);
    if (type === 'add') {
      baseFormRef.current.validateFields().then((res) => {
        list.push({
          key: tabList.length === 0 ? 1 : tabList[tabList.length - 1].key + 1,
          formRef: createRef(),
          actionRef: createRef(),
          initialValues: {
            foldSwitch: false,
            expCardList: experimentContentLength ? checkedCase
              .filter((a) => a.value)
              .map((b) => {
                return {
                  cpdTaskType: taskArr.find((task) => task.configType === b.key)
                    .cpdTaskType,
                };
              })
              : [],
          },
        });
        setTabList(list);
      });
    } else {
      const newPanes = list.filter((item, index) => item.key !== +targetKey);
      setTabList(newPanes);
    }
  };

  return (
    <Drawer
      destroyOnClose
      title={configType === 1? '操作详情' : '用户分层配置'}
      width={1500}
      open={modalVisible}
      onClose={() => {
        setVisible(false);
        setTabList([])
      }}
      footer={
        !isAudit &&
        <Space>
          <Button
            onClick={() => {
                setVisible(false)
            }}
          >
            取消
          </Button>
          <Button
            type="primary"
            onClick={() => {
              onSubmit()
            }}
          >
            确认
          </Button>
        </Space>
      }
    >
      <Spin spinning={loading}>
        <Space direction="vertical">
          <ProCard
            direction="column"
            style={{ width: '1500px' }}
          >
            <ProCard>
              <BetaSchemaForm
                columns={columns}
                formRef={baseFormRef}
                submitter={false}
                readonly={isAudit}
              />
            </ProCard>
            <Tabs
              defaultActiveKey={0}
              type="editable-card"
              hideAdd={!showTab || isAudit}
              addIcon={<>添加实验组➕</>}
              onEdit={onTabEditHandle}
            >
              <Tabs.TabPane
                tab="对照组-线上"
                key={0}
                forceRender={true}
                closable={false}
              >
                <div style={{ marginBottom: '10px' }}>
                  对照组：默认为线上兜底配置
                </div>
                <ProForm
                  formRef={defaultFormRef}
                  submitter={false}
                  readonly={isAudit}
                >
                  <ProFormTextArea
                    colProps={{ span: 24 }}
                    name="activityRuleUrl"
                    label="活动规则详情"
                    placeholder="请输入悟空页面dp链接"
                    rules={[
                      {
                        required: true,
                        message: '请填写活动规则详情',
                      },
                    ]}
                  />
                  <ProForm.Group size={20}>
                    <ProFormSwitch
                      name="foldSwitch"
                      label="任务列表是否折叠"
                      tooltip="针对所有任务列表生效"
                      rules={[
                        {
                          required: false,
                          message: '必填',
                        },
                      ]}
                    />

                    <ProFormDependency name={['foldSwitch']}>
                      {({ foldSwitch }) => {
                        if (foldSwitch) {
                          return (
                            <ProFormDigit
                              name="foldTaskNum"
                              label="外显任务数量(3-8个)"
                              fieldProps={{ precision: 0 }}
                              rules={[
                                {
                                  required: true,
                                  message: '请填写数量',
                                },
                                { validator: (rule, value, callback) => {
                                    if (value && (Number.isNaN(value) || value < 3 || value.toString().includes('.') || value > 8)) {
                                      callback('仅支持输入3-8之间的整数值');
                                    } else {
                                      callback();
                                    }
                                  }
                                }
                              ]}
                            />
                          )
                        }
                        return null;
                      }}
                    </ProFormDependency>
                  </ProForm.Group>
                  <ProCard>
                    <ProFormList
                      alwaysShowItemLabel
                      min={1}
                      name="cpdCommonTaskDTOList"
                      deleteIconProps={false}
                      copyIconProps={false}
                      creatorButtonProps={false}
                      itemRender={(doms, listMeta) => {
                        const { index, record: item } = listMeta;
                        return (
                          <ProFormGroup key="group">
                            <DndProvider backend={HTML5Backend}>
                              <Space direction="vertical">
                                <ProCard colSpan={24} layout="left" key={index}>
                                  <TaskCard
                                    text={
                                      taskArr.find(
                                        (task) =>
                                          task.cpdTaskType === item.cpdTaskType,
                                      ).text
                                    }
                                    index={index}
                                    changePosition={changePosition}
                                    key={item.cpdTaskType}
                                    type={
                                      taskArr.find(
                                        (task) =>
                                          task.cpdTaskType === item.cpdTaskType,
                                      ).configType
                                    }
                                  />
                                </ProCard>
                              </Space>
                            </DndProvider>
                          </ProFormGroup>
                        );
                      }}
                    />
                  </ProCard>
                </ProForm>
              </Tabs.TabPane>

              {tabList.map((item, index) => {
                return (
                  <Tabs.TabPane
                    tab={'实验' + item.key}
                    key={item.key}
                    forceRender={true}
                    closable={!isAudit}
                  >
                    <ProForm
                      formRef={item.formRef}
                      submitter={false}
                      initialValues={item.initialValues}
                      readonly={isAudit}
                    >
                      <ProFormTextArea
                        colProps={{ span: 24 }}
                        name="activityRuleUrl"
                        label="活动规则详情"
                        placeholder="请输入悟空页面dp链接"
                        rules={[
                          {
                            required: true,
                            message: '请填写活动规则详情',
                          },
                        ]}
                      />
                      <ProForm.Group size={20}>
                        <ProFormSwitch
                          name="foldSwitch"
                          label="任务列表是否折叠"
                          tooltip="针对所有任务列表生效"
                          rules={[
                            {
                              required: false,
                              message: '必填',
                            },
                          ]}
                        />
                        <ProFormDependency name={['foldSwitch']}>
                          {({ foldSwitch }) => {
                            if (foldSwitch) {
                              return (
                                <ProFormDigit
                                  name="foldTaskNum"
                                  label="外显任务数量(3-8个)"
                                  fieldProps={{ precision: 0 }}
                                  rules={[
                                    {
                                      required: true,
                                      message: '请填写数量',
                                    },
                                    { validator: (rule, value, callback) => {
                                        if (value && (Number.isNaN(value) || value < 3 || value.toString().includes('.') || value > 8)) {
                                          callback('仅支持输入3-8之间的整数值');
                                        } else {
                                          callback();
                                        }
                                      }
                                    }
                                  ]}
                                />
                              )
                            }
                            return null;
                          }}
                        </ProFormDependency>
                      </ProForm.Group>
                      <Space direction="vertical">
                        <ProFormList
                          name="expCardList"
                          actionRef={item.actionRef}
                          deleteIconProps={false}
                          copyIconProps={false}
                          creatorButtonProps={false}
                          itemRender={(doms, listMeta) => {
                            console.log(1212, listMeta);
                            const { index: jndex } = listMeta;
                            return (
                              <ProFormGroup key="group">
                                <ProCard colSpan={24} layout="left" key={jndex}>
                                  <TaskCardAbtest
                                    key={jndex}
                                    showItem={
                                      taskArr.find(
                                        (task) =>
                                          task.cpdTaskType ===
                                          listMeta.record.cpdTaskType,
                                      ).configType
                                    }
                                  />
                                </ProCard>
                              </ProFormGroup>
                            );
                          }}
                        />
                      </Space>
                    </ProForm>
                  </Tabs.TabPane>
                );
              })}
            </Tabs>
          </ProCard>
        </Space>
      </Spin>
      {configType === 1 && <AuditInfoCard itemOperationLog={itemOperationLog} />}
    </Drawer>
  );
};

export default TaskModal;
