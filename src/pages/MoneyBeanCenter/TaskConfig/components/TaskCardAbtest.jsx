import { Button, Space } from 'antd';
import { BetaSchemaForm, ProCard } from '@ant-design/pro-components';
import { Fragment, useRef, forwardRef } from 'react';
import { getTitle, getFormItemsByShowList } from './config';
import BraftEditor from 'braft-editor';
import 'braft-editor/dist/index.css';
import './BraftEditor.css';

const DragItem = forwardRef(({ showItem }, formRef) => {
  const cardList = getFormItemsByShowList(showItem);
    // const now = new Date();
    // const formattedDate = now.toISOString().slice(0, 19).replace('T', ' ');
    // console.log(formattedDate, 'taskCardAbtest', 'showItem', showItem, 'cardList', cardList)
    const columns = cardList;
  // test
  const onFill = () => {
    formRef?.current?.setFieldsValue({
      zz: 1,
      oo: 2,
      pp: 9,
      ww: BraftEditor.createEditorState('<p>123123</p>'),
    });
  };
  return (
    <div>
      <ProCard title={getTitle(showItem)} boxShadow>
        <BetaSchemaForm
          formRef={formRef}
          autoFocusFirstInput={false}
          layoutType="Embed"
          columns={columns}
          submitter={{
            resetButtonProps: {
              style: {
                display: 'none',
              },
            },
            submitButtonProps: {
              style: {
                display: 'none',
              },
            },
          }}
        />
      </ProCard>
    </div>
  );
});

export default DragItem;
