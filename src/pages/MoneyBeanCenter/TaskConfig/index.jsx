import React, { Fragment, useEffect, useState } from 'react';
import AuditCard from '@/components/AuditCard';
import {
  message, Space, Spin, Button, List, Avatar, Switch,
} from 'antd';
import {
  ProCard,
} from '@ant-design/pro-components';
import {
  getTaskConfigList,
  submitTaskConfig,
  saveTaskConfig,
} from '@/services/moneyBeanCenter';
import TaskModal from './components/TaskModal';

const TaskConfig = ({ isAudit }) => {
  const [loading, setLoading] = useState(true);
  const [classifyList, setClassifyList] = useState();
  const [taskModalVisible, setTaskModalVisible] = useState(false);
  const [configType, setConfigType] = useState();
  const [selectedData, setSelectedData] = useState({});
  const [auditData, setAuditData] = useState();

  const initData = () => {
    getTaskConfigList().then((res) => {
      if (!res) return;
      setAuditData(res.data);
      setLoading(false);
      const list = res.data?.cpdConfigClassifyDTOList || [];
      if (list && list.length && isAudit) {
        const v = list.filter((e) => e.auditStatus === 1);
        return setClassifyList(v);
      }
      setClassifyList(list);
    });
  };

  useEffect(() => {
    initData();
  }, []);

  const onEdit = (value, params) => {
    setSelectedData(params);
    setTaskModalVisible(true);
    setConfigType(value);
  };
  const handleSwitchChange = async (value, item) => {
    setClassifyList((prevList) => prevList.map((e) => {
      if (e.id === item.id) {
        return { ...e, plug: value ? 1 : 0 };
      }
      return e;
    }));
    item.plug = value ? 1 : 0;
    const submitRes = await saveTaskConfig({
      cpdConfigClassifyDTO: item,
    });
    if (submitRes && submitRes.code === '200') {
      message.success('操作成功');
    }
  };
  const actionsToShow = (item) => {
    const action1 = <a key="list-detail" onClick={() => { onEdit(3, item); }}>详情</a>;
    const action2 = <a key="list-edit" onClick={() => { onEdit(4, item); }}>编辑</a>;
    const action3 = <Switch disabled={isAudit || Number(item.classifyValue) === 1} value={item.plug} key="list-switch" onChange={(checked) => handleSwitchChange(checked, item)} />;
    const actions = isAudit ? [action1, action3] : [action1, action2, action3];
    return actions;
  };

  const onSubmit = async () => {
    const params = classifyList.map((e) => e.id);
    const submitRes = await submitTaskConfig(params);
    if (submitRes && submitRes.code === '200') {
      initData();
      message.success('提交成功');
      // handleReload()
    }
  };

  return (
    <>
      <Spin spinning={loading}>
        <Space direction="vertical">
          {!isAudit && (
            <AuditCard title="任务配置审核" onSubmit={onSubmit} type="MoneyBeanCenter" scene="task" auditData={auditData} />
          )}
          {!isAudit && (
            <ProCard>
              <Button type="primary" onClick={() => { onEdit(2); }}>新增用户分层配置</Button>
            </ProCard>
          )}
          <ProCard
            title="钱豆任务配置"
            direction="column"
            style={isAudit ? { width: '70vw' } : {}}
            // style={{ width: '850px' }}
          >
            <List
              itemLayout="horizontal"
              dataSource={classifyList}
              renderItem={(item, index) => (
                <List.Item
                  actions={actionsToShow(item)}
                >
                  <List.Item.Meta
                    avatar={<Avatar src={`https://api.dicebear.com/7.x/miniavs/svg?seed=${index}`} />}
                    title={`分层名称：${item.classifyName}`}
                    description={`目标人群：${item.classifyTagName}`}
                  />
                </List.Item>
              )}
            />
          </ProCard>
        </Space>
      </Spin>
      <TaskModal
        modalVisible={taskModalVisible}
        setVisible={setTaskModalVisible}
        detailParams={selectedData}
        configType={configType}
        isAudit={Number(configType) === 3}
        handleReload={initData}
      />
    </>
  );
};

export default TaskConfig;
