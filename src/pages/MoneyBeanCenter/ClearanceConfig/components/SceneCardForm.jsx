import React, { forwardRef, useState } from 'react';
import { BetaSchemaForm, ProCard } from '@ant-design/pro-components';
import HotGoodsExp from './HotGoodsExp';
import Coupon from './Coupon';
import { getQuotaId } from '@/services/moneyBeanCenter';
import { GIFT_URL_ENUM } from '../../constant';
import { validatePositiveInteger } from '@/utils/validate';

const SceneCardForm = forwardRef(
  ({ title, index, isReadOnly, isAudit, isHot, layoutType }, formRef) => {
    const [rewardLogo, setRewardLogo] = useState('');
    const [displayTitle, setDisplayTitle] = useState('');
    const [consumeQdAmount, setConsumeQdAmount] = useState('');
    const [buttonName, setButtonName] = useState('');
    const [rewardName, setRewardName] = useState('');
    const [isAdPosition, setIsAdPosition] = useState(false);

    // 远程获取库存id
    const quotaIdRequest = async (param) => {
      const { keyWords, rewardType } = param;
      if (keyWords && rewardType) {
        const { data = [] } = await getQuotaId({
          type: rewardType,
          quotaIdOrName: keyWords,
        });
        return data.map((item) => {
          return {
            label: item.id,
            value: item.id,
          };
        });
      } else {
        return [];
      }
    };

    const rewardLogoCol = [
      {
        valueType: 'dependency',
        name: ['isAdPosition'],
        columns: ({ isAdPosition }) => {
          return [
            {
              valueType: 'group',
              columns: [
                {
                  title: '奖品图',
                  readonly: isReadOnly,
                  dataIndex: 'rewardLogo',
                  valueType: 'inputUpload',
                  formItemProps: {
                    rules: [
                      {
                        required: true,
                        message: '此项为必填项',
                      },
                    ],
                  },
                  fieldProps: {
                    readonly: isAudit,
                    limit: {
                      width: isAdPosition === 'true' ? 468 : 444,
                      height:
                        isAdPosition === 'true' ? (isHot ? 843 : 651) : 444,
                    },
                    appKey: 'finance-operation-admin.vmic.xyz',
                    appName: 'finance-operation-admin',
                  },
                },
                {
                  valueType: 'dependency',
                  name: [
                    'rewardLogo',
                    'displayTitle',
                    'consumeQdAmount',
                    'buttonName',
                    'rewardName',
                  ],
                  columns: ({
                    rewardLogo,
                    displayTitle,
                    consumeQdAmount,
                    buttonName,
                    rewardName,
                  }) => {
                    setRewardLogo(rewardLogo);
                    setDisplayTitle(displayTitle);
                    setConsumeQdAmount(consumeQdAmount);
                    setButtonName(buttonName);
                    setRewardName(rewardName);
                    return [];
                  },
                },
              ],
            },
          ];
        },
      },
    ];

    const cardTitle = isHot
      ? [
          {
            title: '卡片标题',
            readonly: isReadOnly,
            dataIndex: 'displayTitle',
            width: 'sm',
            fieldProps: {
              maxLength: 15,
              showCount: true,
            },
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
              ],
            },
          },
        ]
      : [];
    const exchangeCol = [
      {
        title: '奖励类型',
        dataIndex: 'rewardType',
        valueType: 'select',
        width: 'sm',
        readonly: isReadOnly,
        fieldProps: (form) => {
          return {
            onChange: () => {
              if (isHot) {
                form.setFieldValue('quotaId', undefined);
              } else {
                form.setFieldValue(
                  ['exchangeGoodsList', index, 'quotaId'],
                  undefined,
                );
              }
            },
          };
        },
        formItemProps: {
          rules: [
            {
              required: true,
              message: '此项为必填项',
            },
          ],
        },
        valueEnum: {
          payCoupon: {
            text: '支付券',
          },
          redPacketCoupon: {
            text: '返现券',
          },
          coupon: {
            text: '贷款券',
          },
          outerRedeem: {
            text: '会员兑换码',
          },
          points: {
            text: '积分',
          },
          inKind: {
            text: '实物奖品',
          },
        },
      },
      {
        valueType: 'dependency',
        name: ['rewardType'],
        columns: ({ rewardType }) => {
          let col = [];
          if (rewardType === 'redPacketCoupon') {
            col = [
              {
                title: '券id',
                dataIndex: 'templateId',
                width: 'sm',
                readonly: isReadOnly,
                formItemProps: {
                  rules: [
                    {
                      required: true,
                      message: '此项为必填项',
                    },
                  ],
                },
              },
            ];
          } else if (rewardType === 'points') {
            col = [
              {
                title: '积分数量',
                readonly: isReadOnly,
                dataIndex: 'quota',
                valueType: 'digit',
                width: 'sm',
                formItemProps: {
                  rules: [
                    {
                      required: true,
                      message: '此项为必填项',
                    },
                  ],
                },
              },
            ];
          }
          if (['payCoupon', 'redPacketCoupon', 'coupon'].includes(rewardType)) {
            col.push({
              valueType: 'select',
              title: '我的礼品跳转链接',
              dataIndex: 'myGiftRedirectUrl',
              valueEnum: GIFT_URL_ENUM,
              width: 'md',
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: '此项为必填项',
                  },
                ],
              },

              fieldProps: (form) => {
                return {
                  mode: 'tags',
                  onSelect: (val) => {
                    if (isHot) {
                      console.log();
                      form.setFieldValue('myGiftRedirectUrl', [val]);
                    } else {
                      form.setFieldValue(
                        ['exchangeGoodsList', index, 'myGiftRedirectUrl'],
                        [val],
                      );
                    }
                  },
                };
              },
            });
          }

          return [
            {
              valueType: 'group',
              columns: [
                ...col,
                {
                  title: '库存id',
                  dataIndex: 'quotaId',
                  valueType: 'select',
                  width: 'sm',
                  readonly: isReadOnly,
                  request: quotaIdRequest,
                  params: { rewardType },
                  fieldProps: {
                    showSearch: true,
                  },
                  formItemProps: {
                    extra: '除返现券外，不同产品请勿关联同一个库存id',
                    rules: [
                      {
                        required: true,
                        message: '此项为必填项',
                      },
                    ],
                  },
                },
              ],
            },
          ];
        },
      },
      {
        title: '单日可兑数量上限',
        readonly: isReadOnly,
        dataIndex: 'perDayLimitCount',
        valueType: 'digit',
        width: 'sm',
        formItemProps: {
          rules: [
            {
              required: true,
              message: '此项为必填项',
            },
            { validator: validatePositiveInteger },
          ],
        },
      },
      {
        title: '消耗钱豆数',
        readonly: isReadOnly,
        dataIndex: 'consumeQdAmount',
        width: 'sm',
        formItemProps: {
          rules: [
            {
              required: true,
              message: '此项为必填项',
            },
            { validator: validatePositiveInteger },
          ],
        },
      },
      ...cardTitle,
      {
        title: '按钮名称',
        readonly: isReadOnly,
        dataIndex: 'buttonName',
        width: 'sm',
        fieldProps: {
          maxLength: 10,
          showCount: true,
        },
        formItemProps: {
          rules: [
            {
              required: true,
              message: '此项为必填项',
            },
          ],
        },
      },
      {
        title: '奖品名称',
        readonly: isReadOnly,
        dataIndex: 'rewardName',
        width: 'sm',
        fieldProps: {
          maxLength: 15,
          showCount: true,
        },
        formItemProps: {
          rules: [
            {
              required: true,
              message: '此项为必填项',
            },
          ],
        },
      },
      ...rewardLogoCol,
      {
        title: '奖品规则弹窗',
        readonly: isReadOnly,
        dataIndex: 'briefDesc',
        width: 'sm',
        valueType: 'textarea',
        formItemProps: {
          rules: [
            {
              required: true,
              message: '此项为必填项',
            },
          ],
        },
      },
    ];
    const bannerCol = [
      ...rewardLogoCol,
      {
        title: '跳转链接',
        readonly: isReadOnly,
        dataIndex: 'adPositionRedirectUrl',
        width: 'sm',
        formItemProps: {
          rules: [
            {
              required: true,
              message: '此项为必填项',
            },
          ],
        },
      },
    ];
    const isTopCol = [
      {
        valueType: 'group',
        columns: [
          {
            title: '是否展示',
            readonly: isReadOnly,
            dataIndex: 'isTop',
            valueType: 'radio',
            initialValue: 'false',
            width: 'sm',
            valueEnum: {
              false: '否',
              true: '是（展示在瀑布流首张卡片位）',
            },
          },
        ],
      },
    ];
    const commonCol = [
      {
        title: '应用场景',
        readonly: isReadOnly,
        dataIndex: 'isAdPosition',
        valueType: 'radio',
        initialValue: 'false',
        width: 'sm',
        formItemProps: {
          style: {
            color: 'red',
          },
        },
        valueEnum: {
          false: '兑换奖励',
          true: '广告位',
        },
      },
      {
        valueType: 'dependency',
        name: ['isAdPosition'],
        columns: ({ isAdPosition }) => {
          setIsAdPosition(isAdPosition);
          return isAdPosition === 'true'
            ? [
                {
                  valueType: 'group',
                  columns: bannerCol,
                },
              ]
            : [
                {
                  valueType: 'group',
                  columns: exchangeCol,
                },
              ];
        },
      },
    ];

    const columns = isHot ? [...isTopCol, ...commonCol] : commonCol;

    return (
      <ProCard>
        <ProCard layout="left">
          <BetaSchemaForm
            layoutType={layoutType}
            formRef={formRef}
            columns={columns}
            submitter={false}
            readonly={isAudit}
          />
        </ProCard>

        <ProCard colSpan="200px" style={{ background: '#f4f4f4' }}>
          {isAdPosition !== 'true' && isHot && (
            <HotGoodsExp
              rewardLogo={rewardLogo}
              displayTitle={displayTitle}
              consumeQdAmount={consumeQdAmount}
              buttonName={buttonName}
              rewardName={rewardName}
            />
          )}
          {(isAdPosition === 'true' || !isHot) && (
            <Coupon
              rewardLogo={rewardLogo}
              displayTitle={displayTitle}
              consumeQdAmount={consumeQdAmount}
              buttonName={buttonName}
              rewardName={rewardName}
              type={isAdPosition === 'true' ? 'banner' : 'coupon'}
            />
          )}
        </ProCard>
      </ProCard>
    );
  },
);

export default SceneCardForm;
