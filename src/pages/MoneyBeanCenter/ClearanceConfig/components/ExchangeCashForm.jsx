import React, { forwardRef } from 'react';
import {
  ProForm,
  ProFormList,
  ProFormGroup,
  ProCard,
  ProFormDigit,
  BetaSchemaForm,
} from '@ant-design/pro-components';
import { CloseCircleOutlined } from '@ant-design/icons';
import {
  validateMoneyNotNegativeInteger,
  validatePositiveInteger,
} from '@/utils/validate';

const ExchangeCashForm = forwardRef(({ isAudit }, formRef) => {
  return (
    <ProCard title="兑换现金红包配置"  style={{ width: '100%' }}>
      <ProForm formRef={formRef} submitter={false} readonly={isAudit}>
        <ProFormDigit
          width="md"
          name="cashCouponPerPersonLimit"
          label="单人单日可兑上限次数"
          formItemProps={{
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
              { validator: validatePositiveInteger },
            ],
          }}
        />
        <ProFormList
          alwaysShowItemLabel
          min={1}
          name="cashCouponNominalAmountList"
          deleteIconProps={{ Icon: CloseCircleOutlined }}
          copyIconProps={false}
          itemRender={(doms, listMeta) => {
            const { index } = listMeta;
            let label1 = `可兑换红包面额 ${index + 1}`;
            index === 0 && (label1 += '（仅限首次）');
            return (
              <ProFormGroup key="group">
                <BetaSchemaForm
                  layoutType={'Embed'}
                  columns={[
                    {
                      valueType: 'group',
                      columns: [
                        {
                          title: label1,
                          dataIndex: 'nominalAmount',
                          valueType: 'money',
                          width: 200,
                          fieldProps: {
                            suffix: '元',
                          },
                          formItemProps: {
                            rules: [
                              {
                                required: true,
                                message: '此项为必填项',
                              },
                              { validator: validateMoneyNotNegativeInteger },
                            ],
                          },
                        },
                        {
                          title: '单日可兑上限次数',
                          width: 200,
                          dataIndex: 'perDayLimitCount',
                          valueType: 'input',
                          formItemProps: {
                            rules: [
                              {
                                required: true,
                                message: '此项为必填项',
                              },
                              { validator: validatePositiveInteger },
                            ],
                          },
                        },
                      ],
                    },
                  ]}
                  submitter={false}
                />
                {index !== 0 && (
                  <div style={{ marginTop: '30px', marginLeft: '-20px' }}>
                    {doms.action}
                  </div>
                )}
              </ProFormGroup>
            );
          }}
        />
      </ProForm>
    </ProCard>
  );
});

export default ExchangeCashForm;
