import React, { forwardRef } from 'react';
import { ProCard, ProForm, ProFormList } from '@ant-design/pro-components';
import SceneCardForm from './SceneCardForm';
import HotGoodsExp from './HotGoodsExp';

const HotGoodsForm = forwardRef(({ isAudit }, formRef) => {
  return (
    <ProCard title="爆款商品配置">
      <ProCard>
        <SceneCardForm isAudit={isAudit} ref={formRef} isHot={true} />
      </ProCard>
    </ProCard>
  );
});

export default HotGoodsForm;
