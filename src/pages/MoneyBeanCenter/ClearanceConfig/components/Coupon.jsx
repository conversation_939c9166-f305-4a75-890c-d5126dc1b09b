import React, { useMemo } from 'react';
import './Coupon.less';

const Coupon = ({
  type,
  rewardLogo,
  displayTitle,
  consumeQdAmount,
  buttonName,
  rewardName,
}) => {
  if (type === 'coupon') {
    return (
      <>
        <div className="coupon-container">
          <img className="coupon-goods-img" src={rewardLogo} alt="" />
          <div className="title-content">
            <div className="title">
              {rewardName}
              <p className="title-tag">仅剩10张</p>
            </div>
          </div>
          <div className="change-content">
            <div>
              {consumeQdAmount}{' '}
              <img
                className="img-bean"
                src="https://zhanstatic.vivo.com.cn/wukong-zhan/img/edfc925c-b87e-4159-87d7-e881184cc70bnwebp_compress.png"
                alt=""
              />
            </div>
            <span className="btn">{buttonName}</span>
          </div>
        </div>
      </>
    );
  } else {
    return (
      <>
        <div className="banner">
          <img className="banner-img" src={rewardLogo} alt="" />
        </div>
      </>
    );
  }
};

export default Coupon;
