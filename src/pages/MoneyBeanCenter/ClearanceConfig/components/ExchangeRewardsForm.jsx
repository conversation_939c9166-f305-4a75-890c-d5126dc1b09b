import { ProCard } from '@ant-design/pro-components';
import SceneCardForm from './SceneCardForm';
import { useDrop, useDrag } from 'react-dnd';
import React, { useRef, forwardRef } from 'react';
import Coupon from './Coupon';
import { DragOutlined } from '@ant-design/icons';

const DragItem = forwardRef(
  ({ id, index, isAudit, changePosition, addItem, delItem, list }, formRef) => {
    const ref = useRef(null);

    const [, drop] = useDrop({
      accept: 'DragDropBox', // 只对useDrag的type的值为DragDropBox时才做出反应
      drop: (item, monitor) => {
        const dragIndex = item.index;
        const hoverIndex = index;
        if (dragIndex === hoverIndex) return; // 如果回到自己的坑，那就什么都不做
        changePosition(dragIndex, hoverIndex); // 调用传入的方法完成交换
      },
    });

    const [{ isDragging }, drag] = useDrag(() => ({
      type: 'DragDropBox',
      item: {
        id,
        index,
      },
      collect: (monitor) => ({
        isDragging: monitor.isDragging(), // css样式需要
      }),
    }));

    return (
      <div ref={drag(drop(ref))} style={{ opacity: isDragging ? 0.5 : 1 }}>
        <ProCard title={`配置#${index + 1}`} style={{ marginBlockStart: 16 }}>
          <ProCard title={<DragOutlined />} boxShadow>
            <ProCard layout="left" colSpan={'1200px'}>
              <SceneCardForm
                isAudit={isAudit}
                index={index}
                layoutType={'Embed'}
              />
            </ProCard>
          </ProCard>
        </ProCard>
      </div>
    );
  },
);

export default DragItem;
