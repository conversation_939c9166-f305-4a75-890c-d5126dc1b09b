.coupon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 156px;
  height: 230px;
  padding: 4px 4px 8px 4px;
  background: #fff;
  border-radius: 10px;

  .coupon-goods-img {
    width: 148px;
    height: 148px;
    background: #fff;
  }

  .title-content {
    display: flex;
    align-items: center;
    margin-top: 6px;

    .title {
      color: #000;
      font-weight: 500;
      font-size: 13px;
      font-family: vivo Sans SC VF;
    }

    .title-tag {
      display: inline-flex;
      align-items: flex-start;
      margin-left: 4px;
      padding: 3px 4px;
      font-size: 9px;
      line-height: 9px;
      background: #ffe9e3;
      border-radius: 3px;
    }
  }

  .change-content {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-top: 8px;
    color: #ff8640;
    font-weight: 600;
    font-size: 15px;
    font-family: vivo Sans SC VF;

    .img-bean {
      width: 13px;
      height: 13px;
    }

    .btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 50px;
      height: 24px;
      color: #fff;
      font-size: 12px;
      background: #fe9331;
      border-radius: 14px;
    }
  }
}

.banner {
  width: 148px;
  height: 148px;
  padding: 10px;
  background: #fff1dc;
  .banner-img {
    width: 128px;
    height: 128px;
    background: red;
  }
}
