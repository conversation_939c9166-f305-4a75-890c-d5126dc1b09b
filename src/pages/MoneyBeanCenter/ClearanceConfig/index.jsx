import React, { useEffect, useRef, useState } from 'react';
import AuditCard from '@/components/AuditCard';
import ExchangeCashForm from './components/ExchangeCashForm';
import HotGoodsForm from './components/HotGoodsForm';
import ExchangeRewardsForm from './components/ExchangeRewardsForm';
import { message, Space, Spin } from 'antd';
import { PlusCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import {
  ProCard,
  ProFormList,
  ProFormGroup,
  ProForm,
} from '@ant-design/pro-components';
import { ConfigProvider } from 'finance-busi-components-toB';
import {
  getExchangeConfig,
  getExchangeDetail,
  submitExchangeConfig,
} from '@/services/moneyBeanCenter';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { GIFT_URL_ENUM } from '../constant';

const ClearanceConfig = ({ isAudit }) => {
  const hotFormRef = useRef();
  const cashFormRef = useRef();
  const exchangeFormRef = useRef();
  const exchangeActionRef = useRef();
  const [list, setList] = useState([]);
  const [auditData, setAuditData] = useState(null);
  const [loading, setLoading] = useState(true);

  const parseMyGiftRedirectUrl = (item) => {
    let data = { ...item };
    if (data.myGiftRedirectUrl) {
      let key = '';
      for (const str in GIFT_URL_ENUM) {
        GIFT_URL_ENUM[str].url === data.myGiftRedirectUrl && (key = str);
      }
      if (key) {
        data.myGiftRedirectUrl = [key];
      } else {
        data.myGiftRedirectUrl = [data.myGiftRedirectUrl];
      }
    }
    return data;
  };

  const initData = async () => {
    const func = isAudit ? getExchangeDetail : getExchangeConfig;
    const res = await func();
    if (!res) {
      return;
    }
    const {
      cashCouponPerPersonLimit,
      cashCouponNominalAmountList,
      hotExchangeGoodsList,
      exchangeGoodsList = [],
    } = res?.data || {};
    setAuditData(res.data);
    cashFormRef.current?.setFieldsValue({
      cashCouponPerPersonLimit,
      cashCouponNominalAmountList: cashCouponNominalAmountList.map((item) => {
        return {
          ...item,
          nominalAmount: (+item.nominalAmount / 100).toFixed(2),
        };
      }),
    });

    if (hotExchangeGoodsList?.[0]) {
      const data = parseMyGiftRedirectUrl(hotExchangeGoodsList?.[0]);
      hotFormRef.current?.setFieldsValue({
        ...data,
        isTop: data.isTop.toString(),
        isAdPosition: data.isAdPosition.toString(),
      });
    }

    exchangeFormRef.current?.setFieldsValue({
      exchangeGoodsList: exchangeGoodsList.map((item) => {
        const data = parseMyGiftRedirectUrl(item);
        return {
          ...data,
          isAdPosition: data.isAdPosition.toString(),
        };
      }),
    });
    setLoading(false);
  };

  useEffect(() => {
    initData();
  }, []);

  const changePosition = (dragIndex, hoverIndex) => {
    exchangeActionRef.current.move(dragIndex, hoverIndex);
  };

  const addItem = (index) => {
    exchangeActionRef.current.add({}, index + 1);
  };

  const delItem = (index) => {
    exchangeActionRef.current.remove(index);
  };

  const onSubmit = async () => {
    const res1 = await cashFormRef.current.validateFields().catch((e) => {
      message.error('请完善必填配置');
      throw e;
    });
    const cashCouponPerPersonLimit = res1.cashCouponPerPersonLimit;
    const cashCouponNominalAmountList = res1.cashCouponNominalAmountList.map(
      (item, index) => {
        return {
          ...item,
          nominalAmount: +item.nominalAmount * 100,
          firstExchange: index === 0,
        };
      },
    );

    let res2 = await hotFormRef.current.validateFields().catch((e) => {
      message.error('请完善必填配置');
      throw e;
    });
    console.log(1, res2);
    if (res2?.myGiftRedirectUrl?.length > 0) {
      console.log(2, res2.myGiftRedirectUrl[0]);
      const val = res2.myGiftRedirectUrl[0];
      if (GIFT_URL_ENUM[val]) {
        console.log(3, GIFT_URL_ENUM[val].url);
        res2.myGiftRedirectUrl = GIFT_URL_ENUM[val].url;
      } else {
        console.log(4, val);

        res2.myGiftRedirectUrl = val;
      }
    }

    const hotExchangeGoodsList = [res2];

    const res3 = await exchangeFormRef.current.validateFields().catch((e) => {
      message.error('请完善必填配置');
      throw e;
    });
    const exchangeGoodsList = res3.exchangeGoodsList.map((item, index) => {
      let data = {
        ...item,
      };
      if (data?.myGiftRedirectUrl?.length > 0) {
        const val = data.myGiftRedirectUrl[0];
        if (GIFT_URL_ENUM[val]) {
          data.myGiftRedirectUrl = GIFT_URL_ENUM[val].url;
        } else {
          data.myGiftRedirectUrl = val;
        }
      }

      if (data.isAdPosition === 'true') {
        return {
          isAdPosition: data.isAdPosition,
          rewardLogo: data.rewardLogo,
          adPositionRedirectUrl: data.adPositionRedirectUrl,
        };
      } else {
        return data;
      }
    });

    const res = await submitExchangeConfig({
      id: auditData.id,
      cashCouponPerPersonLimit,
      cashCouponNominalAmountList,
      hotExchangeGoodsList,
      exchangeGoodsList,
    });
    if (res && res.code === '200') {
      message.success('提交成功');
      initData();
    }
  };

  return (
    <ConfigProvider>
      <Spin spinning={loading}>
        <Space direction="vertical" size="middle">
          {!isAudit && (
            <AuditCard
              title="核销配置审核"
              type="MoneyBeanCenter"
              scene="exchange"
              auditData={auditData}
              onSubmit={onSubmit}
            />
          )}
          <ExchangeCashForm ref={cashFormRef} isAudit={isAudit} />
          <HotGoodsForm ref={hotFormRef} isAudit={isAudit} />
          <ProCard title="兑换奖励配置（支持拖拽排序）">
            <ProForm
              formRef={exchangeFormRef}
              submitter={false}
              readonly={isAudit}
            >
              <DndProvider backend={HTML5Backend}>
                <ProFormList
                  actionRef={exchangeActionRef}
                  alwaysShowItemLabel
                  min={1}
                  name="exchangeGoodsList"
                  deleteIconProps={false}
                  copyIconProps={false}
                  creatorButtonProps={false}
                  itemRender={(doms, listMeta) => {
                    const { index } = listMeta;
                    console.log(111, doms, listMeta);
                    return (
                      <ProFormGroup key="group">
                        <ExchangeRewardsForm
                          key={index}
                          index={index}
                          isAudit={isAudit}
                          changePosition={changePosition}
                        />
                        {!isAudit && (
                          <div>
                            <PlusCircleOutlined
                              onClick={() => {
                                addItem(index);
                              }}
                              style={{
                                fontSize: '24px',
                                margin: '80px 0 0 -30px',
                              }}
                            />
                            {index !== 0 && (
                              <DeleteOutlined
                                onClick={() => {
                                  delItem(index);
                                }}
                                style={{
                                  fontSize: '24px',
                                  margin: '80px 0 0 10px',
                                }}
                              />
                            )}
                          </div>
                        )}
                      </ProFormGroup>
                    );
                  }}
                />
              </DndProvider>
            </ProForm>
          </ProCard>
        </Space>
      </Spin>
    </ConfigProvider>
  );
};

export default ClearanceConfig;
