import {
  React, Fragment, useState, forwardRef,
} from 'react';
import { ProCard, BetaSchemaForm } from '@ant-design/pro-components';
import {
  validateTrialDuration,
  validatePositiveInteger,
} from '@/utils/validate';
import { getDmpTag } from '@/services/moneyBeanCenter';

let timer = null;
const $debounce = (fn, time) => {
  if (timer) {
    clearTimeout(timer);
    timer = null;
  }
  timer = setTimeout(() => {
    fn();
  }, time);
};

const CommonConfigForm = forwardRef(({ isAudit }, formRef) => {
  const [options, setOptions] = useState([]);
  const columns = [
    {
      title: '钱豆汇率',
      tooltip: '请填正整数，限为100的整数倍',
      dataIndex: 'exchangeRate',
      valueType: 'input',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          { validator: validatePositiveInteger },
        ],
      },
      fieldProps: {
        prefix: '1元 = ',
        suffix: '钱豆',
        disabled: true,
      },
      width: 'md',
      colProps: {
        xs: 24,
        md: 12,
      },
    },
    {
      valueType: 'group',
      columns: [
        {
          title: '话费流量最高抵扣比例',
          tooltip: '请填0-100之间的正整数，填0则为不可抵扣',
          dataIndex: 'phoneBillDiscountRate',
          formItemProps: {
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
              { validator: validateTrialDuration },
            ],
          },
          fieldProps: {
            suffix: '%',
          },
        },
        {
          title: '话费流量最高抵扣次数',
          tooltip: '请填正整数，填0则为不可抵扣',
          dataIndex: 'phoneBillDiscountTimes',
          formItemProps: {
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
              { validator: validateTrialDuration },
            ],
          },
          fieldProps: {
            suffix: '次/月',
          },
        },
      ],
    },
    {
      title: <h3>应用下载过滤规则（优先级按顺序由高到低）</h3>,
      valueType: 'group',
      columns: [
        {
          title: (
            <h4>
              按投放人群生效（支持添加多个dmp人群，黑名单用户可以上传dmp进行投放控制）
            </h4>
          ),
          valueType: 'formList',
          dataIndex: ['cpdDownloadFilterDTO', 'cpdDownloadCrowdFilter', 'dmpFilters'],
          initialValue: [{ state: 'all', title: '标题' }],
          colProps: {
            xs: 24,
            sm: 12,
          },
          columns: [
            {
              valueType: 'group',
              columns: [
                {
                  title: '用户标签',
                  dataIndex: 'dmpId',
                  valueType: 'select',
                  fieldProps: {
                    showSearch: true,
                    allowClear: true,
                    placeholder: '请输入用户标签',
                    defaultActiveFirstOption: false,
                    suffixIcon: null,
                    filterOption: false,
                    onClear: () => {
                      setOptions([]);
                    },
                    onSearch: async (value) => {
                      $debounce(async () => {
                        const res = await getDmpTag({ tagName: value });
                        const data = res
                          && res.data
                          && res.data.map((item) => ({
                            value: item.id,
                            text: item.tagIdWithName,
                          }));
                        setOptions(data);
                      }, 500);
                    },
                    options: (options || []).map((d) => ({
                      value: d.value,
                      label: d.text,
                    })),
                  },
                  colProps: {
                    xs: 24,
                    sm: 12,
                  },
                  width: 'md',
                  formItemProps: {
                    rules: [
                      {
                        required: true,
                        message: '此项为必填项',
                      },
                    ],
                  },
                },
                {
                  title: '单人单日应用展示数',
                  dataIndex: 'appCount',
                  width: 'md',
                  colProps: {
                    xs: 24,
                    sm: 12,
                  },
                  formItemProps: {
                    rules: [
                      {
                        required: true,
                        message: '此项为必填项',
                      },
                    ],
                  },
                },
              ],
            },
          ],
        },
        {
          title: '按规则生效（默认全部用户生效）',
          valueType: 'group',
          columns: [
            {
              title: '应用下载日期',
              dataIndex: ['cpdDownloadFilterDTO', 'cpdDownloadRulesFilter', 'filterDays'],
              fieldProps: {
                prefix: '近',
                suffix: '天',
              },
            },
            {
              title: '应用卸载率',
              tooltip: '卸载数/下载数',
              dataIndex: ['cpdDownloadFilterDTO', 'cpdDownloadRulesFilter', 'uninstallRate'],
              fieldProps: {
                prefix: '大于等于',
                suffix: '%',
              },
            },
            {
              title: '单人单日应用展示数',
              dataIndex: ['cpdDownloadFilterDTO', 'cpdDownloadRulesFilter', 'appCount'],
              tooltip:
                '近xx天，应用卸载率大于等于xx%的用户，单人单日应用展示xx个',
            },
          ],
        },
      ],
    },
  ];

  return (
    <>
      <ProCard
        title="通用配置"
        subTitle="为了保证用户体验，以下规则应用之后不会立刻生效，会从次日0点起生效"
      >
        <BetaSchemaForm
          layoutType="Form"
          formRef={formRef}
          columns={columns}
          readonly={isAudit}
          submitter={{
            resetButtonProps: {
              style: {
                display: 'none',
              },
            },
            submitButtonProps: {
              style: {
                display: 'none',
              },
            },
          }}
        />
      </ProCard>
    </>
  );
});

export default CommonConfigForm;
