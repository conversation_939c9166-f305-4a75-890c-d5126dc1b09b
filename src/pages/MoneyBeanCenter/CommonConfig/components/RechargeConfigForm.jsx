import { React, Fragment, forwardRef } from 'react';
import { ProCard, BetaSchemaForm } from '@ant-design/pro-components';

const CommonConfigForm = forwardRef(({ isAudit }, formRef) => {
  const columns = [
    {
      title: <h4>充值中心来源钱豆卡片文案（通用卡片文案：价值约为XX.XX元，可微信提现）</h4>,
      valueType: 'group',
      columns: [
        {
          title: '有钱豆余额文案',
          valueType: 'input',
          width: 'md',
          dataIndex: ['qdOriginInfoDTO', 'remainDesc'],
          formItemProps: {
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
            ],
          },
        },
        {
          title: '无钱豆余额文案',
          valueType: 'input',
          width: 'md',
          dataIndex: ['qdOriginInfoDTO', 'remainNoneDesc'],
          formItemProps: {
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
            ],
          },
        },
      ],
    },
    {
      title: <h4>钱豆中心主启引导弹窗配置</h4>,
      valueType: 'group',
      columns: [
        {
          title: '生效渠道spm',
          valueType: 'textarea',
          width: 'md',
          dataIndex: ['qdOriginInfoDTO', 'opSpmList'],
          formItemProps: {
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
            ],
          },
          colProps: {
            xs: 24,
            md: 12,
          },
        },
      ],
    },
  ];

  return (
    <>
      <ProCard
        title="充值中心跳转配置"
        subTitle="为了保证用户体验，以下规则应用之后不会立刻生效，会从次日0点起生效"
      >
        <BetaSchemaForm
          layoutType="Form"
          formRef={formRef}
          columns={columns}
          readonly={isAudit}
          submitter={{
            resetButtonProps: {
              style: {
                display: 'none',
              },
            },
            submitButtonProps: {
              style: {
                display: 'none',
              },
            },
          }}
        />
      </ProCard>
    </>
  );
});

export default CommonConfigForm;
