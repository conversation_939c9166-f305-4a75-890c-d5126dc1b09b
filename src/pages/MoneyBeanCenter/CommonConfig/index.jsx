import {
  React, useRef, useState, useEffect,
} from 'react';
import AuditCard from '@/components/AuditCard';
import { Space, message } from 'antd';
import {
  getCommonConfig,
  getCommonDetail,
  submitCommonConfig,
} from '@/services/moneyBeanCenter';
import CommonConfigForm from './components/CommonConfigForm';
import RechargeConfigForm from './components/RechargeConfigForm';

const CommonConfig = ({ isAudit }) => {
  const comFormRef = useRef();
  const rechargeFormRef = useRef();

  const [auditData, setAuditData] = useState({});

  const requestHandle = async () => {
    const res = isAudit ? await getCommonDetail() : await getCommonConfig();
    const data = res?.data || {};
    setAuditData(data);
    const {
      exchangeRate, phoneBillDiscountRate, phoneBillDiscountTimes, cpdDownloadFilterDTO, qdOriginInfoDTO,
    } = data;
    if (qdOriginInfoDTO && qdOriginInfoDTO.opSpmList && typeof qdOriginInfoDTO.opSpmList === 'object') {
      qdOriginInfoDTO.opSpmList = qdOriginInfoDTO.opSpmList.join(',');
    }
    comFormRef.current?.setFieldsValue({
      exchangeRate,
      phoneBillDiscountRate,
      phoneBillDiscountTimes,
      cpdDownloadFilterDTO,
    });
    rechargeFormRef.current?.setFieldsValue({
      qdOriginInfoDTO,
    });
  };
  useEffect(() => {
    requestHandle();
  }, []);

  const onSubmit = async () => {
    const comRes = await comFormRef.current.validateFields().catch((e) => {
      message.error('请完善必填配置');
      throw e;
    });
    const rechargeRes = await rechargeFormRef.current.validateFields().catch((e) => {
      message.error('请完善必填配置');
      throw e;
    });
    if (typeof rechargeRes.qdOriginInfoDTO.opSpmList === 'string') {
      rechargeRes.qdOriginInfoDTO.opSpmList = rechargeRes.qdOriginInfoDTO.opSpmList.split(',');
    }
    const params = {
      id: auditData.id,
      ...comRes,
      ...rechargeRes,
    };
    // 过滤空值不传
    const { cpdDownloadCrowdFilter, cpdDownloadRulesFilter } = params?.cpdDownloadFilterDTO;
    if (Object.keys(cpdDownloadCrowdFilter).length === 0 && Object.keys(cpdDownloadRulesFilter).length === 0) {
      delete params.cpdDownloadFilterDTO;
    } else {
      if (Object.keys(cpdDownloadCrowdFilter).length !== 0) {
        const { dmpFilters } = cpdDownloadCrowdFilter;
        if (!dmpFilters || (dmpFilters && dmpFilters.length === 0)) {
          delete params.cpdDownloadFilterDTO.cpdDownloadCrowdFilter;
        }
      } else {
        delete params.cpdDownloadFilterDTO.cpdDownloadCrowdFilter;
      }
      if (Object.keys(cpdDownloadRulesFilter).length !== 0) {
        const { appCount, filterDays, uninstallRate } = cpdDownloadRulesFilter;
        if (!(appCount || filterDays || uninstallRate)) {
          delete params.cpdDownloadFilterDTO.cpdDownloadRulesFilter;
        }
      } else {
        delete params.cpdDownloadFilterDTO.cpdDownloadRulesFilter;
      }
      if (Object.keys(params.cpdDownloadFilterDTO).length === 0) {
        delete params.cpdDownloadFilterDTO;
      }
    }

    const res = await submitCommonConfig(params);
    if (res && res.code === '200') {
      message.success('提交成功');
    }
    await requestHandle();
  };

  return (
    <Space direction="vertical" size="middle">
      {!isAudit && (
        <AuditCard
          title="通用配置审核"
          onSubmit={onSubmit}
          type="MoneyBeanCenter"
          scene="common"
          auditData={auditData}
        />
      )}
      <CommonConfigForm ref={comFormRef} isAudit={isAudit} />
      <RechargeConfigForm ref={rechargeFormRef} isAudit={isAudit} />
    </Space>
  );
};

export default CommonConfig;
