import {
  ProFormDigit,
  ProFormSelect,
  DrawerForm,
  ProFormText,
  ProFormDateTimeRangePicker,
  ProFormRadio,
  ProFormUploadButton,
  ModalForm,
} from '@ant-design/pro-components';
import {
  Button, Form, message, Space, Row,
} from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { downloadFile } from '@/utils/util';
import {
  saveAutoIncreaseLimit,
  auditAutoIncreaseLimit,
} from '@/services/increaseQuotaActivity/multiScenario';
import { getDmpTag, getUsePackageView } from '@/services/common';
import PropTypes from 'prop-types';
import Style from './index.less';
// eslint-disable-next-line import/no-cycle
import OperateHistory from '../OperateHistory';

const TitleEnum = {
  add: '新建',
  edit: '编辑',
  copy: '复制',
  view: '详情',
  audit: '审核',
  history: '操作历史',
};

const DetailModal = ({
  action,
  detail,
  drawerVisit,
  setDrawerVisit,
  handleReload,
  sceneOptions = [],
}) => {
  const [form] = Form.useForm();
  const [radioValue, setRadioValue] = useState();
  const [userPackageData, setUserPackageData] = useState([]);

  useEffect(() => {
    if (!detail) return;
    const data = { ...detail };
    const { startTime, endTime } = data;
    let actTime = [];
    if (startTime && endTime) {
      actTime = [startTime, endTime];
    }
    form.setFieldsValue({ ...data, actTime });
    detail?.audienceType === 'userPackage'
      && form.setFieldsValue({
        uploadTxt: [
          {
            name: detail?.audienceName,
            status: 'done',
            // eslint-disable-next-line no-undef
            url: `${API_BASE_URL}/user/file/download?fileId=${detail?.audienceTag}`,
          }],
      });
    setRadioValue(detail.audienceType);
  }, [detail]);

  const id = useMemo(() => detail?.id, [detail]);
  const readonly = useMemo(() => !['add', 'edit', 'copy'].includes(action), [action]);
  const operateHistory = useMemo(() => drawerVisit && ['view', 'audit'].includes(action), [action, drawerVisit]);

  // 审核
  const onAudit = async (auditOperation) => {
    const res = await auditAutoIncreaseLimit({
      id,
      auditOperation,
    });
    if (!res) return;
    setDrawerVisit(false);
    handleReload();
    form.resetFields(); // 重置表单
  };

  // 新增 修改 复制
  const onFinish = async (param) => {
    const data = param;
    const { actTime: [startTime, endTime] = [] } = data;
    if (action === 'copy') data.id = undefined;
    data.startTime = startTime;
    data.endTime = endTime;
    delete data.actTime;
    if (param?.uploadTxt) {
      if (param?.uploadTxt[0]?.response) {
        const { data: { fileId, fileName } } = param?.uploadTxt[0].response;
        data.audienceTag = fileId;
        data.audienceName = fileName;
      } else {
        data.audienceTag = detail?.audienceTag;
        data.audienceName = detail?.audienceName;
      }
    }
    const res = await saveAutoIncreaseLimit(data);
    if (!res) return false;
    message.success('提交成功');
    handleReload();
    return true;
  };

  const submiter = useMemo(() => {
    switch (action) {
      case 'view':
      case 'history':
        return {
          searchConfig: {
            resetText: '关闭',
          },
          submitButtonProps: {
            style: {
              display: 'none',
            },
          },
        };
      case 'audit':
        return {
          submitButtonProps: {
            style: {
              display: 'none',
            },
          },
          render: (props, defaultDoms) => [
            ...defaultDoms,
            <Button
              type="primary"
              key="审核驳回"
              onClick={() => onAudit(3)}
            >
              审核驳回
            </Button>,
            <Button
              type="primary"
              key="审核通过"
              onClick={() => onAudit(2)}
            >
              审核通过
            </Button>,
          ],
        };
      default:
        return {
          searchConfig: {
            submitText: '提交',
            resetText: '取消',
          },
        };
    }
  }, [action, id]);
  const handleUserPackage = async () => {
    const param = {
      fileId: detail?.audienceTag,
    };
    const res = await getUsePackageView(param);
    if (res.code === '200' && res.data) {
      setUserPackageData(res.data?.split(','));
    }
  };
  return (
    <DrawerForm
      submitter={submiter}
      layout="horizontal"
      form={form}
      readonly={readonly}
      onOpenChange={(val) => {
        if (!val) {
          form.resetFields();
        }
        setDrawerVisit(val);
      }}
      title={TitleEnum[action]}
      open={drawerVisit}
      onFinish={onFinish}
      className={Style.MultiScenarioWarp}
      labelCol={{ span: 4 }}
    >
      <ProFormText
        name="id"
        label="id"
        hidden
      />
      <ProFormText
        name="version"
        label="version"
        hidden
      />
      <ProFormText
        name="name"
        label="配置名称"
        rules={[{ required: true }]}
        fieldProps={{
          maxLength: 20,
        }}
      />
      <ProFormSelect
        rules={[{ required: true }]}
        fieldProps={
          {
            options: sceneOptions,
          }
        }
        name="sceneId"
        label="触发场景"
      />
      <ProFormRadio.Group
        rules={[{ required: true }]}
        name="audienceType"
        label="提额名单"
        options={[
          {
            label: '所有人',
            value: 'allUsers',
          },
          {
            label: '用户标签',
            value: 'dmpTag',
          },
          {
            label: '人群包',
            value: 'userPackage',
          },
        ]}
        fieldProps={{
          onChange: (e) => {
            const temp = e.target.value;
            setRadioValue(temp); // 存储选中的值
            form.setFieldsValue({ audienceType: temp }); // 更新表单中的值
          },
        }}
      />
      {radioValue === 'allUsers' && (
        null
      )}
      {radioValue === 'dmpTag' && (
        <ProFormSelect
          rules={[{ required: true }]}
          fieldProps={{
            showSearch: true,
          }}
          name="audienceTag"
          label="用户标签"
          placeholder="请查询选择"
          mode="single"
          debounceTime={300} // 防抖时间，减少请求频率
          request={async ({ keyWords }) => {
            const { data } = await getDmpTag(keyWords ? { tagName: keyWords } : { tagId: detail?.audienceTag });
            return data?.map((item) => ({
              label: item.tagIdWithName,
              value: String(item.id),
            }));
          }}
        />
      )}
      {
        radioValue === 'userPackage' && (
          <>
            <ProFormUploadButton
              rules={[{ required: true }]}
              label="上传文件(txt)"
              name="uploadTxt"
              title="上传文件"
              max={1} // 限制只上传一个文件
              // eslint-disable-next-line no-undef
              action={`${API_BASE_URL}/coupon/campaign/upload/back-up`}
              accept=".txt"
              listType="text"
              fieldProps={{
                withCredentials: true,
              }}
              disabled={readonly}
              extra={(
                readonly && (
                  <Space>
                    <ModalForm
                      trigger={(
                        <Button type="link" disabled={false} onClick={handleUserPackage}>
                          查看预览
                        </Button>
                      )}
                      title="人群包配置"
                      modalProps={{
                        destroyOnClose: true,
                        centered: true,
                      }}
                      submitter={false}
                    >
                      <div
                        style={{
                          height: 300,
                          width: '100%',
                          overflow: 'scroll',
                        }}
                      >
                        {userPackageData.map((i) => (
                          <Row>{i}</Row>
                        ))}
                      </div>
                    </ModalForm>
                    <Button
                      type="link"
                      disabled={false}
                      onClick={() => {
                        downloadFile(detail.audienceTag);
                      }}
                    >
                      下载文件
                    </Button>
                  </Space>
                )
              )}
            />
          </>
        )
      }
      <ProFormDigit
        name="increasePercentage"
        rules={[{ required: true }]}
        min={0}
        max={100}
        label="自营提额幅度"
        placeholder="请输入0-100的数字，不限小数"
        addonAfter="%"
        extra="每次提额不能低于500元"
      />
      <ProFormDigit
        rules={[{ required: true }]}
        label="提额有效期"
        placeholder="请输入1-9999的正整数"
        name="validDays"
        min={1}
        max={9999}
        fieldProps={{ precision: 0 }}
        addonAfter="天"
      />
      <ProFormDigit
        rules={[{ required: true }]}
        label="延迟通知"
        placeholder="请输入1-9999的正整数"
        name="delayMinutes"
        min={1}
        max={9999}
        fieldProps={{ precision: 0 }}
        addonAfter="分钟"
      />
      {operateHistory && (
        <OperateHistory
          key="operateHistory"
          id={id}
          bizCode="cod"
          disabled={false}
          sceneOptions={sceneOptions}
        />
      )}
      <ProFormDateTimeRangePicker
        name="actTime"
        label="活动时间"
        rules={[{ required: true }]}
      />
      <ProFormText
        name="experimentCode"
        label="实验code"
        placeholder="非必填，若填写则仅对实验组生效提额"
        rules={[{ required: false }]}
      />
    </DrawerForm>
  );
};

// 添加 prop 类型验证
DetailModal.propTypes = {
  action: PropTypes.string.isRequired,
  detail: PropTypes.shape({
    zyIncreasePercentage: PropTypes.string,
    id: PropTypes.number,
  }).isRequired,
  drawerVisit: PropTypes.bool.isRequired,
  setDrawerVisit: PropTypes.func.isRequired,
  handleReload: PropTypes.func.isRequired,
  sceneOptions: PropTypes.arrayOf(PropTypes.shape({
    value: PropTypes.string.isRequired,
    label: PropTypes.string.isRequired,
    // 其他可能的属性
  })),
};

// 设置默认值
DetailModal.defaultProps = {
  sceneOptions: [],
};

export default DetailModal;
