import React, { useRef, useState } from 'react';
import CommonTableList from '@/components/CommonTableList';
import {
  Button, Space, Tag, Divider, Popconfirm, Spin,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import {
  getBatchIncreaseLimitList,
  batchIncreaseLimitOnline,
  deleteBatchIncreaseLimit,
  getBatchIncreaseLimitDetails,
} from '@/services/increaseQuotaActivity/initiativeApply';
import DetailModal from './components/DetailModal/index';

const InitiativeApply = () => {
  const actionRef = useRef();
  const [modalVisible, setModalVisible] = useState(false); // 弹窗可见性
  const [action, setAction] = useState('');
  const [detail, setDetail] = useState();
  const [loading, setLoading] = useState(false);

  /**
   * 列表数据请求
   */
  const gainActivitySettingList = async (params) => {
    // const res = await getActivitySettingList(params);
    const res = await getBatchIncreaseLimitList(params);
    const { data } = res;
    return {
      data: data.list,
      total: data.total,
    };
  };

  /**
   * 表格刷新
   */
  const handleReload = () => {
    actionRef.current?.reload();
  };

  /**
 * 列表操作
 * actionType: view edit delete audit add
 */
  const handleAction = async (record, actionType) => {
    // 删除
    if (actionType === 'delete') {
      const res = await deleteBatchIncreaseLimit({ id: record.id });
      if (res?.code === '200') {
        handleReload();
      }
      return;
    }
    // 编辑详情
    if (record.id) {
      // 请求详情信息
      setLoading(true);
      // const res = await getActivityDetails({ id: record.id });
      const res = await getBatchIncreaseLimitDetails({ id: record.id });
      if (res?.code === '200' && res?.data) {
        const data = res.data || {};
        setDetail(data);
        setLoading(false);
        setModalVisible(true);
      }
    } else {
      setLoading(false);
      setModalVisible(true);
    }
    setAction(actionType);
  };

  /**
  * 活动上下线
  * onlineStatus 1 上线 2 下线
  */
  const changeOnlineStatus = async (record) => {
    const params = {
      id: record.id,
      onlineOperation: record.onlineStatus === 1 ? 2 : 1,
    };
    const res = await batchIncreaseLimitOnline(params);
    if (res?.code === '200') {
      handleReload();
    }
  };

  const tableColumns = [
    {
      title: 'id',
      dataIndex: 'id',
      key: 'id',
      valueType: 'digit',
      fieldProps: {
        min: 1,
        precision: 0,
      },
      width: 120,
      fixed: 'left',
      align: 'center',
      render: (text, record) => <span>{record.id}</span>,
    },
    {
      title: '配置名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'center',
    },
    {
      title: '提额名单',
      dataIndex: 'audienceType',
      key: 'audienceType',
      valueType: 'select',
      valueEnum: {
        1: '用户标签',
        2: '人群包',
      },
      width: 120,
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '提额幅度',
      dataIndex: 'increasePercentage',
      key: 'increasePercentage',
      width: 200,
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '提额有效期',
      dataIndex: 'validDays',
      key: 'validDays',
      width: 200,
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '提额生效时间',
      dataIndex: 'sendTime',
      key: 'sendTime',
      hideInSearch: true,
      valueType: 'dateTime',
      width: 200,
      align: 'center',
    },
    {
      title: '创建人',
      dataIndex: 'operatorName',
      key: 'operatorName',
      hideInSearch: true,
      width: 120,
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'onlineStatus',
      key: 'onlineStatus',
      valueType: 'select',
      // 上线状态 1:禁用 2:启用 3:已失效 4:数据准备中/发送中 5:已完成 6:已冻结
      valueEnum: {
        1: {
          text: '禁用',
          status: 'Error',
        },
        2: {
          text: '启用',
          status: 'Success',
        },
        3: {
          text: '已失效',
          status: 'Default',
        },
        4: {
          text: '发送中',
          status: 'Default',
        },
        5: {
          text: '已完成',
          status: 'Success',
        },
        6: {
          text: '已冻结',
          status: 'Error',
        },
      },
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
      width: 120,
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '审核人',
      dataIndex: 'auditorName',
      key: 'auditorName',
      hideInSearch: true,
      width: 120,
      align: 'center',
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      key: 'auditStatus',
      valueType: 'select',
      valueEnum: {
        1: '待审核',
        2: '审核通过',
        3: '审核驳回',
      },
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
      render: (_, record) => {
        const auditStatusColorMap = {
          1: { text: '待审核', color: '#faad14' },
          2: { text: '审核通过', color: '#1890ff' },
          3: { text: '审核驳回', color: 'f5222d' },
        };
        return (
          <Space>
            <Tag color={auditStatusColorMap[record.auditStatus]?.color} key={_}>
              {auditStatusColorMap[record.auditStatus]?.text}
            </Tag>
          </Space>
        );
      },
      width: 120,
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      hideInSearch: true,
      valueType: 'dateTime',
      width: 140,
      align: 'center',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      valueType: 'dateTime',
      hideInSearch: true,
      width: 140,
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      hideInSearch: true,
      width: 400,
      align: 'center',
      fixed: 'right',
      render: (text, record) => (
        <Space split={<Divider type="vertical" />} key={record.id}>
          {record.supportEdit === 1 && (
            <Button
              key="edit"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'edit');
              }}
            >
              编辑
            </Button>
          )}
          {record.supportDelete === 1 && (
            <Popconfirm
              title="删除后无法恢复，确认删除吗？"
              onConfirm={() => {
                handleAction(record, 'delete');
              }}
              key="del"
              onCancel={() => { }}
              okText="确定"
              cancelText="取消"
            >
              <Button key="delete" type="link" size="small">
                删除
              </Button>
            </Popconfirm>
          )}
          {record.supportApprove === 1 && (
            <Button
              key="audit"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'audit');
              }}
            >
              审核
            </Button>
          )}
          {record.supportView === 1 && (
            <Button
              key="view"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'view');
              }}
            >
              详情
            </Button>
          )}
          {record.supportOffline === 1 && (
            <Button
              key="offline"
              type="link"
              size="small"
              onClick={() => {
                changeOnlineStatus(record);
              }}
            >
              下线
            </Button>
          )}
          {record.supportOnline === 1 && (
            <Button
              key="online"
              type="link"
              size="small"
              onClick={() => {
                changeOnlineStatus(record);
              }}
            >
              上线
            </Button>
          )}
          {record.supportCopy === 1 && (
            <Button
              key="copy"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'copy');
              }}
            >
              复制
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <>
      <Spin spinning={loading}>
        <CommonTableList
          toolBarRender={() => [
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                handleAction({}, 'add');
              }}
            >
              新增
            </Button>,
          ]}
          columns={tableColumns}
          request={gainActivitySettingList}
          actionRef={actionRef}
          pagination={{
            pageSize: 10,
          }}
        />
        {/* 详情弹窗 */}
        <DetailModal
          action={action}
          detail={detail}
          handleReload={handleReload}
          drawerVisit={modalVisible}
          setDrawerVisit={setModalVisible}
        />
      </Spin>
    </>
  );
};

export default InitiativeApply;
