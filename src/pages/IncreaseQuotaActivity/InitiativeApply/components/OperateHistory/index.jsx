import React, { useState } from 'react';
import CommonTableList from '@/components/CommonTableList';
import { queryLogList } from '@/services/common';
import { operateBasicColumn } from '@/constant/config';
import { Button } from 'antd';
import PropTypes from 'prop-types';
// eslint-disable-next-line import/no-cycle
import DetailModal from '../DetailModal/index';

const OperateHistory = ({ id, bizCode }) => {
  const [modalVisible, setModalVisible] = useState(false); // 弹窗可见性
  const [detail, setDetail] = useState();
  // 操作记录列表数据请求
  const getOperationHistory = async (params) => {
    const param = {
      page: params.current,
      pageSize: params.pageSize,
      operationId: id,
      bizCode,
    };
    const res = await queryLogList(param);
    return {
      data: res.data.list,
      total: res.data.total,
    };
  };

  const columns = [
    ...operateBasicColumn,
    {
      title: '操作详情',
      dataIndex: 'actionType',
      key: 'actionType',
      align: 'center',
      fixed: 'right',
      hideInSearch: true,
      render: (text, record) => (
        <Button
          key="history"
          type="link"
          size="small"
          // disabled={false}
          onClick={() => {
            setDetail(record.contentBefore);
            setModalVisible(true);
          }}
        >
          查看详情
        </Button>
      ),
    },
  ];
  return (
    <>
      <CommonTableList
        key="OperateHistory"
        columns={columns}
        search={false}
        bordered
        request={getOperationHistory}
        pagination={{
          pageSize: 10,
          showSizeChanger: false,
          showTotal: (total) => `共${total}项`,
        }}
        headerTitle="操作记录"
      />
      {/* 详情弹窗 */}
      <DetailModal action="history" detail={detail} drawerVisit={modalVisible} setDrawerVisit={setModalVisible} />
    </>
  );
};

OperateHistory.propTypes = {
  id: PropTypes.string.isRequired,
  bizCode: PropTypes.string.isRequired,
};

export default OperateHistory;
