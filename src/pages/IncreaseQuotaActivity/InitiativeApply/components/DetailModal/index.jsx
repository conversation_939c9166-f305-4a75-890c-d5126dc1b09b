import {
  ProFormDigit,
  ProFormSelect,
  DrawerForm,
  ProFormDateTimePicker,
  ProFormText,
  ProFormRadio,
  ProFormUploadButton,
  ModalForm,
} from '@ant-design/pro-components';
import {
  Button, Form, message, Space, Row,
} from 'antd';
import React, {
  memo, useEffect, useMemo, useState,
} from 'react';
import {
  saveBatchIncreaseLimit,
  auditBatchIncreaseLimit,
} from '@/services/increaseQuotaActivity/initiativeApply';
import { getDmpTag, getUsePackageView } from '@/services/common';
import { downloadFile } from '@/utils/util';
import PropTypes from 'prop-types';
import dayjs from 'dayjs';
import Style from './index.less';
// eslint-disable-next-line import/no-cycle
import OperateHistory from '../OperateHistory';

const TitleEnum = {
  add: '新建',
  edit: '编辑',
  copy: '复制',
  view: '详情',
  audit: '审核',
  history: '操作历史',
};

const DetailModal = ({
  action,
  detail,
  drawerVisit,
  setDrawerVisit,
  handleReload,
}) => {
  const [form] = Form.useForm();
  const [radioValue, setRadioValue] = useState();
  const [userPackageData, setUserPackageData] = useState([]);

  useEffect(() => {
    if (!detail) return;
    form.setFieldsValue(detail);
    detail?.audienceType === 2
      && form.setFieldsValue({
        uploadTxt: [
          {
            name: detail?.audienceName,
            status: 'done',
            // eslint-disable-next-line no-undef
            url: `${API_BASE_URL}/user/file/download?fileId=${detail?.audienceTag}`,
          }],
      });
    setRadioValue(detail.audienceType);
  }, [detail]);

  const id = useMemo(() => detail?.id, [detail]);
  const readonly = useMemo(() => !['add', 'edit', 'copy'].includes(action), [action]);
  const operateHistory = useMemo(() => drawerVisit && ['view', 'audit'].includes(action), [action, drawerVisit]);

  // 审核
  const onAudit = async (auditOperation) => {
    const res = await auditBatchIncreaseLimit({
      id,
      auditOperation,
    });
    if (!res) return;
    setDrawerVisit(false);
    handleReload();
    form.resetFields(); // 重置表单
  };

  // 新增 修改 复制
  const onFinish = async (param) => {
    const data = param;
    if (param?.uploadTxt) {
      if (param?.uploadTxt[0]?.response) {
        const { data: { fileId, fileName } } = param?.uploadTxt[0].response;
        data.audienceTag = fileId;
        data.audienceName = fileName;
      } else {
        data.audienceTag = detail?.audienceTag;
        data.audienceName = detail?.audienceName;
      }
    }
    if (action === 'copy') data.id = undefined;
    const res = await saveBatchIncreaseLimit(data);
    if (!res) return false;
    message.success('提交成功');
    handleReload();
    return true;
  };

  const submiter = useMemo(() => {
    switch (action) {
      case 'view':
      case 'history':
        return {
          searchConfig: {
            resetText: '关闭',
          },
          submitButtonProps: {
            style: {
              display: 'none',
            },
          },
        };
      case 'audit':
        return {
          submitButtonProps: {
            style: {
              display: 'none',
            },
          },
          render: () => [
            <Button
              type="primary"
              key="审核驳回"
              onClick={() => onAudit(3)}
            >
              审核驳回
            </Button>,
            <Button
              type="primary"
              key="审核通过"
              onClick={() => onAudit(2)}
            >
              审核通过
            </Button>,
          ],
        };
      default:
        return {
          searchConfig: {
            submitText: '提交',
            resetText: '取消',
          },
        };
    }
  }, [action, id]);

  // 获取人群包数据
  const handleUserPackage = async () => {
    const param = {
      fileId: detail?.audienceTag,
    };
    const res = await getUsePackageView(param);
    if (res.code === '200' && res.data) {
      setUserPackageData(res.data?.split(','));
    }
  };

  return (
    <DrawerForm
      submitter={submiter}
      layout="horizontal"
      form={form}
      readonly={readonly}
      onOpenChange={(val) => {
        if (!val) {
          form.resetFields();
        }
        setDrawerVisit(val);
      }}
      title={TitleEnum[action]}
      open={drawerVisit}
      onFinish={onFinish}
      className={Style.MultiScenarioWarp}
      labelCol={{ span: 4 }}
    >
      <ProFormText
        name="id"
        label="id"
        hidden
      />
      <ProFormText
        name="version"
        label="version"
        hidden
      />
      <ProFormText
        rules={[{ required: true }]}
        fieldProps={{
          maxLength: 20,
        }}
        name="name"
        label="配置名称"
        placeholder="请输入配置名称，限制20字"
      />
      <ProFormRadio.Group
        rules={[{ required: true }]}
        name="audienceType"
        label="提额名单"
        options={[
          {
            label: '用户标签',
            value: 1,
          },
          {
            label: '人群包',
            value: 2,
          },
        ]}
        fieldProps={{
          onChange: (e) => {
            const temp = e.target.value;
            setRadioValue(temp); // 存储选中的值
            form.setFieldsValue({ audienceType: temp }); // 更新表单中的值
          },
        }}
      />
      {radioValue === 1 && (
        <ProFormSelect
          rules={[{ required: true }]}
          fieldProps={{
            showSearch: true,
          }}
          name="audienceTag"
          label="用户标签"
          placeholder="请查询选择"
          mode="single"
          debounceTime={300} // 防抖时间，减少请求频率
          request={async ({ keyWords }) => {
            const { data } = await getDmpTag(keyWords ? { tagName: keyWords } : { tagId: detail?.audienceTag });
            return data?.map((item) => ({
              label: item.tagIdWithName,
              value: String(item.id),
            }));
          }}
        />
      )}
      {
        radioValue === 2 && (
          <>
            <ProFormUploadButton
              rules={[{ required: true }]}
              label="上传文件(txt)"
              name="uploadTxt"
              title="上传文件"
              max={1} // 限制只上传一个文件
              // eslint-disable-next-line no-undef
              action={`${API_BASE_URL}/coupon/campaign/upload/back-up`}
              accept=".txt"
              listType="text"
              fieldProps={{
                withCredentials: true,
              }}
              disabled={readonly}
              extra={(
                readonly && (
                  <Space>
                    <ModalForm
                      trigger={(
                        <Button type="link" disabled={false} onClick={handleUserPackage}>
                          查看预览
                        </Button>
                      )}
                      title="人群包配置"
                      modalProps={{
                        destroyOnClose: true,
                        centered: true,
                      }}
                      submitter={false}
                    >
                      <div
                        style={{
                          height: 300,
                          width: '100%',
                          overflow: 'scroll',
                        }}
                      >
                        {userPackageData.map((i) => (
                          <Row>{i}</Row>
                        ))}
                      </div>
                    </ModalForm>
                    <Button
                      type="link"
                      disabled={false}
                      onClick={() => {
                        downloadFile(detail.audienceTag);
                      }}
                    >
                      下载文件
                    </Button>
                  </Space>
                )
              )}
            />
          </>
        )
      }
      <ProFormRadio.Group
        rules={[{ required: true }]}
        name="partnerCode"
        label="提额合作方"
        options={[
          {
            label: '自营',
            value: '99',
          },
          {
            label: '普享贷',
            value: '88',
          },
        ]}
      />
      <ProFormDigit
        rules={[{ required: true }]}
        label="提额幅度"
        placeholder="请输入0-999的数字，不限小数"
        name="increasePercentage"
        addonAfter="%"
        extra="每次提额不能低于500元"
      />
      <ProFormDateTimePicker
        rules={[{ required: true }]}
        label="提额生效时间"
        name="sendTime"
        fieldProps={{
          disabledDate: (current) => current < dayjs().subtract(1, 'day'),
        }}
      />
      <ProFormDigit
        rules={[{ required: true }]}
        label="提额有效期"
        placeholder="请输入1-9999的正整数"
        name="validDays"
        min={1}
        max={9999}
        fieldProps={{ precision: 0 }}
        addonAfter="天"
      />
      {operateHistory && (
        <OperateHistory
          key="operateHistory"
          id={id}
          bizCode="cod"
          disabled={false}
        />
      )}
    </DrawerForm>
  );
};

// 添加 prop 类型验证
DetailModal.propTypes = {
  action: PropTypes.string.isRequired,
  detail: PropTypes.shape({
    audienceType: PropTypes.number,
    id: PropTypes.number,
    audienceTag: PropTypes.string,
    audienceName: PropTypes.string,
    fileId: PropTypes.number,
  }).isRequired,
  drawerVisit: PropTypes.bool.isRequired,
  setDrawerVisit: PropTypes.func.isRequired,
  handleReload: PropTypes.func.isRequired,
};

export default memo(DetailModal);
