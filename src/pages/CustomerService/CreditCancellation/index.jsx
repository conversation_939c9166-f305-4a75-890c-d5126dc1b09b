import React, {
  useRef, useState, useEffect,
} from 'react';
import { ProCard, StepsForm, BetaSchemaForm } from '@ant-design/pro-components';
import { approveSubmit, getLogOffLimitList } from '@/services/customerService/creditCancellation';
import { getReviewerList } from '@/services/customerService/customerCommon';
import { notification } from 'antd';
import { idCardRule } from '@/utils/validate';

const CreditCancellation = () => {
  const actionRef = useRef();
  const basicFormRef = useRef();
  const mainFormRef = useRef();
  const [quotaList, setQuotaList] = useState({}); // 注销额度列表
  const [reviewerList, setReviewerList] = useState({}); // 复核人列表

  // 获取用户信息（水印用）
  const getLogOffLimitOptions = () => {
    basicFormRef.current && basicFormRef.current.validateFields().then(async (values) => {
      const res = await getLogOffLimitList(values);
      if (res?.code === '200' && res?.data) {
        const limitList = res.data;
        const fundCode = mainFormRef.current?.getFieldsValue()?.fundCode;
        if (fundCode) {
          if (!limitList.some((e) => e.fundCode === fundCode)) {
            mainFormRef.current?.setFieldsValue({ fundCode: '' });
          }
        }
        const options = {};
        limitList.forEach((e) => { options[e.fundCode] = e.desc; });
        setQuotaList(options);
      }
    });
  };

  const getReviewerOptions = async () => {
    const res = await getReviewerList();
    if (res?.code === '200' && res?.data) {
      const options = {};
      res.data.forEach((e) => { options[e.reviewerNo] = e.reviewer; });
      setReviewerList(options);
    }
  };

  useEffect(() => {
    getReviewerOptions();
  }, []);

  const basicConfigurationColumn = [
    {
      title: 'openId',
      dataIndex: 'openId',
      key: 'openId',
      ellipsis: true,
      copyable: true,
      align: 'center',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请输入openId',
          },
        ],
      },
    },
    {
      title: '身份证号',
      dataIndex: 'idNo',
      key: 'idNo',
      ellipsis: true,
      copyable: true,
      align: 'center',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '请输入身份证号',
          },
          idCardRule,
        ],
      },
    },
  ];

  const mainConfigurationColumn = [
    {
      title: '注销额度',
      dataIndex: 'fundCode',
      key: 'fundCode',
      ellipsis: true,
      align: 'center',
      valueEnum: quotaList,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '复核人',
      dataIndex: 'reviewerNo',
      key: 'reviewerNo',
      ellipsis: true,
      align: 'center',
      valueEnum: reviewerList,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
  ];

  return (
    <ProCard
      title="额度注销配置"
      direction="column"
    >
      <StepsForm
        formRef={actionRef}
        onCurrentChange={(number) => {
          if (number === 1) {
            getLogOffLimitOptions();
          }
        }}
        onFinish={async (values) => {
          const params = {
            approveType: 'limit_log_off',
            ...values,
          };

          const res = await approveSubmit(params);

          if (res?.code === '200') {
            notification.success({
              message: '操作成功',
            });
            return true;
          }
          return false;
        }}
      >
        <StepsForm.StepForm name="step1" title="账户信息" formRef={basicFormRef}>
          <BetaSchemaForm columns={basicConfigurationColumn} layoutType="Embed" />
        </StepsForm.StepForm>
        <StepsForm.StepForm name="step2" title="注销信息" formRef={mainFormRef}>
          <BetaSchemaForm columns={mainConfigurationColumn} layoutType="Embed" />
        </StepsForm.StepForm>
      </StepsForm>
    </ProCard>
  );
};

export default CreditCancellation;
