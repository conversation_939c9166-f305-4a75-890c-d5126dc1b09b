import React, { memo, useEffect, useRef } from 'react';
import {
  Image, Row, Button, notification,
} from 'antd';
import { BetaSchemaForm, ModalForm } from '@ant-design/pro-components';
import { auditRefuse, auditPass } from '@/services/customerService/customerCommon';

const AuditModal = ({
  modalVisible,
  setModalVisible,
  detailInfo,
  handleReload,
}) => {
  const formRef = useRef();
  const remarkTitleMap = {
    limit_log_off: '借据依据',
    debt_transfer: '借据依据',
    block_user_loan: '屏蔽用户借款权限原因',
  };

  useEffect(() => {
    console.log('detailInfo', detailInfo);
    formRef?.current?.setFieldsValue(detailInfo);
  }, [detailInfo]);

  const detailColumns = [
    {
      title: 'openid',
      dataIndex: 'openId',
      key: 'openId',
    },
    {
      title: '身份证号',
      dataIndex: 'idNo',
      key: 'idNo',
    },
    {
      title: '借据号',
      dataIndex: 'orderNo',
      key: 'orderNo',
    },
    {
      title: remarkTitleMap[detailInfo.approveType],
      dataIndex: 'remark',
      key: 'remark',
      valueType: 'textarea',
    },
    {
      title: '附件',
      dataIndex: 'imgBaseList',
      key: 'imgBaseList',
      render: (text, record) => {
        console.log('render', { text, record });
        return (
          <Row>
            {text?.length > 0
              ? text?.map((item) => (
                <div style={{ marginRight: 10, marginBottom: 10 }}>
                  <Image width={200} src={item} />
                </div>
              ))
              : ''}
          </Row>
        );
      },
    },
  ];
  let dynamicDetailColumns = [...detailColumns];
  const typeMap = {
    limit_log_off: ['openId', 'idNo', 'orderNo', 'remark'],
    debt_transfer: ['openId', 'orderNo', 'remark', 'imgBaseList'],
    block_user_loan: ['openId', 'idNo', 'remark'],
  };
  dynamicDetailColumns = dynamicDetailColumns.filter((column) => typeMap[detailInfo.approveType]?.includes(column.dataIndex));

  return (
    <ModalForm
      key="detailModal"
      title="通用审核"
      formRef={formRef}
      open={modalVisible}
      onOpenChange={setModalVisible}
      layout="horizontal"
      labelCol={{ span: 4 }}
      wrapperCol={{ span: 20 }}
      readonly
      modalProps={{
        destroyOnClose: true,
        centered: true,
      }}
      width={1000}
      onFinish={async (values) => {
        // 审核通过
        console.log('values', values, 'detailInfo', detailInfo);
        const params = {
          sno: detailInfo.sno,
        };
        const res = await auditPass(params);
        if (res?.code === '200') {
          handleReload();
          notification.success({
            message: '操作成功',
          });
          return true;
        }
        return false;
      }}
      initialValues={detailInfo}
      submitter={detailInfo.canReview ? {
        searchConfig: {
          submitText: '审核通过',
        },
        render: (props, defaultDoms) => {
          console.log('defaultDoms', defaultDoms, 'props', props, props.form.getFieldsValue());
          return [
            <ModalForm
              title="审核驳回"
              trigger={(
                <Button>
                  审核驳回
                </Button>
              )}
              autoFocusFirstInput
              modalProps={{
                destroyOnClose: true,
                centered: true,
              }}
              layout="horizontal"
              onFinish={async (values) => {
                const params = {
                  reviewRemark: values.reviewRemark || '',
                  sno: detailInfo.sno,
                };
                const res = await auditRefuse(params);
                if (res?.code === '200') {
                  handleReload();
                  notification.success({
                    message: '驳回成功',
                  });
                  setModalVisible(false);
                  return true;
                }
                return false;
              }}
            >
              <BetaSchemaForm
                key="detailForm"
                layoutType="Embed"
                columns={[{
                  title: '驳回原因',
                  dataIndex: 'reviewRemark',
                  key: 'reviewRemark',
                  valueType: 'textarea',
                }]}
              />
            </ModalForm>,
            defaultDoms[1],
          ];
        },
      } : false}
    >
      <BetaSchemaForm
        key="detailForm"
        layoutType="Embed"
        columns={dynamicDetailColumns}
      />
    </ModalForm>
  );
};

export default memo(AuditModal);
