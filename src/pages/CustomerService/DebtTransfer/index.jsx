import React, {
  useRef, useEffect, useState, useCallback,
} from 'react';
import { useLocation } from 'umi';
import dayjs from 'dayjs';
// import Watermark from 'antd/es/watermark'; // 如果直接用antd的组件必须这样引入 否则找不到style
import { WaterMark } from '@ant-design/pro-components';
import CommonTableList from '@/components/CommonTableList';
import { getDebtTransferList, getDebtTransferDetail } from '@/services/customerService/debtTransfer';
import { getUser, getImages } from '@/services/customerService/customerCommon';
import AuditModal from './components/AuditModal';

const DebtTransfer = () => {
  const actionRef = useRef();
  const [userInfo, setUserInfo] = useState({}); // 登录用户信息-水印用
  const [modalVisible, setModalVisible] = useState(false); // 审核弹窗控制
  const [detailInfo, setDetailInfo] = useState({}); // 审核详情
  const { query } = useLocation();

  // 获取用户信息（水印用）
  const getUserInfo = async () => {
    const res = await getUser();
    console.log('getUserInfo====', res);
    if (res?.code === '200' && res?.data) {
      setUserInfo({
        ...res.data,
      });
    }
  };

  useEffect(() => {
    getUserInfo();
  }, []);

  // 获取列表数据
  const getTableData = async (params) => {
    const tempParams = {
      page: params.current,
      ...params,
      beginReviewTime: params.reviewTime && params.reviewTime[0],
      endReviewTime: params.reviewTime && params.reviewTime[1],
    };
    delete tempParams.current;
    delete tempParams.reviewTime;

    console.log('tempParams====', tempParams);
    const res = await getDebtTransferList(tempParams);
    if (res?.code === '200' && res?.data) {
      console.log('getDebtTransferList====', res);
      return {
        data: res.data.data,
        total: res.data.totalNum,
      };
    }
    return {
      data: [],
      total: 0,
    };
  };

  // 刷新列表
  const handleReload = useCallback(() => {
    actionRef.current?.reload();
    setDetailInfo({});
  }, [actionRef]);

  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      valueType: 'index',
      key: 'index',
      align: 'center',
      width: 100,
    },
    {
      title: '操作类型',
      dataIndex: 'approveType',
      key: 'approveType',
      ellipsis: true,
      align: 'center',
      width: 150,
      valueEnum: {
        debt_transfer: '债转结清',
        limit_log_off: '额度注销',
        block_user_loan: '屏蔽用户借款权限',
      },
    },
    {
      title: '合作方',
      dataIndex: 'partnerName',
      key: 'partnerName',
      hideInSearch: true,
      ellipsis: true,
      align: 'center',
      width: 180,
    },
    {
      title: 'openId',
      dataIndex: 'openId',
      key: 'openId',
      ellipsis: true,
      copyable: true,
      align: 'center',
      width: 250,
      initialValue: query.openId ? query.openId : null, // 搜索表单默认值
    },
    {
      title: '借据号',
      dataIndex: 'orderNo',
      key: 'orderNo',
      ellipsis: true,
      copyable: true,
      align: 'center',
      width: 250,
    },
    {
      title: '审核状态',
      dataIndex: 'reviewStatus',
      key: 'reviewStatus',
      ellipsis: true,
      align: 'center',
      width: 150,
      valueEnum: {
        0: { text: '待审核', status: 'Warning' },
        1: {
          text: '复核通过',
          status: 'Processing',
        },
        2: {
          text: '复核驳回',
          status: 'Error',
        },
        3: {
          text: '财务驳回',
          status: 'Error',
        },
        4: {
          text: '已入账',
          status: 'Success',
        },
        5: {
          text: '已驳回',
          status: 'Default',
        },
      },
    },
    {
      title: '处理结果',
      dataIndex: 'businessStatus',
      key: 'businessStatus',
      ellipsis: true,
      align: 'center',
      width: 150,
      valueEnum: {
        0: {
          text: '处理中',
          status: 'Warning',
        },
        1: {
          text: '处理成功',
          status: 'Processing',
        },
        2: {
          text: '处理失败',
          status: 'Error',
        },
      },
    },
    {
      title: '详细结果',
      dataIndex: 'businessResult',
      key: 'businessResult',
      ellipsis: true,
      align: 'center',
      width: 150,
    },
    {
      title: '提交人',
      dataIndex: 'submitter',
      key: 'submitter',
      ellipsis: true,
      align: 'center',
      width: 150,
    },
    {
      title: '提交时间',
      dataIndex: 'submitTime',
      key: 'submitTime',
      hideInSearch: true,
      ellipsis: true,
      align: 'center',
      width: 180,
    },
    {
      title: '审核人',
      dataIndex: 'reviewer',
      key: 'reviewer',
      ellipsis: true,
      align: 'center',
      width: 150,
    },
    {
      title: '审核时间',
      dataIndex: 'reviewTime',
      key: 'reviewTime',
      ellipsis: true,
      align: 'center',
      width: 180,
      hideInSearch: true,
    },
    {
      title: '审核时间',
      dataIndex: 'reviewTime',
      key: 'reviewTime',
      width: 180,
      hideInTable: true,
      valueType: 'dateTimeRange',
      fieldProps: {
        showTime: {
          defaultValue: [dayjs('00:00:00', 'HH:mm:ss'), dayjs('23:59:59', 'HH:mm:ss')],
        },
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      hideInSearch: true,
      align: 'center',
      fixed: 'right',
      width: 150,
      render: (text, record) => [
        <a
          key="audit"
          onClick={async () => {
            // 请求详情数据
            const res = await getDebtTransferDetail({ sno: record.sno });
            // 打开审核弹窗
            if (res?.code === '200' && res?.data) {
              const { attachmentUrls } = res.data;
              setDetailInfo(res.data);
              setModalVisible(true);
              if (!attachmentUrls) return;
              const imgRes = await getImages({ attachmentFileIds: attachmentUrls });
              if (imgRes?.code === '200' && imgRes?.data) {
                const { base64Encoders } = imgRes.data;
                setDetailInfo({
                  ...res.data,
                  imgBaseList: base64Encoders,
                });
              }
            }
          }}
        >
          { record.canReview && (record.reviewStatus === 0) ? '审核' : '查看详情'}
        </a>,
      ],
    },
  ];

  return (

    <WaterMark content={`${userInfo?.userId || ''} ${userInfo.userName || ''}`} gapY={111} key="watermark">
      <div style={{ 'min-height': 'calc(-75px + 100vh)' }}>
        <CommonTableList
          key="table"
          search={{ defaultCollapsed: false }}
          columns={columns}
          request={getTableData}
          actionRef={actionRef}
          pagination={{
            showQuickJumper: true,
            defaultPageSize: 10,
          }}
        />
        {/* 审核弹窗 */}
        <AuditModal
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          detailInfo={detailInfo}
          setDetailInfo={setDetailInfo}
          handleReload={handleReload}
        />
      </div>
    </WaterMark>
  );
};

export default DebtTransfer;
