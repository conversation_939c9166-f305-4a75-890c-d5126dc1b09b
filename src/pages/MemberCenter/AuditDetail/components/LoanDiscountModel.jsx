import React, { useState } from 'react';
import { Drawer, Modal } from 'antd';
import { ProCard } from '@ant-design/pro-components';

import LoanDiscountDetail from '../../LoanDiscountConfig/index';
import AuditInfoCard from '@/components/AuditInfoCard';

const ExchangeModel = ({ modalVisible, setVisible, detailData }) => {
  return (
    <Drawer
      title="操作详情"
      width={1500}
      open={modalVisible}
      onClose={() => {
        setVisible(false);
      }}
    >
      <ProCard title="审核详情">
        <LoanDiscountDetail isAudit={true} detailData={detailData} />
      </ProCard>
      <AuditInfoCard itemOperationLog={detailData} />
    </Drawer>
  );
};

export default ExchangeModel;
