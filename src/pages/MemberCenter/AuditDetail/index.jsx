import React, {
  Fragment,
  useState,
  useEffect,
  useMemo,
  useCallback,
} from 'react';
import {
  ProBreadcrumb,
  ProDescriptions,
  ProCard,
  ProTable,
} from '@ant-design/pro-components';
import { Space, Button, Modal, message, Input } from 'antd';
import { useParams } from 'react-router-dom';
import LoanDiscountDetail from '../LoanDiscountConfig/index';
import LoanDiscountModel from './components/LoanDiscountModel';

import { parseDate } from '@/utils/util';
import { Link } from 'umi';

import {
  getLoanDiscountDetail,
  auditLoanDiscountConfig,
  getOperationLogList,
} from '@/services/memberCenter';

const AuditDetail = ({}) => {
  const [loanDiscountModalVisible, setLoanDiscountModalVisible] =
    useState(false);
  const { scene } = useParams();
  const [detailData, setDetailData] = useState(null);
  const [operationLogList, setOperationLogList] = useState([]);
  const [itemOperationLog, setItemOperationLog] = useState([]);
  const [auditRemark, setAuditRemark] = useState('');
  const [showConfirmModal, setShowConfirmModal] = useState(false);
  const [pass, setPass] = useState(false);

  useEffect(() => {
    if (scene === 'loanDiscount') {
      getLoanDiscountDetail().then((res) => {
        if (res && res.code === '200' && res.data) {
          setDetailData(res.data);
          onGetOperationLogList(res.data.id);
        }
      });
    }
  }, []);

  const onGetOperationLogList = async (id) => {
    const params = {
      operationId: id,
      bizCode: scene === 'loanDiscount' ? 'qualificationConfig' : '',
      page: 1,
      pageSize: 10,
    };
    const res = await getOperationLogList(params);
    res && res.code === '200' ? setOperationLogList(res.data.list) : '';
  };

  const onPass = async () => {
    const params = {
      id: detailData.id,
      auditOperation: 2,
      auditRemark: auditRemark || '审核通过',
    };
    const res =
      scene === 'loanDiscount' ? await auditLoanDiscountConfig(params) : '';
    if (res && res.code === '200') {
      setShowConfirmModal(false);
      message.success('审核成功');
      onGetOperationLogList(detailData.id);
    }
  };

  const onReject = async () => {
    const params = {
      id: detailData.id,
      auditOperation: 3,
      auditRemark: auditRemark || '审核失败',
    };
    const res =
      scene === 'loanDiscount' ? await auditLoanDiscountConfig(params) : '';
    if (res && res.code === '200') {
      setShowConfirmModal(false);
      message.success('驳回成功');
      onGetOperationLogList(detailData.id);
    }
  };

  const onAudit = (pass) => {
    setShowConfirmModal(true);
    setPass(pass);
  };

  return (
    <Fragment>
      <Space direction="vertical" size="middle">
        <Link
          to={`/MemberCenter/${
            scene === 'loanDiscount' ? 'LoanDiscountConfig' : ''
          }`}
          key={0}
        >
          返回
        </Link>
        <ProCard title="审核详情">
          {scene === 'loanDiscount' ? (
            <LoanDiscountDetail isAudit={true} />
          ) : (
            ''
          )}
        </ProCard>
        <ProCard>
          <ProTable
            defaultData={[]}
            headerTitle="操作记录"
            search={false}
            options={false}
            dataSource={operationLogList}
            columns={[
              {
                title: '操作编号',
                dataIndex: 'id',
                ellipsis: true,
              },
              {
                title: '操作员',
                dataIndex: 'operatorName',
                ellipsis: true,
              },
              {
                title: '操作内容',
                dataIndex: 'operatorType',
                ellipsis: true,
              },
              {
                title: '操作时间',
                dataIndex: 'createTime',
                ellipsis: true,
                render: (text, record) => (
                  <span>
                    {record.createTime && parseDate(record.createTime)}
                  </span>
                ),
              },
              {
                title: '操作备注',
                dataIndex: 'auditRemark',
                ellipsis: true,
              },
              {
                title: '操作详情',
                dataIndex: 'contentBefore',
                ellipsis: true,
                render: (text, record, _, action) => [
                  <a
                    key="editable"
                    onClick={() => {
                      if (scene === 'loanDiscount') {
                        setLoanDiscountModalVisible(true);
                        setItemOperationLog(record);
                      }
                    }}
                  >
                    查看详情
                  </a>,
                ],
              },
            ]}
            toolBarRender={() => [
              <Button
                key="pass"
                onClick={() => {
                  onAudit(true);
                }}
                type="primary"
              >
                审核通过
              </Button>,
              <Button
                key="reject"
                onClick={() => {
                  onAudit(false);
                }}
                type="primary"
              >
                审核驳回
              </Button>,
            ]}
          />
        </ProCard>
      </Space>
      <LoanDiscountModel
        modalVisible={loanDiscountModalVisible}
        setVisible={setLoanDiscountModalVisible}
        detailData={itemOperationLog}
      />
      {showConfirmModal && (
        <Modal
          title={pass ? '审核通过备注' : '审核驳回意见'}
          open={true}
          onOk={pass ? onPass : onReject}
          onCancel={() => {
            setShowConfirmModal(false);
          }}
        >
          <Input.TextArea
            onChange={(e) => {
              setAuditRemark(e.target.value);
            }}
          />
        </Modal>
      )}
    </Fragment>
  );
};

export default AuditDetail;
