import { useRef, useState, useEffect } from 'react';
import AuditCard from '@/components/AuditCard';
import { Space, message } from 'antd';
import { ProCard, BetaSchemaForm } from '@ant-design/pro-components';
import {
  getLoanDiscountConfig,
  getLoanDiscountDetail,
  submitLoanDiscountConfig,
} from '@/services/memberCenter';
import { validateTrialDuration } from '@/utils/validate';

const LoanDiscountConfig = ({ isAudit, detailData }) => {
  const formRef = useRef();
  const [auditData, setAuditData] = useState({});

  const initData = () => {
    // 弹窗详情内容
    if (detailData) {
      const res = detailData?.contentBefore?.config;
      const configDetail = JSON.parse(res);
      configDetail.maxDeductionAmount = Math.floor(
        configDetail.maxDeductionAmount / 100,
      );
      formRef.current.setFieldsValue(configDetail);
      return;
    }
    // 首页编辑详情 & 查看详情页详情
    const func = isAudit ? getLoanDiscountDetail : getLoanDiscountConfig;
    func().then((res) => {
      const data = res?.data || {};
      setAuditData(data);
      const { discount, maxDeductionAmount, redPacketQuotaId } = data;
      const config = {
        discount,
        maxDeductionAmount,
        redPacketQuotaId,
      };
      formRef.current.setFieldsValue(config);
    });
  };

  const onSubmit = () => {
    formRef.current
      .validateFields()
      .then(async (values) => {
        const data = {
          id: auditData.id,
          ...values,
        };
        const res = await submitLoanDiscountConfig(data);
        if (res && res.code === '200') {
          message.success('提交成功');
        }
      })
      .catch((e) => {
        message.error('请完善必填配置');
        throw e;
      });
  };

  useEffect(() => {
    initData();
  }, [detailData]);

  const columns = [
    {
      title: '利息折扣',
      dataIndex: 'discount',
      formItemProps: {
        extra: '请填0-99之间的整数，如填70则为7折，填0则为免息',
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          { validator: validateTrialDuration },
        ],
      },
      fieldProps: {
        suffix: '%',
      },
      width: 'md',
      colProps: {
        xs: 24,
        md: 12,
      },
    },
    {
      title: '最高减免',
      dataIndex: 'maxDeductionAmount',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          { validator: validateTrialDuration },
        ],
      },
      fieldProps: {
        suffix: '元',
      },
      width: 'md',
      colProps: {
        xs: 24,
        md: 12,
      },
    },
    {
      title: '关联返现红包库存id',
      dataIndex: 'redPacketQuotaId',
      width: 'md',
      colProps: {
        xs: 24,
        sm: 12,
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
  ];

  return (
    <Space direction="vertical" size="middle">
      {!isAudit ? (
        <AuditCard
          title="超级会员借款折扣配置审核"
          onSubmit={onSubmit}
          type="MemberCenter"
          scene="loanDiscount"
          auditData={auditData}
        />
      ) : (
        ''
      )}
      <ProCard title="会员借款折扣">
        <BetaSchemaForm
          layoutType="Form"
          formRef={formRef}
          columns={columns}
          readonly={isAudit}
          submitter={{
            resetButtonProps: {
              style: {
                display: 'none',
              },
            },
            submitButtonProps: {
              style: {
                display: 'none',
              },
            },
          }}
        />
      </ProCard>
    </Space>
  );
};

export default LoanDiscountConfig;
