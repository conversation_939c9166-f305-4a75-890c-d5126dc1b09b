import { Button, Result } from 'antd';
import React from 'react';
import { history } from 'umi';

const NoFoundPage = () => (
  <div
    className="flex f-jc-c f-ai-c"
    style={{ width: '100%', minHeight: 'calc(100vh - 108px)' }}
  >
    <Result
      status="404"
      title="找不到页面了诶"
      subTitle="您访问的地址不存在，请重新输入哦"
      extra={
        <Button type="primary" onClick={() => history.push('/')}>
          返回首页
        </Button>
      }
    />
  </div>
);

export default NoFoundPage;
