import React, { useRef } from 'react';
import { useDrag } from 'react-dnd';
import { MODULE } from '../constant';

const Module = ({ data, components, handleDrop, path, children }) => {
  const ref = useRef(null);

  const [{ isDragging }, drag] = useDrag({
    item: {
      type: MODULE,
      id: data.id,
      path,
    },
    type: MODULE,
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });
  const opacity = isDragging ? 0 : 1;
  drag(ref);

  return (
    <div ref={ref} style={{ opacity }}>
      {children}
    </div>
  );
};

export default Module;
