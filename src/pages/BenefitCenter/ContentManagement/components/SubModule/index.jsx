import React, { useRef } from 'react';
import { useDrag } from 'react-dnd';
import { Button, Form } from 'antd';

import { InputUpload } from 'finance-busi-components-toB';
import { ProCard, ProFormText } from '@ant-design/pro-components';
import DropZone from '../DropZone';
import RichTextEditor from '@/components/RichTextEditor';
import { SUBMODULE } from '../constant';
import { nanoid } from 'nanoid';

const SubModule = ({
  record,
  handleSubModule,
  handleDrop,
  item,
  id,
  subModuleId,
  path,
  currentPath,
  action,
  index,
  subModuleIndex,
  formDisabled,
}) => {
  const ref = useRef(null);

  const [{ isDragging }, drag] = useDrag({
    item: {
      type: SUBMODULE,
      id,
      path,
    },
    type: SUBMODULE,
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const opacity = isDragging ? 0 : 1;
  drag(ref);

  return (
    <div ref={ref} style={{ opacity }} data-path={path}>
      <ProCard
        size="small"
        // collapsible
        bordered
        extra={
          <span>
            <Button
              type="link"
              onClick={() =>
                handleSubModule({
                  subModuleActionType: 'add',
                  type: item.type,
                  index,
                  subModuleIndex,
                })
              }
            >
              新增
            </Button>
            <Button
              type="link"
              danger
              onClick={() =>
                handleSubModule({
                  subModuleActionType: 'delete',
                  type: item.type,
                  index,
                  subModuleIndex,
                })
              }
            >
              删除
            </Button>
          </span>
        }
        style={{
          marginBlockEnd: 8,
        }}
      >
        {item.type === 'title' && (
          <ProFormText
            key="useMode"
            name={[[subModuleIndex], 'content']}
            label="标题"
            rules={[{ required: true, message: '请输入标题' }]}
          />
        )}
        {/* 注意 特殊处理此处name */}
        {item.type === 'image' && (
          <Form.Item
            name={[index, [subModuleIndex], 'content']}
            label="图片"
            rules={[{ required: true, message: '请输入标题' }]}
          >
            <InputUpload
              appKey="finance-operation-admin.vmic.xyz"
              appName="finance-operation-admin"
              limit={{ size: 500 * 1024 }}
            />
          </Form.Item>
        )}
        {item.type === 'richText' && (
          <Form.Item
            name={[index, [subModuleIndex], 'content']}
            label="文字"
            rules={[{ required: true, message: '请输入标题' }]}
          >
            <RichTextEditor disabled={formDisabled} />
          </Form.Item>
        )}
      </ProCard>
    </div>
  );
};

export default SubModule;
