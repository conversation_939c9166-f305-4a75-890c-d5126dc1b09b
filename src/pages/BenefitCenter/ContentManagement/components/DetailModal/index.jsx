import React, {
  memo, useRef, useState, useCallback, useEffect,
} from 'react';
import {
  Button,
  Space,
  notification,
  Drawer,
} from 'antd';
import {
  ProCard,
  BetaSchemaForm,
  ProForm,
  EditableProTable,
} from '@ant-design/pro-components';
import { ConfigProvider } from 'finance-busi-components-toB';
import { titleMap } from '@/constant/config.js';
import {
  saveBenefitContent,
  auditBenefitContent,
  getPlatformInfo,
} from '@/services/benefitCenter.js';
import { getDmpTag } from '@/services/common.js';
import { debounce } from '@/utils/util.js';
import { validateLink, dpRule } from '@/utils/validate.js';
import { nanoid } from 'nanoid';
import dayjs from 'dayjs';
import { tagsConfig, categoryConfig } from '../../../config';
import ContentDetail from '../ContentDetail';
import { mainImageColumn } from './column';

const DetailModal = (props) => {
  const {
    setModalVisible,
    modalVisible,
    actionType,
    setActionType,
    initialConfig = {},
    setInitialConfig,
    handleReload,
    noHistoryTable = false,
    isAutomaticPush = false,
    title,
    operateHistoryRender,
  } = props;
  const formRef = useRef();
  const [tagList, setTagList] = useState([]);
  const [tagOptions, setTagOptions] = useState([]);
  const formDisabled = actionType === 'view' || actionType === 'audit';
  const [mainImageEditableKeys, setMainImageEditableKeys] = useState([]);
  const [platformList, setPlatformList] = useState([]); // 平台列表 详细信息
  const [platformOptions, setPlatformOptions] = useState([]); // 平台名称
  const [currentWelfareType, setCurrentWelfareType] = useState(''); // 福利类型

  useEffect(() => {
    Object.keys(initialConfig).length > 0 && setCurrentWelfareType(initialConfig.welfareType);
  }, [initialConfig]);

  const getPlatformList = async () => {
    const res = await getPlatformInfo();
    if (res?.code === '200' && res.data) {
      const platformOptionList = res.data.map((item) => ({
        label: item.name,
        value: item.name,
      }));
      setPlatformOptions(platformOptionList);
      setPlatformList(res.data);
    }
  };

  const getDmpTagData = async (params) => {
    const res = await getDmpTag(params);
    if (res?.code === '200' && res.data) {
      setTagList(res.data);
      const options = res.data.map((item) => ({
        value: item.id,
        label: item.tagIdWithName,
      }));
      setTagOptions(options);
    }
  };

  // 折扣默认值处理
  const handleDefaultBubbleText = () => {
    const values = formRef.current?.getFieldsValue();
    if (values?.welfareDiscountInfo) {
      const { discountPrice, originPrice, bubbleType } = values?.welfareDiscountInfo;
      if (discountPrice && originPrice && [0, 1].includes(bubbleType)) {
        let bubbleTextDefault = '';
        if (bubbleType === 0) {
          const discountPercentage = ((discountPrice / originPrice) * 10).toFixed(1);
          bubbleTextDefault = `${discountPercentage}折`;
        } else if (bubbleType === 1) {
          const discountAmount = (originPrice - discountPrice).toFixed(1);
          bubbleTextDefault = `省${discountAmount}元`;
        }

        // 将生成的bubbleTextDefault设置到formRef的welfareDiscountInfo的bubbleText字段中
        formRef.current.setFieldValue(
          ['welfareDiscountInfo', 'bubbleText'],
          bubbleTextDefault,
        );
      }
    }
  };

  // 卡片预览
  const handlePreview = useCallback(
    (data) => {
      try {
        const previewIframe = document.getElementById('previewIframe');
        const previewIframe1 = document.getElementById('previewIframe1');

        const values = formRef.current.getFieldsValue();
        const { welfareDiscountInfo, platformName } = data || values;

        // 数据转化处理
        const temData = data ? { ...data } : { ...values };
        if (welfareDiscountInfo) {
          temData.welfareDiscountInfo = {
            ...welfareDiscountInfo,
            discountPrice: welfareDiscountInfo.discountPrice
              ? welfareDiscountInfo.discountPrice * 100
              : undefined,
            originPrice: welfareDiscountInfo.originPrice
              ? welfareDiscountInfo.originPrice * 100
              : undefined,
          };
        }
        if (Array.isArray(platformName)) {
          temData.platformName = platformName[0];
        }
        if (previewIframe1) {
          previewIframe1?.contentWindow?.postMessage(
            JSON.stringify(temData),
            '*',
          );
          return;
        }
        if (previewIframe) {
          previewIframe?.contentWindow?.postMessage(
            JSON.stringify(temData),
            '*',
          );
        }
      } catch (e) {
        console.log('预览方法报错===', e);
      }
    },
    [initialConfig, modalVisible],
  );

  // 详情页预览
  const handleDetailPreview = useCallback(
    (data) => {
      const previewDetailIframe = document.getElementById(
        'previewDetailIframe',
      );
      const previewDetailIframe1 = document.getElementById(
        'previewDetailIframe1',
      );

      const values = formRef.current.getFieldsValue();
      const { welfareDiscountInfo, platformName } = data || values;
      // 数据转化处理
      const temData = data && Object.keys(data).length > 0 ? { ...data } : { ...values };
      if (welfareDiscountInfo) {
        temData.welfareDiscountInfo = {
          ...welfareDiscountInfo,
          discountPrice: welfareDiscountInfo.discountPrice
            ? welfareDiscountInfo.discountPrice * 100
            : undefined,
          originPrice: welfareDiscountInfo.originPrice
            ? welfareDiscountInfo.originPrice * 100
            : undefined,
        };
      }
      if (Array.isArray(platformName)) {
        temData.platformName = platformName[0];
      }
      if (previewDetailIframe1) {
        previewDetailIframe1?.contentWindow?.postMessage(
          JSON.stringify(temData),
          '*',
        );
        return;
      }
      if (previewDetailIframe) {
        previewDetailIframe?.contentWindow?.postMessage(
          JSON.stringify(temData),
          '*',
        );
      }
    },
    [initialConfig, modalVisible],
  );

  const infoColumns = [
    {
      title: '营销标签',
      dataIndex: 'marketingTagList',
      valueType: 'select',
      fieldProps: {
        mode: 'tags',
        maxTagTextLength: 6,
        maxCount: 1,
        showCount: true,
        placeholder: '请输入营销标签，不超过6个字',
      },
      formItemProps: {
        labelCol: { span: isAutomaticPush ? 3 : 4 },
        wrapperCol: { span: 20 },
        extra: '单个营销标签字数不超过6个字',
      },
    },
    {
      title: '福利主标题',
      dataIndex: 'welfareMainTitle',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
      },
      fieldProps: {
        showCount: true,
      },
    },
    {
      title: '福利副标题',
      dataIndex: 'welfareSubTitle',
      fieldProps: {
        maxLength: 16,
        showCount: true,
      },
      formItemProps: {
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
        rules: [
          {
            type: 'string',
            max: 16,
          },
        ],
      },
    },
    {
      valueType: 'dependency',
      name: ['welfareType'],
      columns: ({ welfareType }) => (welfareType === 0
        ? [
          {
            title: '折后价（元）',
            dataIndex: ['welfareDiscountInfo', 'discountPrice'],
            valueType: 'digit',
            transform: (value) => value * 100,
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
                ({ getFieldValue }) => ({
                  validator() {
                    const { welfareDiscountInfo } = getFieldValue();
                    if (
                      welfareDiscountInfo?.discountPrice
                      && welfareDiscountInfo?.originPrice
                      && welfareDiscountInfo?.discountPrice
                      > welfareDiscountInfo?.originPrice
                    ) {
                      return Promise.reject(
                        new Error('折后价不能高于原价'),
                      );
                    }
                    return Promise.resolve();
                  },
                }),
              ],
              labelCol: { span: 4 },
              wrapperCol: { span: 20 },
              onChange: () => {
                handleDefaultBubbleText();
              },
            },
            fieldProps: {
              precision: 2,
              min: 0,
              max: 100000000,
              style: { width: '100%' },
            },
          },
          {
            title: '原价（元）',
            dataIndex: ['welfareDiscountInfo', 'originPrice'], // 转成分
            valueType: 'digit',
            transform: (value) => value * 100,
            formItemProps: () => ({
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
                ({ getFieldValue }) => ({
                  validator() {
                    const { welfareDiscountInfo } = getFieldValue();
                    if (
                      welfareDiscountInfo?.discountPrice
                      && welfareDiscountInfo?.originPrice
                      && welfareDiscountInfo?.discountPrice
                      > welfareDiscountInfo?.originPrice
                    ) {
                      return Promise.reject(
                        new Error('折后价不能高于原价'),
                      );
                    }
                    return Promise.resolve();
                  },
                }),
              ],
              labelCol: { span: 4 },
              wrapperCol: { span: 20 },
              onChange: () => {
                handleDefaultBubbleText();
              },
            }),
            fieldProps: {
              precision: 2,
              min: 0,
              max: 100000000,
              style: { width: '100%' },
            },
          },
          {
            title: '优惠气泡',
            dataIndex: ['welfareDiscountInfo', 'bubbleType'],
            valueType: 'radio',
            valueEnum: {
              discount: '折扣',
              reduction: '立减',
            },
            initialValue: 'discount',
            fieldProps: {
              options: [
                { label: '折扣', value: 0 },
                { label: '立减', value: 1 },
              ],
            },
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
              ],
              labelCol: { span: 4 },
              wrapperCol: { span: 20 },
              onChange: () => {
                handleDefaultBubbleText();
              },
            },
          },
          {
            valueType: 'dependency',
            name: ['welfareDiscountInfo'],
            columns: ({ welfareDiscountInfo }) => [
              {
                title: '气泡文案',
                dataIndex: ['welfareDiscountInfo', 'bubbleText'],
                formItemProps: () => ({
                  rules: [
                    {
                      required: true,
                      message: '此项为必填项',
                    },
                  ],
                  extra:
                    welfareDiscountInfo?.bubbleType === 0
                      ? '【折后价/原价】折， 超出6字大字模式显示异常'
                      : '【省n元】，超出6字大字模式显示异常',
                }),
                fieldProps: {
                  maxLength: 6,
                  showCount: true,
                  placeholder:
                    welfareDiscountInfo?.bubbleType === 0
                      ? '【折后价/原价】折'
                      : '【省n元】',
                },
              },
            ],
          },
        ]
        : [
          {
            title: '利益点',
            dataIndex: ['welfareDiscountInfo', 'benefitPoint'],
            formItemProps: {
              rules: [
                {
                  type: 'string',
                  max: 10,
                },
              ],
              labelCol: { span: 4 },
              wrapperCol: { span: 20 },
              extra:
                '为保证大字模式展示效果，请配置小于等于6个字，2个数字或符号=1个字',
            },
            fieldProps: {
              maxLength: 10,
              showCount: true,
            },
          },
        ]),
    },
  ];

  /**
 * 重置数据 关闭弹窗
 */
  const resetDialog = () => {
    setTagList([]);
    setTagOptions([]);
    setModalVisible(false);
    setActionType('');
    setInitialConfig({});
    setCurrentWelfareType('');
    // 刷新列表
    handleReload && handleReload();
  };

  // 获取平台标签

  useEffect(() => {
    getPlatformList();
  }, []);

  // 主图编辑设置
  useEffect(() => {
    if ((initialConfig?.mainImageList || [])?.length > 0) {
      setMainImageEditableKeys(
        initialConfig.mainImageList.map((item) => item.id),
      );
    }
  }, [initialConfig]);

  useEffect(() => {
    initialConfig?.dmpConfigInfo?.dmpId
      && getDmpTagData({ tagId: Number(initialConfig.dmpConfigInfo.dmpId) });
    if (!modalVisible) return;

    const previewIframe = document.getElementById('previewIframe');
    previewIframe
      && (previewIframe.onload = () => {
        handlePreview(initialConfig);
      });

    const previewIframe1 = document.getElementById('previewIframe1');
    previewIframe1
      && (previewIframe1.onload = () => {
        handlePreview(initialConfig);
      });

    // 操作历史(区分页面俩iframe)
    const previewDetailIframe = document.getElementById('previewDetailIframe');
    previewDetailIframe
      && (previewDetailIframe.onload = () => {
        handleDetailPreview(initialConfig);
      });

    const previewDetailIframe1 = document.getElementById('previewDetailIframe1');
    previewDetailIframe1
      && (previewDetailIframe1.onload = () => {
        handleDetailPreview(initialConfig);
      });
  }, [initialConfig, modalVisible]);

  const dmpColumns = [
    {
      title: 'DMP ID',
      dataIndex: ['dmpConfigInfo', 'dmpId'],
      valueType: 'select',
      fieldProps: (form) => ({
        showSearch: true,
        allowClear: true,
        options: tagOptions,
        onClear: () => {
          setTagOptions([]);
          setTagList([]);
          form.setFieldValue(['dmpConfigInfo', 'dmpType'], undefined);
        },
        onSearch: debounce((value) => {
          value && getDmpTagData({ tagId: value });
        }, 500),
        onChange: (value) => {
          const item = tagList.find((tagItem) => tagItem.id === value);
          if (item) {
            const { dimension } = item;
            form.setFieldValue(['dmpConfigInfo', 'dmpType'], dimension);
          }
        },
        placeholder: '请输入dmpId',
      }),
    },
    {
      title: 'DMP取反',
      dataIndex: ['dmpConfigInfo', 'dmpInverse'],
      valueType: 'radio',
      fieldProps: {
        options: [
          { label: '否', value: false },
          { label: '是', value: true },
        ],
      },
      initialValue: false,
    },
    {
      title: 'DMP类型',
      dataIndex: ['dmpConfigInfo', 'dmpType'],
      valueType: 'radio',
      fieldProps: {
        options: [
          { label: 'imei', value: 1 },
          { label: 'openId', value: 2 },
        ],
        disabled: true,
      },
    },
  ];

  const basicColumns = [
    categoryConfig,
    {
      ...tagsConfig,
      fieldProps: (form) => ({
        ...tagsConfig.fieldProps,
        onChange: (value) => {
          let platformInfo = null;
          value.find((tagId) => {
            platformInfo = platformList.find((platformItem) => platformItem.id === tagId);
            if (platformInfo) {
              return true;
            }
            return false;
          });
          form.setFieldValue('platformName', platformInfo?.name || []);
        },
      }),
    },
    {
      valueType: 'dependency',
      name: ['tagIdList'],
      columns: () => [
        {
          title: '平台名称',
          dataIndex: 'platformName',
          key: 'platformName',
          valueType: 'select',
          fieldProps: () => ({
            showSearch: true,
            allowClear: true,
            options: platformOptions,
          }),
          formItemProps: {
            extra:
              '如需跳端则必填，可优化用户体验，同时还在C端卡片展示角标，选项无对应平台名称联系产品处理',
          },
        },
      ],
    },
    {
      title: '福利有效期',
      valueType: 'dateRange',
      dataIndex: 'productActiveRange',
      hideInTable: true,

      fieldProps: {
        presets: [
          {
            label: '7天',
            value: [dayjs(), dayjs().add(7, 'd')],
          },
          {
            label: '14天',
            value: [dayjs(), dayjs().add(14, 'd')],
          },
          {
            label: '30天',
            value: [dayjs(), dayjs().add(30, 'd')],
          },
        ],
      },
      initialValue: [dayjs(), dayjs().add(7, 'd')],
    },
    {
      title: '商品链接(DP)',
      dataIndex: 'productLink',
      formItemProps: {
        rules: [
          {
            validator: validateLink,
          },
          dpRule,
        ],
        extra:
          '从自建详情页跳出到站外的链接，如未填写，点击行动按钮默认返回二级页',
      },
      fieldProps: {
        disabled: formDisabled || isAutomaticPush || initialConfig.autoPush, // 自动化流程置灰
      },
    },
    {
      title: '选品id',
      dataIndex: 'crawlerPoolId',
      valueType: 'digit',
      fieldProps: {
        min: 1,
        precision: 0,
        style: { width: '100%' },
        disabled: formDisabled || initialConfig.crawlerPoolId, // 有id之后不允许修改
      },
      formItemProps: {
        extra: '此字段用于关联选品池内容, 填入内容池序号',
      },
    },
    {
      valueType: 'dependency',
      name: ['platformName'],
      columns: ({ platformName }) => [{
        title: '商品id',
        dataIndex: 'productId',
        key: 'productId',
        align: 'center',
        fieldProps: {
          disabled: formDisabled || initialConfig.productId, // 有id之后不允许修改
        },
        formItemProps: {
          extra: <span style={{ color: 'red' }}>如非自动推送，运营手动自建，此字段需填写， 用于获取可分佣转链链接</span>,
          rules: [
            {
              required: !!(platformName === '拼多多' || platformName === '京东') && currentWelfareType === 0,
              message: '此项为必填项',
            },
          ],
        },
      }],
    },
  ];

  const detailedColumns = [
    {
      title: '福利类型',
      dataIndex: 'welfareType',
      valueType: 'select',
      fieldProps: (form) => ({
        options: [
          { label: '交易类', value: 0 },
          { label: '资讯类', value: 1 },
        ],
        onSelect: (value) => {
          const btnTextMap = {
            0: '去看看',
            1: '立即领',
          };
          form.setFieldValue('buttonDTO', {
            text: btnTextMap[value],
          });
          if (value === 1) {
            form.setFieldValue('productId', '');
          }
          setCurrentWelfareType(value);
        },
      }),
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '福利卡图',
      dataIndex: 'cardLogoUrl',
      key: 'cardLogoUrl',
      valueType: 'inputUpload',
      fieldProps: {
        placeholder:
          '请输入图片链接，或点击右边上传，图片限制宽高一致，大小150kb以内',
        limit: {
          size: 150 * 1024,
          type: ['png', 'jpg', 'jpeg'],
        },
        appKey: 'finance-operation-admin.vmic.xyz',
        appName: 'finance-operation-admin',
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '详情页主图',
      dataIndex: 'mainImageList',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      renderFormItem: () => (
        <EditableProTable
          columns={mainImageColumn}
          rowKey="id"
          name="mainImageList"
          bordered
          scroll={{
            y: 300,
          }}
          initialValue={initialConfig.mainImageList}
          recordCreatorProps={{
            newRecordType: 'dataSource',
            record: () => ({
              id: nanoid(),
            }),
            creatorButtonText: '新增一行',
          }}
          minLength={1}
          maxLength={6}
          editable={{
            type: 'multiple',
            editableKeys: mainImageEditableKeys,
            actionRender: (_, __, defaultDoms) => [defaultDoms.delete],
            onChange: setMainImageEditableKeys,
          }}
        />
      ),
    },
    {
      title: '福利信息',

      renderFormItem: () => (
        <ProCard
          // title="可折叠"
          bordered
          size="small"
          collapsible
        >
          <BetaSchemaForm
            layoutType="Embed"
            columns={
              isAutomaticPush
                ? infoColumns.slice(1, infoColumns.length)
                : infoColumns
            }
          />
        </ProCard>
      ),
    },
    {
      title: '开启评论区',
      dataIndex: 'commentOpen',
      valueType: 'radio',
      initialValue: 1,
      fieldProps: {
        options: [
          { label: '是', value: 1 },
          { label: '否', value: 2 },
        ],
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '行动按钮',
      dataIndex: ['buttonDTO', 'text'],
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      fieldProps: {
        maxLength: 3,
        showCount: true,
      },
    },
  ];

  // 表单提交
  const handleSubmit = async () => {
    const values = await formRef.current.validateFieldsReturnFormatValue();
    const {
      contentDetail = {},
      platformName,
      productLink,
      mainImageList,
      productActiveRange,
    } = values;
    if (!values.platformName) {
      values.platformName = '';
      values.platformInfo = null;
    }
    // 福利有效期 时间入参处理
    if (productActiveRange) {
      values.startValidity = productActiveRange[0];
      values.endValidity = productActiveRange[1];
      delete values.productActiveRange;
    }
    //  dpLink存在的情况下 平台名称必填
    if (productLink && !platformName) {
      return notification.error({
        message: '提交失败',
        description: '填写deepLink链接的情况下必须填写平台名称',
      });
    }
    // 主图不能为空
    const errorImage = mainImageList.find((item) => !item.mainImage);
    if (errorImage) {
      return notification.error({
        message: '提交失败',
        description: '主图不能为空',
      });
    }

    const empTyModule = contentDetail.moduleList?.find((item) => !item.length);
    if (empTyModule) {
      return notification.error({
        message: '提交失败',
        description: '模块内容不能为空',
      });
    }
    values.contentDetail = {
      ...initialConfig.contentDetail,
      ...contentDetail,
    };
    const params = {
      ...initialConfig,
      ...values,
    };
    if (!values.welfareDiscountInfo) {
      params.welfareDiscountInfo = null;
    }

    // console.log({ params, initialConfig, values })
    delete params[2];
    if (['add', 'copy'].includes(actionType)) {
      delete params.id;
    }
    if (['copy'].includes(actionType)) {
      delete params.autoPush;
    }
    if (!params?.dmpConfigInfo?.dmpId) {
      delete params.dmpConfigInfo;
    }
    if (Array.isArray(values.platformName)) {
      params.platformName = values.platformName[0];
    }
    const res = await saveBenefitContent(params);
    if (res?.code === '200') {
      notification.success({
        message: '提交成功',
        description: isAutomaticPush
          ? `【${params.welfareMainTitle}】已自动推送至内容管理，请注意及时审核并进行相关页面关联配置`
          : '',
      });
      resetDialog();
    }
    return true;
  };

  // 修改审核状态
  const handleAudit = async (params) => {
    const res = await auditBenefitContent(params);

    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
      });
      resetDialog();
    }
  };

  return (
    <Drawer
      width={1400}
      title={title || titleMap[actionType] || ''}
      onClose={() => resetDialog()}
      open={modalVisible}
      destroyOnClose
      footer={(
        <Space Space>
          <Button
            onClick={() => {
              resetDialog(false);
            }}
          >
            {actionType === 'view' ? '关闭' : '取消'}
          </Button>
          {['edit', 'add', 'copy'].includes(actionType) && (
            <>
              <Button
                type="primary"
                onClick={() => {
                  handleSubmit();
                }}
              >
                提交
              </Button>
            </>
          )}
          {actionType === 'audit' && (
            <>
              <Button
                type="primary"
                onClick={() => {
                  handleAudit({ id: initialConfig?.id, auditOperation: 3 });
                }}
              >
                审核驳回
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  handleAudit({ id: initialConfig?.id, auditOperation: 2 });
                }}
              >
                审核通过
              </Button>
            </>
          )}
        </Space>
      )}
    >
      <div
        style={{
          height: '100%',
          overflow: 'hidden',
          position: 'relative',
          display: 'flex',
          paddingBottom: 20,
        }}
      >
        <div
          style={{
            height: '100%',
            flex: 1,
            overflow: 'scroll',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <ProCard colSpan="65%" style={{ height: '100%', overflow: 'hidden' }}>
            <div
              style={{ height: '100%', overflow: 'scroll', paddingBottom: 10 }}
            >
              <ConfigProvider>
                <ProForm
                  onValuesChange={() => {
                    handlePreview();
                    handleDetailPreview();
                  }}
                  disabled={formDisabled}
                  formRef={formRef}
                  layout="horizontal"
                  labelCol={{ span: 3 }}
                  wrapperCol={{ span: 20 }}
                  submitter={false}
                  initialValues={initialConfig}
                >
                  {isAutomaticPush && (
                    <>
                      <h2 style={{ marginBottom: 10 }}>
                        自定义字段（除原价外，均非必填，如无需配置，可核对自动字段后直接提交）
                      </h2>
                      <BetaSchemaForm
                        layoutType="Embed"
                        columns={[infoColumns[0], ...dmpColumns]}
                      />
                      <h2>自动字段（支持自定义修改)</h2>
                    </>
                  )}
                  <h3>基础配置</h3>
                  <BetaSchemaForm
                    layoutType="Embed"
                    columns={
                      isAutomaticPush
                        ? basicColumns
                        : [...basicColumns, ...dmpColumns]
                    }
                  />
                  <h3>详情配置</h3>
                  <BetaSchemaForm
                    layoutType="Embed"
                    columns={detailedColumns}
                  />
                  {/* 详情页编辑 */}
                  <ContentDetail
                    formDisabled={formDisabled}
                    formRef={formRef}
                  />
                </ProForm>
              </ConfigProvider>
            </div>
          </ProCard>
        </div>
        {/* 福利卡片预览 */}
        <div style={{ width: 375 }}>
          <ProCard title="福利卡片预览" colSpan="30%">
            <div
              style={{
                // height: 'calc(100vh - 200px)',
                height: '120px',
                'pointer-events': 'none',
              }}
            >
              {!noHistoryTable && (
                <iframe
                  style={{
                    width: '100%',
                    height: '120px',
                    display: 'block',
                    overflow: 'scroll',
                    border: '1px solid #cccccd',
                  }}
                  id="previewIframe"
                  src="//m.vivojrkj.com/favour/home/<USER>"
                  title="page1"
                />
              )}
              {noHistoryTable && (
                <iframe
                  style={{
                    width: '100%',
                    height: '120px',
                    display: 'block',
                    overflow: 'scroll',
                    border: '1px solid #cccccd',
                  }}
                  id="previewIframe1"
                  src="//m.vivojrkj.com/favour/home/<USER>"
                  title="page2"
                />
              )}
            </div>
          </ProCard>
          <ProCard title="详情页预览" colSpan="30%">
            <div
              style={{
                height: 'calc(100vh - 420px)',
              }}
            >
              {!noHistoryTable && (
                <iframe
                  style={{
                    width: '100%',
                    height: 'calc(100vh - 420px)',
                    display: 'block',
                    overflow: 'scroll',
                    border: '1px solid #cccccd',
                  }}
                  id="previewDetailIframe"
                  // src="//10.15.49.114:8080/home/<USER>"
                  src="//m.vivojrkj.com/favour/home/<USER>"
                  title="page3"
                />
              )}
              {noHistoryTable && (
                <iframe
                  style={{
                    width: '100%',
                    height: 'calc(100vh - 420px)',
                    display: 'block',
                    overflow: 'scroll',
                    border: '1px solid #cccccd',
                  }}
                  id="previewDetailIframe1"
                  // src="//10.15.49.114:8080/home/<USER>"
                  src="//m.vivojrkj.com/favour/home/<USER>"
                  title="page4"
                />
              )}
            </div>
          </ProCard>
        </div>
      </div>
      {/* 操作记录 */}
      {!noHistoryTable && (actionType === 'audit' || actionType === 'view') && (
        operateHistoryRender({ initialConfig })
      )}
    </Drawer>
  );
};

export default memo(DetailModal);
