import React from 'react';
import classNames from 'classnames';
import { useDrop } from 'react-dnd';
import { MODULE, SUBMODULE } from '../constant';
import './index.less';

const ACCEPTS = [MODULE, SUBMODULE];

const DropZone = ({ data, onDrop, isLast, className }) => {
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: ACCEPTS,
    drop: (item, monitor) => {
      onDrop(data, item);
    },
    canDrop: (item, monitor) => {
      const dropZonePath = data.path;
      const splitDropZonePath = dropZonePath?.split('-');
      const itemPath = item.path;
      const splitItemPath = itemPath.split('-');
      const dropZonePathModuleIndex = splitDropZonePath[0];
      const itemPathModuleIndex = splitItemPath[0];
      const diffModule = dropZonePathModuleIndex !== itemPathModuleIndex;

      // 父模块不能移到子模块
      const parentDropInChild = splitItemPath.length < splitDropZonePath.length;
      if (parentDropInChild) return false;

      // 不能垮模块移动
      if (
        diffModule &&
        (splitDropZonePath.length === 2 || splitItemPath.length === 2)
      ) {
        return false;
      }

      // 自身位置不动
      if (itemPath === dropZonePath) return false;
      // 自身前后两个zone不能移动
      if (
        splitItemPath.length === 1 &&
        splitDropZonePath.length === 1 &&
        Number(dropZonePathModuleIndex) === Number(itemPathModuleIndex) + 1
      )
        return;

      if (Number(splitDropZonePath[1]) === Number(splitItemPath[1]) + 1) {
        return false;
      }

      // 子模块移动
      return true;
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  const isActive = isOver && canDrop;
  return (
    <div
      data-path={data.path}
      className={classNames('dropZone', { active: isActive, isLast })}
      ref={drop}
    />
  );
};
export default DropZone;
