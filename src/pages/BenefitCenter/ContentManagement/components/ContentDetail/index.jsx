import React, { memo, useRef, useState, useCallback, useEffect } from 'react';
import {
  Button,
  Popover,
  Image,
  Input,
  Divider,
  Space,
  Popconfirm,
  notification,
  Spin,
  Drawer,
  Form,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import {
  ProCard,
  BetaSchemaForm,
  ProFormSelect,
  ProForm,
  ProFormList,
  ProFormText,
} from '@ant-design/pro-components';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import DropZone from '../DropZone';
import { nanoid } from 'nanoid';
import Module from '../Module';
import SubModule from '../SubModule';

const ContentDetail = (props) => {
  const { formDisabled, formRef } = props;
  const actionRef = useRef();

  // 处理拖拽
  const handleDrop = (dropZone, item) => {
    const splitDropZonePath = dropZone.path.split('-');
    const splitItemPath = item.path.split('-');
    const zoneModuleIndex = Number(splitDropZonePath[0]);
    const itemModuleIndex = Number(splitItemPath[0]);

    if (splitDropZonePath?.length === 1 && splitItemPath?.length === 1) {
      // 模块移动
      if (zoneModuleIndex > itemModuleIndex) {
        actionRef.current?.move(itemModuleIndex, zoneModuleIndex - 1);
      } else {
        actionRef.current?.move(itemModuleIndex, zoneModuleIndex);
      }
    } else {
      // 模块内部子模块移动
      const currentModule = actionRef?.current.get?.(zoneModuleIndex);

      let element = currentModule.splice(splitItemPath[1], 1); // 从索引移除一个元素
      if (splitDropZonePath[1] - 0 > splitItemPath[1] - 0) {
        currentModule.splice(splitDropZonePath[1] - 1, 0, element[0]); // 在索引插入被移除的元素 向下移动 插入在移动位置的前一个
      } else {
        currentModule.splice(splitDropZonePath[1], 0, element[0]); // 在索引插入被移除的元素
      }
      const tempDetail = formRef.current.getFieldValue([
        'contentDetail',
        'moduleList',
      ]);
      tempDetail[zoneModuleIndex] = currentModule;
      formRef.current.setFieldValue(
        ['contentDetail', 'moduleList'],
        tempDetail,
      );
    }
  };

  // 详情页配置
  const handleSubModule = ({
    subModuleActionType,
    type,
    index,
    subModuleIndex,
  }) => {
    const currentModule = actionRef?.current.get?.(index);
    const newSubModule = { type, content: '' };
    // 添加子模块
    if (subModuleActionType === 'add') {
      if (!subModuleIndex && subModuleIndex !== 0) {
        // 不指定位置
        currentModule.push(newSubModule);
      } else {
        // 放到对应模块底下
        currentModule.splice(subModuleIndex + 1, 0, newSubModule);
      }
    } else {
      // 删除子模块
      currentModule.splice(subModuleIndex, 1);
    }
    const tempDetail = formRef.current.getFieldValue([
      'contentDetail',
      'moduleList',
    ]);
    tempDetail[index] = currentModule;
    formRef.current.setFieldValue(['contentDetail', 'moduleList'], tempDetail);
  };

  // 渲染子模块
  const renderSuModule = (params) => {
    return <SubModule {...params} key={params.id} />;
  };
  return (
    <>
      {/* 详情页编辑 */}
      <DndProvider backend={HTML5Backend}>
        <ProFormList
          name={['contentDetail', 'moduleList']}
          label="福利详情"
          tooltip="拖动模块可调整排序，模块内二级模块也可拖动排序"
          initialValue={[
            [
              { type: 'title', content: '' },
              { type: 'richText', content: '' },
              {
                type: 'image',
                content: '',
              },
            ],
          ]}
          min={1}
          creatorButtonProps={{
            creatorButtonText: '添加模块',
          }}
          creatorRecord={[
            { type: 'title', content: '' },
            { type: 'richText', content: '' },
            { type: 'image', content: '' },
          ]}
          itemRender={({ listDom, action }, { record, index }) => {
            const currentPath = `${index}`;
            const length = formRef?.current?.getFieldValue('detail')?.length;
            const id = nanoid();
            return (
              <>
                <React.Fragment key={id}>
                  <DropZone
                    data={{
                      path: currentPath,
                    }}
                    onDrop={handleDrop}
                    path={currentPath}
                  />
                  <div>
                    <Module
                      data={{
                        path: currentPath,
                        id: id,
                      }}
                      path={currentPath}
                    >
                      {
                        <ProCard
                          // collapsible
                          // defaultCollapsed
                          bordered
                          extra={action}
                          title={`模块${index + 1}`}
                          style={{
                            marginBlockEnd: 8,
                          }}
                        >
                          <Space style={{ marginBottom: '15px' }}>
                            <Button
                              type="primary"
                              icon={<PlusOutlined />}
                              onClick={() =>
                                handleSubModule({
                                  subModuleActionType: 'add',
                                  type: 'title',
                                  index,
                                })
                              }
                            >
                              标题
                            </Button>
                            <Button
                              type="primary"
                              icon={<PlusOutlined />}
                              onClick={() =>
                                handleSubModule({
                                  subModuleActionType: 'add',
                                  type: 'richText',
                                  index,
                                })
                              }
                            >
                              文字
                            </Button>
                            <Button
                              type="primary"
                              icon={<PlusOutlined />}
                              onClick={() =>
                                handleSubModule({
                                  subModuleActionType: 'add',
                                  type: 'image',
                                  index,
                                })
                              }
                            >
                              图片
                            </Button>
                          </Space>
                          {listDom}
                        </ProCard>
                      }
                    </Module>
                  </div>
                  {index + 1 === length && (
                    <DropZone
                      data={{
                        path: `${index + 1}`,
                        id: nanoid(),
                      }}
                      onDrop={handleDrop}
                      isLast
                    />
                  )}
                </React.Fragment>
              </>
            );
          }}
          actionRef={actionRef}
        >
          {(meta, index, action, count) => {
            const record = action.getCurrentRowData();
            return record.map((item, subModuleIndex) => {
              const currentPath = `${index}-${subModuleIndex}`;
              const subModuleId = nanoid();
              return (
                <React.Fragment key={subModuleId}>
                  <DropZone
                    data={{
                      path: currentPath,
                      id: subModuleId,
                    }}
                    onDrop={handleDrop}
                  />
                  {/* 渲染子模块 */}
                  {renderSuModule({
                    record,
                    handleSubModule,
                    handleDrop,
                    item,
                    subModuleId,
                    path: currentPath,
                    id: subModuleId,
                    currentPath,
                    action,
                    index,
                    subModuleIndex,
                    formDisabled,
                  })}
                  {subModuleIndex + 1 === record.length && (
                    <DropZone
                      data={{
                        path: `${index}-${record.length}`,
                        id: nanoid(),
                      }}
                      onDrop={handleDrop}
                      isLast
                    />
                  )}
                </React.Fragment>
              );
            });
          }}
        </ProFormList>
      </DndProvider>
    </>
  );
};

export default memo(ContentDetail);
