import React, {
  useRef,
  useState,
  useCallback,
} from 'react';
import { useLocation } from 'umi';
import {
  Button,
  Divider,
  Space,
  Popconfirm,
  notification,
  Spin,
  Tooltip,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import {
  ProTable,
  ModalForm,
  BetaSchemaForm,
} from '@ant-design/pro-components';
import {
  getBenefitContentList,
  benefitContentOnline,
  getBenefitContentDetail,
  addContentTags,
  saveBenefitPriority,
  getProductInfo,
} from '@/services/benefitCenter';
import DetailModal from './components/DetailModal';
import OperateHistory from './components/OperateHistory';

import { tagsConfig, categoryConfig } from '../config';

const ContentManagement = () => {
  const actionRef = useRef();
  const [modalVisible, setModalVisible] = useState(false);
  const [actionType, setActionType] = useState('');
  const [initialConfig, setInitialConfig] = useState({});
  const [loading, setLoading] = useState(false);
  const [tagModalVisible, setTagsModalVisible] = useState(false);
  const [selectedIds, setSelectedIds] = useState([]);
  // 快速创建
  const location = useLocation();
  const searchParams = new URLSearchParams(location.query);
  const vipStatus = searchParams.get('vipStatus');

  /**
 * 列表刷新
 */
  const handleReload = () => {
    actionRef.current?.reload();
  };

  // 活动上下线
  const changeOnlineStatus = async (idList, onlineStatus) => {
    const params = {
      idList,
      onlineOperation: onlineStatus,
    };
    const res = await benefitContentOnline(params);
    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
        description: res?.message || '',
      });
      handleReload();
    }
  };

  // 操作
  const handleAction = async (record, actionTypeProp) => {
    const { id } = record;
    if (id) {
      // 请求详情信息
      setLoading(true);
      const res = await getBenefitContentDetail({ idList: [id] });
      if (res?.code === '200' && res.data) {
        const { welfareDiscountInfo, startValidity, endValidity } = res.data[0];
        let tempWelfareDiscountInfo = {};
        if (welfareDiscountInfo) {
          const { discountPrice, originPrice } = welfareDiscountInfo;
          tempWelfareDiscountInfo = {
            ...welfareDiscountInfo,
            discountPrice: discountPrice / 100,
            originPrice: originPrice / 100,
          };
        }

        const tempRecord = {
          ...res.data[0],
          welfareDiscountInfo: tempWelfareDiscountInfo,
        };
        if (['add', 'copy'].indexOf(actionTypeProp) !== -1) {
          delete tempRecord.autoPush;
          delete tempRecord.id;
          delete tempRecord.crawlerPoolId;
          delete tempRecord.productId;
          delete tempRecord.couponInfo;
          delete tempRecord.skuId;
          delete tempRecord.platformCommission;
        }
        if (startValidity && endValidity) {
          tempRecord.productActiveRange = [startValidity, endValidity];
        }
        setInitialConfig(tempRecord);
        setModalVisible(true);
      }
      setLoading(false);
    } else {
      setInitialConfig(record);
      setLoading(false);
      setModalVisible(true);
    }
    setActionType(actionTypeProp);
  };

  const columns = [
    {
      title: '权重',
      dataIndex: 'priority',
      key: 'priority',
      tooltip: '权重越高，排序越靠前',
      hideInSearch: true,
      ellipsis: true,
      width: 100,
      align: 'center',
      fixed: 'left',
      render: (_, record) => (
        <ModalForm
          title="权重弹窗"
          modalProps={{
            destroyOnClose: true,
          }}
          layout="horizontal"
          onFinish={async (values) => {
            const params = {
              id: record.id,
              ...values,
            };
            const res = await saveBenefitPriority(params);
            if (res?.code === '200') {
              handleReload();
              notification.success({
                message: '提交成功',
              });
              return true;
            }
            notification.error({
              message: res?.message || res?.msg,
            });
            return false;
          }}
          trigger={<Button type="link">{record.priority}</Button>}
          initialValues={record}
        >
          <BetaSchemaForm
            layoutType="Embed"
            columns={[
              {
                title: '权重排序',
                dataIndex: 'priority',
                key: 'priority',
                valueType: 'digit',
                fieldProps: {
                  precision: 0,
                  min: 0,
                  style: { width: '100%' },
                },
                formItemProps: {
                  extra: '数值越高，权重越大，C端展示越靠前',
                  rules: [
                    {
                      required: true,
                      message: '此项为必填项',
                    },
                  ],
                },
              },
            ]}
          />
        </ModalForm>
      ),
    },
    {
      title: '内容ID',
      dataIndex: 'id',
      key: 'id',
      hideInSearch: true,
      ellipsis: true,
      width: 100,
      align: 'center',
      fixed: 'left',
    },
    {
      title: '福利主标题',
      dataIndex: 'welfareMainTitle',
      key: 'welfareMainTitle',
      ellipsis: true,
      width: 200,
      align: 'center',
    },
    {
      title: '福利类型',
      dataIndex: 'welfareType',
      key: 'welfareType',
      valueType: 'select',
      valueEnum: { 0: '交易类', 1: '咨询类' },
      ellipsis: true,
      width: 150,
      align: 'center',
    },
    tagsConfig,
    {
      title: '福利标签',
      dataIndex: 'tagNameList',
      key: 'tagNameList',
      search: false,
      ellipsis: true,
      width: 250,
      align: 'center',
      render: (text, record) => (
        <Tooltip placement="top" title={record.tagNameList?.join(',')}>
          {record.tagNameList?.join(',')}
        </Tooltip>
      ),
    },
    categoryConfig,
    {
      title: '福利类目',
      dataIndex: 'categoryName',
      key: 'categoryName',
      search: false,
      ellipsis: true,
      width: 200,
      align: 'center',
    },
    {
      title: '自动推送',
      dataIndex: 'autoPush',
      key: 'autoPush',
      ellipsis: true,
      align: 'center',
      valueType: 'select',
      width: 150,
      valueEnum: { 0: '否', 1: '是' },
    },
    {
      title: '更新人',
      dataIndex: 'operatorName',
      key: 'operatorName',
      ellipsis: true,
      width: 150,
      align: 'center',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      valueType: 'dateTime',
      hideInSearch: true,
      ellipsis: true,
      align: 'center',
      width: 200,
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      key: 'auditStatus',
      ellipsis: true,
      width: 150,
      align: 'center',
      valueEnum: {
        1: { text: '待审核', status: 'Processing' },
        2: { text: '审核通过', status: 'Success' },
        3: { text: '审核驳回', status: 'Error' },
      },
    },
    {
      title: '上架状态',
      dataIndex: 'onlineStatus',
      key: 'onlineStatus',
      valueEnum: {
        1: { text: '禁用', status: 'Error' },
        2: { text: '启用', status: 'Success' },
      },
      ellipsis: true,
      width: 150,
      align: 'center',
    },
    {
      title: '列表排序',
      dataIndex: 'listSort',
      key: 'listSort',
      valueType: 'select',
      fieldProps: {
        options: [
          { label: '权重倒序', value: 1 },
          { label: '更新时间倒序', value: 2 },
        ],
      },
      initialValue: 2,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      hideInSearch: true,
      align: 'center',
      fixed: 'right',
      width: 300,
      render: (text, record) => (
        <Space split={<Divider type="vertical" />} key={record.id}>
          {record.onlineStatus !== 2 && (
            <Button
              key="edit"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'edit');
              }}
            >
              编辑
            </Button>
          )}
          {[2, 3].includes(record.auditStatus) && (
            <Button
              key="detail"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'view');
              }}
            >
              详情
            </Button>
          )}
          {record.auditStatus === 1 && (
            <Button
              key="audit"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'audit');
              }}
            >
              审核
            </Button>
          )}

          {record.onlineStatus === 2 && (
            <Popconfirm
              title="是否下架"
              description="下线操作会导致C端用户不可见，确认要下架吗？"
              onConfirm={() => changeOnlineStatus([record.id], 1)}
              key="offline"
              onCancel={() => { }}
              okText="确定"
              cancelText="取消"
            >
              <Button
                key="offlinebtn"
                type="link"
                size="small"
                onClick={() => { }}
              >
                下架
              </Button>
            </Popconfirm>
          )}
          {record.onlineStatus === 1 && record.auditStatus === 2 && (
            <Button
              key="online"
              type="link"
              size="small"
              onClick={() => changeOnlineStatus([record.id], 2)}
            >
              上架
            </Button>
          )}
          <Button
            key="copy"
            type="link"
            size="small"
            onClick={() => {
              handleAction(record, 'copy');
            }}
          >
            复制
          </Button>
        </Space>
      ),
    },
  ];

  const fastEditColumn = [
    {
      title: '参数类型',
      valueType: 'radio',
      dataIndex: 'paramsType',
      key: 'paramsType',
      fieldProps: {
        options: [
          { label: 'id', value: 'ids' },
          { label: 'url', value: 'url' },
        ],
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      initialValue: 'ids',
    },
    {
      title: '参数',
      valueType: 'params',
      dataIndex: 'params',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
        extra:
          '输入什么值得买的商品id或者什么值得买的商品链接， 如‘https://www.smzdm.com/p/111690366’，或者‘111690366’',
      },
    },
  ];

  // 批量添加标签
  const batchAddColumns = [
    tagsConfig,
    {
      title: '营销标签',
      dataIndex: 'marketingTagList',
      valueType: 'select',
      fieldProps: {
        mode: 'tags',
        maxTagTextLength: 6,
        maxCount: 1,
        showCount: true,
        placeholder: '请输入营销标签，不超过6个字',
      },
      formItemProps: {
        extra: '单个营销标签字数不超过6个字',
      },
    },
  ];
  // 获取列表数据
  const getTableData = useCallback(async (params) => {
    const param = {
      page: params.current,
      ...params,
    };
    delete param.current;

    try {
      const res = await getBenefitContentList(param);
      if (res.code === '200') {
        const { data } = res;
        return {
          data: data.list || [],
          total: data.total || 0,
        };
      }
      return {
        data: [],
        total: 0,
      };
    } catch (error) {
      return {
        data: [],
        total: 0,
      };
    }
  }, []);

  // 批量操作
  const handleBatchOperation = (selectedRowKeys, selectedRows, btnType) => {
    if (selectedRowKeys?.length < 1) {
      return notification.error({
        message: '请在下方列表选择需要操作的福利',
      });
    }
    if (btnType === 'online') {
      const tempRows1 = selectedRows
        .filter((item) => !(item.onlineStatus === 1 && item.auditStatus === 2))
        .map((item) => item.id);
      if (tempRows1.length) {
        return notification.error({
          message: '错误提示',
          description: `内容id为${tempRows1.join(
            ',',
          )}的内容不可上架，请选择未上架状态且审核通过的内容`,
        });
      }
      changeOnlineStatus(selectedRowKeys, 2);
    }

    if (btnType === 'offline') {
      const tempRows2 = selectedRows
        .filter((item) => item.onlineStatus !== 2)
        .map((item) => item.id);
      if (tempRows2.length) {
        return notification.error({
          message: '错误提示',
          description: `内容id为${tempRows2.join(
            ',',
          )}的内容不可下架，请选择下架状态的内容`,
        });
      }
      changeOnlineStatus(selectedRowKeys, 1);
    }

    if (btnType === 'addTags') {
      setTagsModalVisible(true);
      setSelectedIds(selectedRowKeys);
    }
    return true;
  };

  // 操作记录render
  const operateHistoryRender = (props) => (
    <OperateHistory
      key="operateHistory"
      id={props.initialConfig?.id}
      bizCode="welfareContent"
      disabled={false}
    />
  );

  return (
    <>
      <Spin spinning={loading}>
        <ProTable
          columns={columns}
          rowSelection={{
            // 自定义选择项参考: https://ant.design/components/table-cn/#components-table-demo-row-selection-custom
            // 注释该行则默认不显示下拉选项
            // selections: [Table.SELECTION_ALL, Table.SELECTION_INVERT],
            // defaultSelectedRowKeys: [1],
            alwaysShowAlert: true,
          }}
          search={{
            defaultCollapsed: false,
          }}
          tableAlertRender={({
            selectedRowKeys,
            selectedRows,
          }) => (
            <Space size={24}>
              <Button
                type="primary"
                icon={<PlusOutlined />}
                onClick={() => handleAction({}, 'add')}
              >
                新建
              </Button>
              {vipStatus && (
                <ModalForm
                  title="创建什么值得买商品"
                  trigger={(
                    <Button type="primary">
                      <PlusOutlined />
                      快速创建
                    </Button>
                  )}
                  autoFocusFirstInput
                  modalProps={{
                    destroyOnClose: true,
                  }}
                  layout="horizontal"
                  labelCol={{ span: 3 }}
                  wrapperCol={{ span: 20 }}
                  onFinish={async (values) => {
                    const params = {};
                    params[values.paramsType] = values.params;
                    const res = await getProductInfo(params);
                    if (res?.code === 0 && res?.data) {
                      const { marketingTagList, welfareType } = res.data;
                      let text;
                      if (welfareType === 0) {
                        text = '去看看';
                      } else if (welfareType === 1) {
                        text = '立即领';
                      } else {
                        text = '';
                      }

                      const tempData = {
                        ...res.data,
                        buttonDTO: {
                          text,
                        },
                      };
                      if (marketingTagList?.length > 1) {
                        tempData.marketingTagList = marketingTagList.slice(
                          0,
                          1,
                        );
                      }

                      setTimeout(() => {
                        handleAction(tempData, 'add');
                      }, 300);
                      return true;
                    }
                    notification.error({
                      message: '快速创建失败',
                      description: '请确认链接/id是否正确',
                    });
                    return false;
                  }}
                >
                  <BetaSchemaForm
                    layoutType="Embed"
                    columns={fastEditColumn}
                  />
                </ModalForm>
              )}
              <>
                <Button
                  type="primary"
                  onClick={() => handleBatchOperation(
                    selectedRowKeys,
                    selectedRows,
                    'addTags',
                  )}
                >
                  批量添加标签
                </Button>
                <Popconfirm
                  title="确定将选中福利批量上架吗"
                  onConfirm={() => {
                    handleBatchOperation(
                      selectedRowKeys,
                      selectedRows,
                      'online',
                    );
                  }}
                  key="online"
                  onCancel={() => { }}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button type="primary" onClick={() => { }}>
                    批量上架
                  </Button>
                </Popconfirm>

                <Popconfirm
                  title="确定将选中福利批量下架吗"
                  description="下线操作会导致C端用户不可见，确认要下架吗？"
                  onConfirm={() => {
                    handleBatchOperation(
                      selectedRowKeys,
                      selectedRows,
                      'offline',
                    );
                  }}
                  key="offline"
                  onCancel={() => { }}
                  okText="确定"
                  cancelText="取消"
                >
                  <Button key="offlinebtn" type="primary" onClick={() => { }}>
                    批量下架
                  </Button>
                </Popconfirm>
              </>
            </Space>
          )}
          alwaysShowAlert
          tableAlertOptionRender={({ selectedRowKeys }) => (
            <Space size={24}>
              <span>
                已选
                {selectedRowKeys.length}
                {' '}
                项
              </span>
            </Space>
          )}
          scroll={{ x: '100%' }}
          options={false}
          pagination={{
            pageSize: 10,
            showSizeChanger: false,
            showTotal: (total) => `共${total}项`,
          }}
          request={getTableData}
          rowKey="id"
          toolBarRender={() => [<></>]}
          actionRef={actionRef}
        />
      </Spin>
      <DetailModal
        setModalVisible={setModalVisible}
        modalVisible={modalVisible}
        setActionType={setActionType}
        actionType={actionType}
        handleReload={handleReload}
        setInitialConfig={setInitialConfig}
        initialConfig={initialConfig}
        operateHistoryRender={operateHistoryRender}
      />
      {/*  批量上架 */}
      <ModalForm
        key="tags"
        title="批量添加标签"
        modalProps={{
          destroyOnClose: true,
          onCancel: () => setTagsModalVisible(false),
        }}
        layout="horizontal"
        onFinish={async (values) => {
          const res = await addContentTags({ ...values, idList: selectedIds });
          if (res?.code === '200') {
            notification.success({
              message: '操作成功',
            });
            handleReload();
            setTagsModalVisible(false);
          }
        }}
        open={tagModalVisible}
      >
        <BetaSchemaForm layoutType="Embed" columns={batchAddColumns} />
      </ModalForm>
    </>
  );
};

export default ContentManagement;
