import React, { useEffect, useState } from 'react';
import { ContainerFilled, DribbbleCircleFilled } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import { getCategories, getTags } from '@/services/benefitCenter';
import HomepageConfig from './components/HomepageConfig';
import Tree from './components/tree';
import CommissionConfig from '../CommissionConfig';

const ConfigManagement = () => {
  const [tagTree, setTagTree] = useState([]);
  const [categoryTree, setCategoryTree] = useState([]);

  const handleGetTags = () => {
    getTags().then((res) => {
      if (res && res.data) {
        setTagTree(res.data);
      }
    });
  };
  const handleCategories = () => {
    getCategories().then((res) => {
      if (res && res.data) {
        setCategoryTree(res.data);
      }
    });
  };
  useEffect(() => {
    handleGetTags();
    handleCategories();
  }, []);

  return (
    <>
      <CommissionConfig actionType="edit" />
      <HomepageConfig />
      <ProCard
        style={{
          marginBlockEnd: 10,
        }}
        split="vertical"
        bordered
        headerBordered
      >
        <ProCard
          title={(
            <div>
              <ContainerFilled />
              {' '}
              类目管理
            </div>
          )}
          colSpan="50%"
          tooltip="只允许同级节点拖拽交换顺序，拖拽后立即生效"
        >
          <Tree
            treeType="category"
            treeData={categoryTree}
            onUpdate={handleCategories}
          />
        </ProCard>
        <ProCard
          title={(
            <div>
              <DribbbleCircleFilled />
              {' '}
              标签管理
            </div>
          )}
          colSpan="50%"
          tooltip="只允许同级节点拖拽交换顺序，拖拽后立即生效"
        >
          <Tree treeType="tag" treeData={tagTree} onUpdate={handleGetTags} />
        </ProCard>
      </ProCard>
    </>
  );
};

export default ConfigManagement;
