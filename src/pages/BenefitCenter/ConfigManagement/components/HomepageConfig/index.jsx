import React, {
  memo, useEffect, useCallback, useRef, useState,
} from 'react';
import {
  BetaSchemaForm,
  ProCard,
  ProForm,
  ProFormList,
  ProFormSelect,
  ProTable,
} from '@ant-design/pro-components';
import {
  Image, notification, Button, Popconfirm,
} from 'antd';
import { ConfigProvider } from 'finance-busi-components-toB';
import { PlusOutlined } from '@ant-design/icons';
import {
  getHomepageConfig,
  saveHomepageConfig,
} from '@/services/benefitCenter.js';
import { nanoid } from 'nanoid';
import { dmpConfig } from '../../../config';
import AdModal from '../AdModal';
import ProductModal from '../../../PageManagement/components/ProductModal';

const HomepageConfig = () => {
  const formRef = useRef();
  const [adModalVisible, setAdModalVisible] = useState(false);
  const [adModalData, setAdModalData] = useState({}); // 广告弹窗内容 含模块序号 行号
  const [productModalVisible, setProductModalVisible] = useState(false);
  const [autoFilterCondition, setAutoFilterCondition] = useState({});
  const [welfareContentList, setWelfareContentList] = useState([]); // 选中的品
  const [productData, setProductData] = useState({}); // 选品弹窗 模块序号 行号

  // 获取配置
  const getConfigData = useCallback(async () => {
    const res = await getHomepageConfig();
    if (res?.code === '200' && res?.data) {
      formRef.current?.setFieldsValue(res.data);
    }
  }, []);

  useEffect(() => {
    getConfigData();
  }, []);

  // 广告位 删除 上移 下移
  const handelListChange = (type, adModuleIndex, rowIndex) => {
    const recommendDTOList = formRef?.current.getFieldValue('recommendDTOList');
    if (!recommendDTOList) return; // 处理recommendDTOList可能为空的情况
    const updatedList = JSON.parse(JSON.stringify(recommendDTOList));

    if (type === 'delete') {
      updatedList[adModuleIndex].itemList.splice(rowIndex, 1); // 直接操作原数组，删除对应的item
    } else {
      const list = updatedList[adModuleIndex].itemList;
      const targetIndex = type === 'moveUp' ? rowIndex - 1 : rowIndex + 1;
      if (list[targetIndex]) {
        [list[rowIndex], list[targetIndex]] = [list[targetIndex], list[rowIndex]]; // 交换两个元素位置
      }
    }

    formRef?.current.setFieldValue('recommendDTOList', updatedList);
  };

  // 弹窗关闭
  const handleModalClose = (modalType) => {
    // 选品弹窗 'product' 广告弹窗 'modal'
    if (modalType === 'product') {
      setAutoFilterCondition(null);
      setWelfareContentList([]);
      setProductData({});
    } else {
      setAdModalData({});
    }
  };

  // 广告位弹窗
  const showAdModal = ({
    adModuleIndex, record, rowIndex, type,
  }) => {
    setAdModalData({
      adModuleIndex,
      rowIndex,
      adValues: record || { type },
    });
    setAdModalVisible(true);
  };

  // 广告位新增/编辑弹窗
  const handleAdChange = (values) => {
    const { adModuleIndex, rowIndex, adValues } = values;
    const recommendDTOList = formRef?.current.getFieldValue('recommendDTOList');
    const updatedList = JSON.parse(JSON.stringify(recommendDTOList));
    let currentList = updatedList[adModuleIndex].itemList;
    if (rowIndex !== undefined) {
      currentList = currentList.map((item, i) => {
        if (i === rowIndex) {
          return {
            ...item,
            ...adValues,
          };
        }
        return item;
      });
      updatedList[adModuleIndex].itemList = currentList;
      formRef?.current.setFieldValue('recommendDTOList', updatedList);
      return true;
    }
    if (currentList?.length > 5) {
      notification.error({
        message: '新增失败',
        description: '单个位置最高配置5个',
      });
      return false;
    }
    currentList.push(adValues);
    updatedList[adModuleIndex].itemList = currentList;
    formRef?.current.setFieldValue('recommendDTOList', updatedList);
    return true;
  };

  // 选品弹窗
  const showProductModal = ({
    adModuleIndex, record, rowIndex, type,
  }) => {
    setProductData({
      adModuleIndex,
      rowIndex,
      type,
    });
    const content = record?.contentDTO;
    setWelfareContentList(content ? [content] : []);
    setAutoFilterCondition(null);
    setProductModalVisible(true);
  };

  // 选品弹窗选中
  const handleProductSelected = (values) => {
    const { checkedCondition, selectedProductList } = values;
    if (checkedCondition || selectedProductList.length !== 1) {
      return notification.error({
        message: '操作失败',
        description:
          '请选择单个商品',
      });
    }
    const { adModuleIndex, rowIndex } = productData;

    const recommendDTOList = formRef?.current.getFieldValue('recommendDTOList');
    if (!recommendDTOList) return; // 处理recommendDTOList可能为空的情况
    const updatedList = JSON.parse(JSON.stringify(recommendDTOList));
    let currentList = updatedList[adModuleIndex].itemList;
    if (rowIndex !== undefined) {
      const newItem = {
        type: 1,
        contentDTO: selectedProductList[0],
      };
      currentList = currentList.map((item, i) => {
        if (i === rowIndex) {
          return {
            ...item,
            ...newItem,
          };
        }
        return item;
      });
      updatedList[adModuleIndex].itemList = currentList;
      formRef?.current.setFieldValue('recommendDTOList', updatedList);
    } else if (currentList?.length >= 5) {
      notification.error({
        message: '新增失败',
        description: '单个位置最高配置5个',
      });
    } else {
      const newItem = {
        id: nanoid(),
        contentDTO: selectedProductList[0],
        type: 1,
      };
      currentList.push(newItem);
      updatedList[adModuleIndex].itemList = currentList;
      formRef?.current.setFieldValue('recommendDTOList', updatedList);
    }
  };

  const hasDuplicatePositions = (arr) => {
    for (let i = 0; i < arr.length; i++) {
      for (let j = i + 1; j < arr.length; j++) {
        if (arr[i].position === arr[j].position) {
          return true; // 如果找到重复的position，返回true
        }
      }
    }
    return false; // 如果没有重复的position，则返回false
  };

  const getAdColumns = (adModuleIndex) => [
    {
      title: '排序',
      dataIndex: 'index',
      key: 'index',
      valueType: 'indexBorder',
      width: 50,
      align: 'center',
    },
    {
      title: '内容类型',
      dataIndex: 'type',
      key: 'type',
      valueType: 'select',
      width: 130,
      ellipsis: true,
      editable: false,
      fieldProps: {
        options: [
          { label: '福利单品', value: 1 },
          { label: '广告位', value: 2 },
        ],
      },
      align: 'center',
    },
    {
      title: '福利内容',
      key: 'content',
      // editable: false,
      render: (text, record) => (
        <span>{record.type === 1 ? record.contentDTO?.welfareMainTitle : record.adBannerDTO?.redirectUrl}</span>
      ),

      renderFormItem: (schema, config) => (config.record.type === 1 ? (
        <a onClick={() => showProductModal({
          adModuleIndex, rowIndex: schema.index, type: 1, record: config.record,
        })}
        >
          {config?.record?.contentDTO?.welfareMainTitle ? `${config.record.contentDTO.welfareMainTitle}` : '选品'}
        </a>
      ) : (
        <a onClick={() => {
          showAdModal({
            adModuleIndex, rowIndex: schema.index, type: 2, record: config.record,
          });
        }}
        >
          {config?.record?.adBannerDTO?.redirectUrl ? `${config.record.adBannerDTO.redirectUrl}` : '配置'}
        </a>
      )),
      align: 'center',
    },
    {
      title: '活动时间',
      valueType: 'dateTimeRange',
      dataIndex: 'termOfValidity',
      key: 'termOfValidity',
      align: 'center',
      render: (text, record) => (
        <span>{record.termOfValidity ? `${record.termOfValidity[0]}-${record.termOfValidity[1]}` : ''}</span>
      ),
    },
    {
      title: '操作',
      width: 180,
      key: 'option',
      valueType: 'option',
      align: 'center',
      render: (text, record, index, action) => [
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        >
          编辑
        </a>,
        <Popconfirm
          title="确认删除吗？"
          onConfirm={() => {
            handelListChange('delete', adModuleIndex, index);
          }}
          key="delete"
          onCancel={() => { }}
          okText="确定"
          cancelText="取消"
        >
          <a key="delete" type="link" size="small">
            删除
          </a>
        </Popconfirm>,
        <a
          key="link3"
          onClick={() => {
            handelListChange('moveUp', adModuleIndex, index);
          }}
        >
          上移
        </a>,
        <a key="link4" onClick={() => handelListChange('moveDown', adModuleIndex, index)}>下移</a>,
      ],
    },
  ];

  const columns = [
    {
      title: '首页模块活动氛围图（常规屏）',
      dataIndex: 'commonActivityPicUrl',
      key: 'commonActivityPicUrl',
      valueType: 'inputUpload',
      fieldProps: {
        limit: {
          width: 960,
          height: 150,
          size: 150 * 1024,
          type: ['png'],
        },
        appKey: 'finance-operation-admin.vmic.xyz',
        appName: 'finance-operation-admin',
      },
      formItemProps: {
        extra:
          '图片尺寸，960px * 150px，png格式，150kb以内，图片不符合要求不允许上传',
      },
    },
    {
      title: '首页模块活动氛围图（折叠屏）',
      dataIndex: 'foldingScreenActivityPicUrl',
      key: 'foldingScreenActivityPicUrl',
      valueType: 'inputUpload',
      fieldProps: {
        limit: {
          width: 1800,
          height: 150,
          size: 150 * 1024,
          type: ['png'],
        },
        appKey: 'finance-operation-admin.vmic.xyz',
        appName: 'finance-operation-admin',
      },
      formItemProps: {
        extra:
          '图片尺寸，1800px * 150px，png格式，150kb以内，图片不符合要求不允许上传',
      },
    },
    {
      ...dmpConfig,
      title: '首页模块活动氛围图DMP',
    },
    {
      title: '首页瀑布流广告位',
      /* eslint-disable */
      renderFormItem: (schema, config, form) => {
        return <ProFormList
          name="recommendDTOList"
          creatorButtonProps={{
            creatorButtonText: '添加瀑布流广告位',
          }}
          min={0}
          max={3}
          copyIconProps={false}
          itemRender={({ listDom, action }) => (
            <ProCard
              bordered
              style={{ marginBlockEnd: 8 }}
              extra={action}
              bodyStyle={{ paddingBlockEnd: 0 }}
            >
              {listDom}
            </ProCard>
          )}
          creatorRecord={{ position: null, itemList: [] }}
          initialValue={[]}
        >
          {(f, index) => {
            const recommendDTOList = form.getFieldValue('recommendDTOList') || []
            return (
              <>
                <ProFormSelect
                  style={{ padding: 0 }}
                  width="md"
                  name="position"
                  label="展示位置"
                  extra="此字段为在瀑布流中的展示顺序"
                  options={[{ label: 1, value: 1 }, { label: 7, value: 7 }, { label: 13, value: 13 }]}
                  rules={[{ required: true, message: '此项为必填项', }]}
                />
                <ProForm.Item style={{ marginBlockEnd: 0 }} label="内容配置" >
                  <ProTable
                    columns={getAdColumns(index)}
                    cardBordered
                    editable={{
                      type: 'multiple',
                      cancelText: () => { <></> },
                      onSave: (rowIndex, row, originRow) => {
                        const curRecommendDTOList = formRef?.current.getFieldValue('recommendDTOList')
                        let updatedList = JSON.parse(JSON.stringify(curRecommendDTOList))
                        updatedList = curRecommendDTOList.map((item, recommendDTOIndex) => {
                          if (index === recommendDTOIndex) {
                            const tempItem = {
                              ...item,
                              itemList: item.itemList.map((listItem, itemIndex) => {
                                if (itemIndex === row.index) return row
                                return listItem
                              })
                            }
                            return tempItem
                          } else {
                            return item
                          }
                        })

                        formRef.current.setFieldValue('recommendDTOList', updatedList)
                      },
                      onDelete: (key, row) => {
                        const curAdModuleIndex = (form.getFieldValue('recommendDTOList') || []).findIndex((recommendDTO) => {
                          const cur = recommendDTO.itemList.find((record) => {
                            return record.id === key
                          })
                          return cur ? true : false
                        })
                        handelListChange('delete', curAdModuleIndex, row.index)
                      }
                    }}
                    rowKey="id"
                    search={false}
                    options={false}
                    dataSource={recommendDTOList[index]?.itemList}
                    pagination={false}
                    dateFormatter="string"
                    toolBarRender={() => {
                      return [
                        <Button
                          key="button"
                          icon={<PlusOutlined />}
                          onClick={() => {
                            showProductModal({ adModuleIndex: index, type: 1 })
                          }}
                          type="primary"
                        >
                          爆款福利
                        </Button>,
                        <Button
                          key="button"
                          icon={<PlusOutlined />} // eslint-disable-line
                          onClick={() => {
                            showAdModal({ adModuleIndex: index, type: 2 })
                          }}
                          type="primary"
                        >
                          广告位
                        </Button>
                      ]
                    }}
                  />
                </ProForm.Item>
              </>
            )
          }}
        </ProFormList>
      }
      /* eslint-enable */
    },
  ];

  return (
    <ConfigProvider>
      <ProCard
        title="福利首页配置"
        bordered
        style={{
          marginBlockEnd: 10,
        }}
        tooltip={(
          <Image
            width={200}
            src="https://gaia-xy.vivo.com.cn/wallet/polaris/d205e7b0-05d5-11ef-80eb-7100b3ae1c18.jpg.webp"
          />
        )}
      >
        <BetaSchemaForm
          formRef={formRef}
          layout="horizontal"
          onFinish={async (values) => {
            const { commonActivityPicUrl, foldingScreenActivityPicUrl, recommendDTOList } = values;
            if (!!commonActivityPicUrl !== !!foldingScreenActivityPicUrl) {
              notification.error({
                message: '操作失败',
                description:
                  '请确认常规屏和折叠屏图片配置状态一致，不可只配置一个',
              });
              return;
            }

            const hasDuplicatePosition = hasDuplicatePositions(recommendDTOList);
            if (hasDuplicatePosition) {
              notification.error({
                message: '操作失败',
                description:
                  '瀑布流展示位置重复',
              });
              return;
            }

            const hasEmptyPosition = recommendDTOList.find((item) => !item.itemList?.length);
            if (hasEmptyPosition) {
              notification.error({
                message: '操作失败',
                description:
                  '瀑布流广告位不能为空',
              });
              return;
            }

            const res = await saveHomepageConfig(values);
            if (res?.code === '200') {
              notification.success({
                message: '操作成功',
              });
              getConfigData();
            }
          }}
          columns={columns}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
          submitter={{
            resetButtonProps: {
              style: {
                // 隐藏重置按钮
                display: 'none',
              },
            },
            searchConfig: {
              submitText: '应用',
            },
          }}
        />
      </ProCard>

      {/* 广告位配置 */}
      <AdModal modalVisible={adModalVisible} setModalVisible={setAdModalVisible} onClose={() => handleModalClose('ad')} adModalData={adModalData} onSubmit={handleAdChange} />
      {/* 选品弹窗 */}
      <ProductModal
        setModalVisible={setProductModalVisible}
        modalVisible={productModalVisible}
        welfareContentList={welfareContentList}
        setWelfareContentList={setWelfareContentList}
        autoFilterCondition={autoFilterCondition}
        setAutoFilterCondition={setAutoFilterCondition}
        handleProductSelected={handleProductSelected}
      />
    </ConfigProvider>
  );
};

export default memo(HomepageConfig);
