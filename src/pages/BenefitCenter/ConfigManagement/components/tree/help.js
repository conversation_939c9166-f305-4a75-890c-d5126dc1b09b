export const treeData = [
  {
    title: 'parent 1',
    key: '0-0',
    children: [
      {
        title: 'parent 1-0',
        key: '0-0-0',
        children: [
          {
            title: 'leaf1',
            key: '0-0-0-0',
            isLeaf: true,
          },
          {
            title: 'leaf2',
            key: '0-0-0-1',
            isLeaf: true,
          },
          {
            title: 'leaf3',
            key: '0-0-0-2',
            isLeaf: true,
          },
        ],
      },
      {
        title: 'parent 1-1',
        key: '0-0-1',
        children: [
          {
            title: 'leaf4',
            key: '0-0-1-0',
            isLeaf: true,
          },
        ],
      },
      {
        title: 'parent 1-2',
        key: '0-0-2',
        children: [
          {
            title: 'leaf5',
            isLeaf: true,
            key: '0-0-2-0',
          },
          {
            title: 'leaf6',
            key: '0-0-2-1',
            isLeaf: true,
          },
        ],
      },
    ],
  },
];

export const isSameLevel = (a, b) => {
  const aLevel = a.props.pos.split('-').length;
  const bLevel = b.props.pos.split('-').length;

  return aLevel === bLevel;
};

export const isSameParent = (a, b) => {
  const aLevel = a.props.pos.split('-');
  const bLevel = b.props.pos.split('-');
  aLevel.pop();
  bLevel.pop();
  return aLevel.join('') === bLevel.join('');
};
export const isDropToFirst = (a, b) => {
  const aLevel = a.props.pos.split('-');
  const bLevel = b.props.pos.split('-');
  aLevel.pop();
  return aLevel.join('') === bLevel.join('');
};

export const getIdList = (data, dropPos) => {
  // console.log('getIdList data===', data, dropPos, data.map(item => item.id), 'dropPos.length', dropPos.length)
  if (!dropPos) return [];
  if (dropPos.length <= 1) {
    return data.map((item) => item.id);
  } else {
    const index = dropPos[0];
    dropPos.shift();
    return getIdList(data[index].children, dropPos);
  }
};
