import React, { useState, useEffect } from 'react';
import { isSameLevel, isSameParent, isDropToFirst, getIdList } from './help';
import { Tree, Space, Button, Notification, Spin } from 'antd';
import { ModalForm, ProFormText } from '@ant-design/pro-components';
import {
  updateTagOrCategory,
  updateTagOrCategoryPriority,
} from '@/services/benefitCenter';

const VTree = ({ treeType, treeData, onUpdate }) => {
  const [tData, setData] = useState([]);
  const [modalProps, setModalProps] = useState({ open: false });
  const [loading, setLoading] = useState(false);
  const [key, setKey] = useState(Math.random());
  const [expandedKeys, setExpandedKeys] = useState([0]);
  // 组件初始化
  useEffect(() => {
    let _temp = [{ key: 'root', title: 'root', children: treeData }];
    _temp = _handleDataRecursion(_temp);
    !loading &&
      Array.isArray(treeData) &&
      treeData[0] &&
      setExpandedKeys([treeData[0].key]);
    setData(_temp);
    setKey(Math.random());
    setLoading(false);
  }, [treeData]);
  // 设置弹窗属性
  const _setModalProps = ({ type, isClear = false, value }) => {
    if (isClear) {
      setModalProps({ open: false });
      return;
    }
    if (type === 'delete') return;
    let title = '',
      label = '',
      fatherLabel = '';
    switch (type) {
      case 'edit':
        title += '编辑';
        break;
      case 'add':
        title += '新增';
        break;
      case 'addSon':
        title += '新增子';
        fatherLabel += '父';
    }
    switch (treeType) {
      case 'category':
        title += '类目';
        label += '类目名称';
        fatherLabel = fatherLabel ? '父类目' : '';
        break;
      case 'tag':
        title += '标签';
        label += '标签名称';
        fatherLabel = fatherLabel ? '父标签' : '';
    }
    setModalProps({
      ...modalProps,
      type,
      title,
      open: true,
      label,
      fatherLabel,
      fatherTitle: value?.title,
      nodeData: { ...value },
    });
  };
  // 节点增删改查
  const onNodeOp = ({ type = 'add', value = {} }) => {
    const _callbackMap = {
      add: () => {},
      addSon: () => {},
      edit: () => {},
    };
    _callbackMap[type]();
    _setModalProps({ type, value });
  };
  // 节点标题定制
  const titleRender = (item) => {
    if (!item) return;
    const { floor, lastNode } = item;
    const addSonTitle = treeType === 'category' ? '新增子类目' : '新增子标签';
    const edit = floor !== 0 && (
      <Button
        type="link"
        onClick={() => onNodeOp({ type: 'edit', value: item })}
      >
        编辑
      </Button>
    );
    const addSon = floor !== 3 && (
      <Button
        type="link"
        onClick={() => onNodeOp({ type: 'addSon', value: item })}
      >
        {addSonTitle}
      </Button>
    );
    const add = floor === 1 && lastNode && (
      <Button
        type="link"
        danger
        onClick={() => onNodeOp({ type: 'add', value: item })}
      >
        新增
      </Button>
    );
    return (
      <Space size="large" split>
        {item.title}
        <Space.Compact block size="small">
          {addSon}
          {edit}
        </Space.Compact>
      </Space>
    );
  };
  // 节点数据初始化处理
  const _handleDataRecursion = (tData, floor = 0) => {
    tData.forEach((item, index) => {
      item.key = item.id || 'root';
      item.title = item.name || 'root';
      item.floor = floor;
      item.lastNode = index === tData.length - 1;
      if (item.children && item.children.length !== 0) {
        _handleDataRecursion(item.children, floor + 1);
      } else {
        item.isLeaf = true;
      }
    });
    return tData;
  };
  // 节点拖拽
  const onDrop = ({ dragNode, node, dropPosition, dropToGap }) => {
    // console.log('info====', dragNode, node, dropPosition, dropToGap);
    const dropKey = node.key;
    const dragKey = dragNode.key;
    let dropPos = node.pos.split('-'),
      dragPos = dragNode.pos.split('-');
    const _dropPosition = dropPosition - Number(dropPos[dropPos.length - 1]); // the drop position relative to the drop node, inside 0, top -1, bottom 1
    // 只有同级才允许交换
    const canDrop = dropToGap
      ? isSameLevel(dragNode, node) && isSameParent(dragNode, node)
      : isDropToFirst(dragNode, node);
    if (!canDrop) {
      Notification.error({ description: '只允许同级节点交换顺序' });
      return;
    }
    const loop = (data, key, callback) => {
      for (let i = 0; i < data.length; i++) {
        if (data[i].key === key) {
          return callback(data[i], i, data);
        }
        if (data[i].children) {
          loop(data[i].children, key, callback);
        }
      }
    };
    const data = [...tData];

    // 查找拖动的节点，在原位置删除
    let dragObj;
    loop(data, dragKey, (item, index, arr) => {
      arr.splice(index, 1);
      dragObj = item;
    });
    // 在现位置插入
    if (!dropToGap) {
      // Drop on the content
      loop(data, dropKey, (item) => {
        item.children = item.children || [];
        // where to insert. New item was inserted to the start of the array in this example, but can be anywhere
        item.children.unshift(dragObj);
      });
    } else {
      let ar = [];
      let i;
      loop(data, dropKey, (_item, index, arr) => {
        ar = arr;
        i = index;
      });
      if (_dropPosition === -1) {
        // Drop on the top of the drop node
        ar.splice(i, 0, dragObj);
      } else {
        // Drop on the bottom of the drop node
        ar.splice(i + 1, 0, dragObj);
      }
    }
    setExpandedKeys([dragNode.key]);
    dragPos.shift();
    const idList = getIdList(data, dragPos);
    setLoading(true);
    updateTagOrCategoryPriority({ type: treeType, data: { idList } }).then(
      (res) => {
        if (res?.code === '200') {
          Notification.success({ description: '提交成功' });
        }
        onUpdate();
      },
    );
    setData(data);
  };
  return (
    <>
      <Spin spinning={loading}>
        <Tree
          draggable
          key={key}
          blockNode
          showLine
          defaultExpandedKeys={expandedKeys}
          titleRender={titleRender}
          treeData={tData}
          onDrop={onDrop}
        />
      </Spin>
      {/* 编辑/新增弹窗 */}
      <ModalForm
        title={modalProps.title}
        open={modalProps.open}
        modalProps={{
          destroyOnClose: true,
          onCancel: () => _setModalProps({ isClear: true }),
        }}
        request={async () => {
          return modalProps.type === 'edit'
            ? { name: modalProps?.nodeData?.name }
            : {};
        }}
        onFinish={async (values) => {
          setLoading(true);
          let parentId, id;
          if (modalProps.type === 'edit') {
            parentId = modalProps.nodeData?.parentId;
            id = modalProps.nodeData?.id;
          } else if (modalProps.type === 'addSon') {
            parentId = modalProps.nodeData?.id;
          }
          const res = await updateTagOrCategory({
            type: treeType,
            data: { name: values.name, id, parentId },
          });
          if (res?.code === '200') {
            Notification.success({ description: '提交成功' });
          }
          onUpdate();
          _setModalProps({ isClear: true });
          return true;
        }}
      >
        {modalProps.fatherLabel && (
          <ProFormText
            width="md"
            disabled={true}
            name="nameFed"
            value={modalProps.fatherTitle}
            label={modalProps.fatherLabel}
          />
        )}
        <ProFormText
          width="md"
          name="name"
          label={modalProps.label}
          placeholder="请输入名称"
        />
      </ModalForm>
    </>
  );
};

export default VTree;
