import React, { useEffect } from 'react';
import {
  ModalForm,
  BetaSchemaForm,
} from '@ant-design/pro-components';
import { Form } from 'antd';
import { nanoid } from 'nanoid';

const AdModal = ({
  modalVisible, setModalVisible, adModalData, onSubmit, onClose,
}) => {
  const [form] = Form.useForm();

  useEffect(() => {
    modalVisible && form.setFieldsValue(adModalData.adValues?.adBannerDTO);
  }, [modalVisible]);

  const adColumns = [
    {
      title: '跳转类型',
      dataIndex: 'redirectType',
      key: 'redirectType',
      valueType: 'select',
      align: 'center',
      fieldProps: {
        options: [
          { value: '2', label: 'h5' },
          { value: '1', label: 'deepLink' },
        ],
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      initialValue: '2',
    },
    {
      title: '跳转链接',
      dataIndex: 'redirectUrl',
      key: 'redirectUrl',
      align: 'center',
      formItemProps: {
        rules: [

          {
            required: true,
            message: '此项为必填项',
          },

          () => ({
            validator(_, value) {
              if (!value || value.includes('://')) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('链接格式错误'));
            },
          }),
        ],
      },
    },
    {
      title: '广告图',
      dataIndex: 'pictureUrl',
      key: 'pictureUrl',
      valueType: 'inputUpload',
      fieldProps: {
        limit: {
          width: 468,
          height: 882,
          size: 150 * 1024,
          type: ['png', 'jpg', 'jpeg'],
        },
        appKey: 'finance-operation-admin.vmic.xyz',
        appName: 'finance-operation-admin',
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
        extra:
          '图片尺寸，468px * 882px，150kb以内，图片不符合要求不允许上传',
      },
    },
  ];
  return (
    <>
      <ModalForm
        title="广告位配置"
        form={form}
        autoFocusFirstInput
        modalProps={{
          destroyOnClose: true,
        }}
        layout="horizontal"
        labelCol={{ span: 3 }}
        wrapperCol={{ span: 20 }}
        initialValues={adModalData.adValues?.adBannerDTO}
        open={modalVisible}
        onOpenChange={(value) => {
          setModalVisible(value);
          !value && onClose();
        }}
        onFinish={(values) => onSubmit({
          ...adModalData,
          adValues: {
            ...adModalData.adValues,
            id: nanoid(),
            adBannerDTO: values,
            type: 2,
          },
        })}
      >
        <BetaSchemaForm layoutType="Embed" columns={adColumns} />
      </ModalForm>
    </>
  );
};

export default AdModal;
