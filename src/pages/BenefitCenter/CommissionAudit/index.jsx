import React, { useState, useRef } from 'react';
import { Drawer } from 'antd';

import { Link, useLocation } from 'umi';
import OperateHistory from '@/components/OperateHistory';
import CommissionConfig from '../CommissionConfig/index';

const CommissionAudit = () => {
  const [currentConfigData, setCurrentConfigData] = useState({});
  const [modalVisible, setModalVisible] = useState(false);
  const historyRef = useRef();
  const location = useLocation();
  // 获取查询参数
  const id = new URLSearchParams(location.search).get('id');
  console.log('id====', id);

  // 点击操作记录查看详情
  const handleDetailView = (record) => {
    console.log('点击操作记录查看详情', record);
    setCurrentConfigData(record.contentBefore);
    setModalVisible(true);
  };

  // 重置操作详情弹窗
  const resetDialog = () => {
    setCurrentConfigData({});
    setModalVisible(false);
  };

  const upDateHistory = () => {
    historyRef.current.upDateHistory();
  };
  return (
    <>
      <Link
        to="/BenefitCenter/ConfigManagement"
      >
        返回
      </Link>
      <CommissionConfig actionType="audit" upDateHistory={upDateHistory} />
      <OperateHistory bizCode="welfareCommissionConfig" handleDetailView={handleDetailView} ref={historyRef} id={id} />
      <Drawer
        width={1200}
        title="详情"
        onClose={resetDialog}
        open={modalVisible}
        destroyOnClose
        footer={false}
      >
        <CommissionConfig actionType="view" configData={currentConfigData} />
      </Drawer>
    </>
  );
};

export default CommissionAudit;
