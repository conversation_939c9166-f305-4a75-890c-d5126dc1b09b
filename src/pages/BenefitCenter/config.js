/* eslint-disable */
import { getTags, getCategories } from "@/services/benefitCenter";
import { getDmpTag } from "@/services/common";
import DebounceSelect from "@/components/DebounceSelect";

export const tagsConfig = {
  title: "福利标签",
  dataIndex: "tagIdList",
  key: "tagIdList",
  valueType: "treeSelect",
  hideInTable: true,
  // width: 'sm',
  params: {},
  request: async () => {
    const res = await getTags();
    return res.data;
  },
  fieldProps: {
    multiple: true,
    fieldNames: {
      label: "name",
      value: "id",
    },
  },
};

export const categoryConfig = {
  title: "福利类目",
  dataIndex: "categoryId",
  key: "categoryId",
  valueType: "treeSelect",
  // width: 'sm',
  params: {},
  hideInTable: true,
  request: async () => {
    const res = await getCategories();
    return res.data;
  },
  fieldProps: {
    fieldNames: {
      label: "name",
      children: "children",
      value: "id",
    },
  },
  formItemProps: {
    rules: [
      {
        required: true,
        message: "此项为必填项",
      },
    ],
  },
};

// todo 从props拿到初始值进行回显
export const dmpConfig1 = {
  title: "DMP ID",
  dataIndex: "dmpId",
  valueType: "select",
  params: {},
  debounceTime: 500, // 优化请求频次
  request: async (values, props) => {
    try {
      console.log('-=====DMP ID"', { values, props });
      if (!values.keyWords && !props.text) return [];
      console.log("aaaa====mmmm");
      const res = await getDmpTag({ tagId: values.keyWords || props.text });
      console.log("===dmpConfig1====", res);
      return res.data.map((item) => {
        return {
          value: item.id + '',
          label: item.tagIdWithName,
        };
      });
    } catch (error) {
      console.log("errr=====", error);
    }
  },
  fieldProps: {
    showSearch: true,
    allowClear: true,
  },
  align: "center",
};

export const fetchOptions = (param) => {
  return getDmpTag({ tagId: param }).then((res) => {
    return res?.data?.map((item) => {
      return {
        value: item.id,
        label: item.tagIdWithName,
      };
    });
  });
};

export const dmpConfig = {
  title: "DMP ID",
  dataIndex: "dmpId",
  valueType: "select",
  renderFormItem: (schema, config, form) => {
    const dmpId = form.getFieldValue("dmpId") || form.getFieldValue("dmpTagId");
    return (
      <DebounceSelect
        fetchOptions={fetchOptions}
        {...schema.fieldProps}
        value={dmpId}
        addParam={{ initialValue: dmpId }}
        setOptionByAddParam={true}
      />
    );
  },
  fieldProps: {
    showSearch: true,
    allowClear: true,
  },
  align: "center",
};
