import React, {
  memo, useRef, useState, useEffect,
} from 'react';
import {
  ProCard,
  ProList,
} from '@ant-design/pro-components';
import {
  Button,
  Space,
  Modal,
  Tag,
  Checkbox,
  Tooltip,
} from 'antd';
import { getProductContentList } from '@/services/benefitCenter.js';
import { tagsConfig, categoryConfig } from '../../../config.js';

const ProductModal = (props) => {
  const {
    setModalVisible,
    modalVisible,
    welfareContentList,
    setWelfareContentList,
    autoFilterCondition,
    setAutoFilterCondition,
    setEditProductIndex,
    handleProductSelected,
  } = props;
  const formRef = useRef();
  // const [form] = Form.useForm();
  const [productList, setProductList] = useState([]);
  const [total, setTotal] = useState(0);
  const [checkedAll, setCheckedAll] = useState(false);
  const [checkedCondition, setCheckedCondition] = useState(false);
  const [searchParams, setSearchParams] = useState({});
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedProductList, setSelectedProductList] = useState([]);
  useEffect(() => {
    if (!modalVisible) return;
    if (welfareContentList) {
      welfareContentList.map((item) => {
        item.id = String(item.id);
        return item;
      });
      setSelectedRowKeys(welfareContentList.map((item) => item.id));
    }
    setSelectedProductList(welfareContentList);
    if (autoFilterCondition?.categoryId || autoFilterCondition?.tagIdList || [0, 1].includes(autoFilterCondition?.autoPush)) {
      // 设置搜索初始值 & 自动更新开关
      setCheckedCondition(true);
      setTimeout(() => {
        formRef.current?.setFieldsValue({ ...autoFilterCondition });
      }, 2000);
    }
  }, [modalVisible, autoFilterCondition, welfareContentList]);

  const getProductList = async (params, allSelect) => {
    setSearchParams(params);
    const {
      current, pageSize, categoryId, tagIdList, contentId, autoPush,
    } = params;
    const param = {
      page: current,
      pageSize,
      categoryId,
      tagIdList,
      idList: (contentId && contentId.split(',')) || [],
      allSelect,
      autoPush,
    };
    try {
      const res = await getProductContentList(param);
      if (res?.code === '200' && res.data) {
        res.data.list
          && res.data.list.map((item) => {
            item.id = String(item.id);
            return item;
          });
        setProductList(res.data.list || []);
        setTotal(res.data.total || 0);
        if (allSelect) {
          const keys = res.data.list.map((item) => item.id);
          setSelectedRowKeys(keys);
          setSelectedProductList(res.data.list || []);
        }
        return {
          data: res.data.list,
          total: res.data.total,
        };
      }
      return {
        data: [],
        total: 0,
      };
    } catch (error) {
      return {
        data: [],
        total: 0,
      };
    }
  };

  const rowSelection = {
    selectedRowKeys,
    preserveSelectedRowKeys: true,
    onChange: (selectedKeys) => {
      const mapList = (selectedProductList || [])
        .concat(productList)
        .reduce((acc, current) => {
          if (acc.findIndex((item) => item.id === current.id) === -1) {
            acc.push(current);
          }
          return acc;
        }, []);
      const formatSelectedRows = [];
      mapList.forEach((item) => {
        if (selectedKeys.indexOf(item.id) > -1) {
          formatSelectedRows.push(item);
        }
      });
      setSelectedRowKeys(selectedKeys);
      setSelectedProductList(formatSelectedRows);
      if (selectedKeys.length === total) {
        setCheckedAll(true);
      } else {
        setCheckedAll(false);
        setCheckedCondition(false);
      }
    },
  };

  const handleSelectAll = (checked) => {
    setCheckedAll(checked);
    if (checked) {
      getProductList(searchParams, true);
    } else {
      setSelectedRowKeys([]);
      setSelectedProductList([]);
    }
  };

  const handleSelectCondition = (checked) => {
    // console.log('handleSelectCondition====', checked);
    setCheckedCondition(checked);
    if (checked) {
      getProductList(searchParams, true);
    } else {
      setSelectedRowKeys([]);
      setSelectedProductList([]);
    }
  };
  // 表单提交
  const handleSubmit = async () => {
    setWelfareContentList(selectedProductList);
    const { categoryId, tagIdList, autoPush } = formRef.current.getFieldsValue();
    if (checkedCondition && (categoryId || tagIdList || [0, 1].includes(autoPush))) {
      setAutoFilterCondition({
        categoryId,
        tagIdList,
        autoPush,
      });
    } else {
      setAutoFilterCondition(null);
    }

    // 通知父级选品
    handleProductSelected({
      autoFilterCondition: {
        categoryId,
        tagIdList,
        autoPush,
      },
      selectedProductList: checkedCondition ? [] : selectedProductList,
      checkedCondition, // 用这个字段判断是否自动更新
    });
  };

  const closeDialog = () => {
    setModalVisible(false);
    // 重置选中的选项
    setEditProductIndex && setEditProductIndex('');
    setSelectedRowKeys([]);
    setSelectedProductList([]);
    setSearchParams({});
    setCheckedCondition(false);
    setCheckedAll(false);
  };

  const toolBarRender = () => [
    <Checkbox
      checked={checkedAll}
      disabled={checkedCondition}
      onChange={(e) => handleSelectAll(e.target.checked)}
    >
      全选（当前筛选结果
      {total}
      项）
    </Checkbox>,
    <Checkbox
      checked={checkedCondition}
      disabled={checkedAll}
      onChange={(e) => handleSelectCondition(e.target.checked)}
    >
      选择对应筛选条件（自动更新）
    </Checkbox>,
  ];

  const selectedColumns = {
    title: {
      dataIndex: 'welfareMainTitle',
      search: false,
      render: (text, record) => (
        <Space direction="vertical">
          <Tooltip title={record.welfareMainTitle} arrow={false}>
            <span>{record.welfareMainTitle}</span>
          </Tooltip>
          <Tag color="#5BD8A6">{record.categoryName}</Tag>
        </Space>
      ),
    },
    content: {
      dataIndex: 'cardLogoUrl',
      search: false,
      render: (text, record) => (
        <div style={{ flex: 1 }}>
          <div style={{ width: 100 }}>
            <img
              style={{ height: '50px' }}
              src={record.cardLogoUrl}
              alt={record.welfareMainTitle}
            />
          </div>
        </div>
      ),
    },
    actions: {},
  };
  const searchColumns = {
    categoryId: categoryConfig,
    tagIdList: tagsConfig,
    contentId: {
      dataIndex: 'contentId',
      title: '搜索',
      fieldProps: {
        placeholder: '请输入福利id',
        default: '5',
      },
    },
    autoPush: {
      title: '自动推送',
      dataIndex: 'autoPush',
      key: 'autoPush',
      align: 'center',
      valueType: 'select',
      valueEnum: { 0: '否', 1: '是' },
      fieldProps: {
        maxLength: 4,
        showCount: true,
        options: [
          { label: '否', value: 0 },
          { label: '是', value: 1 },
        ],
      },
    },
    title: {
      dataIndex: 'welfareMainTitle',
      search: false,
      render: (text, record) => (
        <Space direction="vertical">
          <Tooltip title={record.welfareMainTitle} arrow={false}>
            <span>{record.welfareMainTitle}</span>
          </Tooltip>
          <Tag color="#5BD8A6">{record.categoryName}</Tag>
        </Space>
      ),
    },
    content: {
      dataIndex: 'cardLogoUrl',
      search: false,
      render: (text, record) => (
        <div style={{ flex: 1 }}>
          <div style={{ width: 200 }}>
            <img
              style={{ height: '50px' }}
              src={record.cardLogoUrl}
              alt={record.welfareMainTitle}
            />
          </div>
        </div>
      ),
    },
  };

  return (
    <Modal
      width={1200}
      height={1200}
      destroyOnClose
      title="选品"
      afterClose={() => closeDialog()}
      onCancel={() => closeDialog()}
      open={modalVisible}
      footer={(
        <Space>
          <Button
            onClick={() => {
              closeDialog();
            }}
          >
            取消
          </Button>
          <Button
            type="primary"
            onClick={() => {
              handleSubmit();
              setModalVisible(false);
            }}
          >
            确认
          </Button>
        </Space>
      )}
    >
      <ProCard style={{ marginBlockStart: 8 }} gutter={8} ghost>
        <ProCard colSpan="50%">
          <ProList
            style={{ maxHeight: '800px', overflow: 'auto' }}
            search={false}
            split
            showActions="hover"
            rowKey="id"
            rowSelection={rowSelection}
            grid={{ gutter: 16, column: 3 }}
            metas={selectedColumns}
            headerTitle="已选择的商品列表"
            dataSource={selectedProductList}
          />
        </ProCard>
        <ProCard colSpan="50%">
          <ProList
            style={{ height: '800px', overflow: 'auto' }}
            pagination={{
              defaultPageSize: 9,
              showSizeChanger: true,
            }}
            split
            formRef={formRef}
            search={{ showHiddenNum: true, defaultCollapsed: false }}
            tableAlertRender={false}
            request={async (params = {}) => getProductList(params, false)}
            showActions="hover"
            rowKey="id"
            rowSelection={rowSelection}
            grid={{ gutter: 16, column: 3 }}
            metas={searchColumns}
            initialValues={autoFilterCondition}
            headerTitle="商品池列表展示"
            dataSource={productList}
            // 其他配置项
            toolBarRender={toolBarRender}
          />
        </ProCard>
      </ProCard>
    </Modal>
  );
};

export default memo(ProductModal);
