import React, { useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import {
  ProCard,
  BetaSchemaForm,
} from '@ant-design/pro-components';
import {
  Button,
  List,
} from 'antd';

const DraggableTab = ({
  id,
  index,
  action,
  record,
  moveTab,
  setProductModalVisible,
  setEditProductIndex,
  setWelfareContentList,
  setAutoFilterCondition,
  formDisabled,
}) => {
  const dragRef = useRef();

  const tabColumns = [
    {
      valueType: 'group',
      columns: [
        {
          title: 'tab名称',
          dataIndex: 'tabName',
          width: 's',
          fieldProps: {
            maxLength: 20,
            showCount: true,
          },
          formItemProps: {
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
            ],
          },
        },
        {
          title: '渠道spm',
          dataIndex: 'channelSpm',
          width: 's',
          formItemProps: {
            extra: '如需输入多个spm，用逗号分隔',
          },
        },
        {
          title: '选品',
          dataIndex: 'welfareContentList',
          width: 'md',
          tooltip: '超出20个展示前20个',
          renderFormItem: (schema, config, form) => {
            const currentTabInfo = form.getFieldValue([
              'welfareTabInfoList',
              index,
            ]);
            const { autoFilterCondition = {}, welfareContentList = [] } = currentTabInfo;

            let descDom;
            const {
              categoryId, autoPush, tagIdList = [], categoryName, tagNameList,
            } = autoFilterCondition;
            if (
              categoryId
              || tagIdList.length > 0
              || [0, 1].includes(autoPush)
            ) {
              descDom = (
                <div>
                  <div>自动更新</div>
                  {categoryName && (
                    <div>
                      已选类目：
                      {categoryName}
                    </div>
                  )}
                  {tagNameList?.length > 0 && (
                    <div>
                      已选标签：
                      {tagNameList.join(',')}
                    </div>
                  )}
                  {['0', '1'].includes(`${autoPush}`) && (
                    <div>
                      自动推送：
                      {autoPush === 1 ? '是' : '否'}
                    </div>
                  )}
                </div>
              );
            } else {
              const descData = welfareContentList.map(
                (contentItem) => contentItem.welfareMainTitle,
              );
              descDom = descData?.length > 0 && (
                <div style={{ maxHeight: 200, overflow: 'scroll' }}>
                  <List
                    size="small"
                    bordered
                    dataSource={descData}
                    renderItem={(item) => <List.Item>{item}</List.Item>}
                  />
                </div>
              );
            }
            return (
              <>
                <Button
                  type="primary"
                  onClick={() => {
                    setProductModalVisible(true);
                    setEditProductIndex(index);
                    setWelfareContentList(welfareContentList);
                    setAutoFilterCondition(autoFilterCondition);
                  }}
                >
                  选择
                </Button>
                {descDom && (
                  <div
                    style={{
                      marginTop: 10,
                      maxHeight: '100',
                      overflow: 'scroll',
                      width: '100%',
                    }}
                  >
                    {descDom}
                  </div>
                )}
              </>
            );
          },
        },
      ],
    },
  ];

  const [{ isDragging }, drag] = useDrag({
    type: 'tab',
    item: () => ({ id, index }),
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [{ handlerId }, drop] = useDrop({
    accept: 'tab',
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item, monitor) {
      if (!dragRef.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;
      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return;
      }
      // Determine rectangle on screen
      const hoverBoundingRect = dragRef.current?.getBoundingClientRect();
      // Get vertical middle
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      // Determine mouse position
      const clientOffset = monitor.getClientOffset();
      // Get pixels to the top
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;
      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%
      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }
      // // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }
      // Time to actually perform the action
      moveTab(dragIndex, hoverIndex);
      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex;
    },
  });

  return (
    <div
      ref={drag(drop(dragRef))}
      style={{ opacity: isDragging ? 0.5 : 1 }}
      data-handler-id={handlerId}
    >
      <ProCard
        bordered
        extra={formDisabled ? null : action}
        title={record?.name}
        style={{
          marginBlockEnd: 8,
        }}
      >
        <BetaSchemaForm layoutType="Embed" columns={tabColumns} />
      </ProCard>
    </div>
  );
};

export default DraggableTab;
