import React, {
  memo, useRef, useState, useEffect, useCallback,
} from 'react';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { DndProvider } from 'react-dnd';
import {
  Button,
  Popover,
  Image,
  Space,
  notification,
  Drawer,
} from 'antd';
import {
  ProCard,
  BetaSchemaForm,
  ProForm,
  ProFormList,
  EditableProTable,
} from '@ant-design/pro-components';
import { ConfigProvider } from 'finance-busi-components-toB';
import { nanoid } from 'nanoid';
import { titleMap } from '@/constant/config.js';
import {
  auditBenefitPage,
  saveBenefitPage,
  getCategoryIdsInfo,
  getTagIdsInfo,
} from '@/services/benefitCenter.js';

import dayjs from 'dayjs';
import { debounce } from '@/utils/util.js';
import DraggableTab from '../DraggableTab';
import ProductModal from '../ProductModal/index';
import { activitySettingColumn, bannerColumns } from './column';

const DetailModal = (props) => {
  const {
    setModalVisible,
    modalVisible,
    actionType,
    setActionType,
    initialConfig = {},
    handleReload,
    setInitialConfig,
    noHistoryTable = false,
    operateHistoryRender,
  } = props;
  const formRef = useRef();
  const tabActionRef = useRef();
  const [productModalVisible, setProductModalVisible] = useState(false);
  const [editProductIndex, setEditProductIndex] = useState(''); // 选中的选品tab
  const [autoFilterCondition, setAutoFilterCondition] = useState({});
  const [welfareContentList, setWelfareContentList] = useState([]);
  const [bannerEditableKeys, setBannerEditableRowKeys] = useState([]);
  const formDisabled = actionType === 'view' || actionType === 'audit';

  // 预览
  const handlePreview = useCallback((data) => {
    // C端通信
    const previewIframe = document.getElementById('ifr');
    const previewIframe1 = document.getElementById('ifr1');
    const values = formRef.current.getFieldsValue();
    const temData = data ? { ...data } : { ...values };
    if (temData.welfareTabInfoList) {
      const tempWelfareTabInfoList = temData.welfareTabInfoList.map(
        (item) => ({
          ...item,
          tabId: String(item.tabId || nanoid()),
          welfarePageContentDTOList: (item.welfareContentList || []).slice(
            0,
            20,
          ),
        }),
      );
      temData.welfareTabInfoList = tempWelfareTabInfoList;
      temData.selectTabId = temData.welfareTabInfoList[0]?.tabId;
      temData.previewTabKey = nanoid();
    }

    // 数据处理
    previewIframe
      && previewIframe?.contentWindow?.postMessage(JSON.stringify(temData), '*');

    previewIframe1
      && previewIframe1?.contentWindow?.postMessage(JSON.stringify(temData), '*');
  }, []);

  useEffect(() => {
    initialConfig.welfareBannerInfoList
      && setBannerEditableRowKeys(
        initialConfig.welfareBannerInfoList.map((item) => item.id),
      );
  }, [initialConfig.welfareBannerInfoList]);

  useEffect(() => {
    const previewIframe = document.getElementById('ifr');
    previewIframe
      && (previewIframe.onload = () => {
        handlePreview(initialConfig);
      });

    // 操作历史(区分页面俩iframe)
    const previewIframe1 = document.getElementById('ifr1');
    previewIframe1
      && (previewIframe1.onload = () => {
        handlePreview(initialConfig);
      });
  }, [initialConfig, modalVisible]);

  const moveTab = useCallback((dragIndex, hoverIndex) => {
    tabActionRef.current.move(dragIndex, hoverIndex);
    setTimeout(() => {
      handlePreview();
    }, 300);
  }, []);

  // 重置
  const resetDialog = useCallback(() => {
    setModalVisible(false);
    setActionType('');
    setInitialConfig({});
    // 刷新列表
    handleReload && handleReload();
  }, [handleReload, setModalVisible, setActionType, setInitialConfig]);

  const columns = [
    {
      title: '页面名称',
      dataIndex: 'pageName',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
        extra: '后台字段，C端不展示',
      },
      fieldProps: {
        maxLength: 50,
        showCount: true,
      },
    },
    {
      title: '页面位置',
      dataIndex: 'pageKey',
      key: 'pageKey',
      valueType: 'select',
      valueEnum: {
        walletWelfareCenter: '省钱首页入口',
        otherAdEntrance: '其他投放入口',
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      fieldProps: {
        disabled: formDisabled || actionType === 'edit',
      },
    },
    {
      title: '页面标题',
      dataIndex: 'pageTitle',
      key: 'pageTitle',
      fieldProps: {
        maxLength: 50,
        showCount: true,
      },
      formItemProps: {
        extra: 'C端展示',
      },
    },
    {
      title: '活动时间',
      valueType: 'dateTimeRange',
      dataIndex: 'activityTime',
      key: 'activityTime',
      formItemProps: {
        extra: '活动过期，自动下线',
      },
    },
    {
      title: '活动配置字段',
      renderFormItem: () => (
        <ProCard
          title="如有活动，请务必配置"
          bordered
          size="small"
          collapsible
          defaultCollapsed={false}
        >
          <BetaSchemaForm
            layoutType="Embed"
            columns={activitySettingColumn}
          />
        </ProCard>
      ),
    },
    {
      title: 'banner图',
      dataIndex: 'welfareBannerInfoList',
      renderFormItem: () => (
        <EditableProTable
          headerTitle="有活动氛围需保证banner配置至少一个支持所有用户可见，避免c端展示问题"
          columns={bannerColumns}
          rowKey="id"
          name="welfareBannerInfoList"
          bordered
          scroll={{
            y: 300,
          }}
          recordCreatorProps={{
            newRecordType: 'dataSource',
            record: () => ({
              id: nanoid(),
            }),
            creatorButtonText: '新增banner配置',
          }}
          maxLength={6}
          editable={{
            type: 'multiple',
            editableKeys: bannerEditableKeys,
            actionRender: (_, __, defaultDoms) => [defaultDoms.delete],
            onChange: setBannerEditableRowKeys,
          }}
        />
      ),
    },
    {
      title: '规则链接',
      dataIndex: 'activityRuleUrl',
      key: 'activityRuleUrl',
      formItemProps: {
        rules: [
          () => ({
            validator(_, value) {
              if (!value || value.includes('://')) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('链接格式错误'));
            },
          }),
        ],
      },
    },
    {
      title: '面客tab',
      renderFormItem: () => (
        <ProFormList
          name="welfareTabInfoList"
          actionRef={tabActionRef}
          creatorButtonProps={{
            creatorButtonText: '新增tab',
          }}
          rules={[
            {
              required: true,
              message: '此项为必填项',
            },
          ]}
          initialValue={[]}
          alwaysShowItemLabel
          copyIconProps={false}
          itemRender={({ action }, listMeta) => {
            const { index, record } = listMeta;
            return (
              <DraggableTab
                formDisabled={formDisabled}
                action={action}
                key={index}
                index={index}
                record={record}
                formRef={formRef}
                tabActionRef={tabActionRef}
                moveTab={moveTab}
                id={nanoid()}
                setProductModalVisible={setProductModalVisible}
                setEditProductIndex={setEditProductIndex}
                setWelfareContentList={setWelfareContentList}
                setAutoFilterCondition={setAutoFilterCondition}
              />
            );
          }}
        />
      ),
    },
  ];

  // 表单提交
  const handleSubmit = async () => {
    const values = await formRef.current?.validateFields();
    const {
      activityTime = [],
      activityConfig = {},
      welfareTabInfoList,
      welfareBannerInfoList,
    } = values;
    const { activityPictureInfo } = activityConfig || {};
    const tempTabList = welfareTabInfoList.map((tabInfo) => ({
      ...tabInfo,
      // priority: index, // 服务端无需这个字段
    }));

    // 校验活动氛围图状态
    if (
      !!activityPictureInfo?.pictureUrl
      !== !!activityPictureInfo?.foldableScreenPictureUrl
    ) {
      return notification.error({
        message: '提交失败',
        description:
          '请确认活动氛围图常规屏和折叠屏图片配置状态一致，不可只配置一个',
      });
    }
    if (
      activityPictureInfo?.pictureUrl
      && activityPictureInfo?.foldableScreenPictureUrl
      && !(welfareBannerInfoList?.length > 0)
    ) {
      return notification.error({
        message: '提交失败',
        description: '在活动氛围下 必须配置banner',
      });
    }
    if (welfareBannerInfoList?.length) {
      const noPicItem = welfareBannerInfoList.find((bannerItem) => !bannerItem.pictureUrl);
      if (noPicItem) {
        return notification.error({
          message: '提交失败',
          description: 'banner图片必填',
        });
      }
    }
    const params = {
      ...initialConfig,
      ...values,
      startValidity:
        activityTime && activityTime[0]
          ? dayjs(activityTime[0]).format('YYYY-MM-DD HH:mm:ss')
          : undefined,
      endValidity:
        activityTime && activityTime[1]
          ? dayjs(activityTime[1]).format('YYYY-MM-DD HH:mm:ss')
          : undefined,
      welfareTabInfoList: tempTabList,
    };
    delete params.activityTime;
    delete params[8];
    delete params.rowKey;

    if (['add', 'copy'].includes(actionType)) {
      delete params.pageConfigId;
    }
    const res = await saveBenefitPage(params);
    if (res?.code === '200') {
      resetDialog();
    }
    return true;
  };

  // 修改审核状态
  const handleAudit = async (params) => {
    const res = await auditBenefitPage(params);

    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
      });
      resetDialog();
    }
  };

  // 选中选品列表
  const handleProductSelected = async (values) => {
    const {
      selectedProductList = [],
      autoFilterCondition: newAutoFilterConditionKey,
      checkedCondition,
    } = values;
    const item = formRef.current.getFieldValue([
      'welfareTabInfoList',
      editProductIndex,
    ]);

    let tagNameList = null;
    let categoryName = '';
    let tempItem = {};
    if (checkedCondition) {
      if (newAutoFilterConditionKey?.categoryId) {
        const res1 = await getCategoryIdsInfo({
          idList: [newAutoFilterConditionKey.categoryId],
        });
        if (res1?.code === '200' && res1?.data) {
          categoryName = res1.data.map((categoryItem) => categoryItem.name).join(',');
        }
      }
      if ((newAutoFilterConditionKey?.tagIdList || []).length > 0) {
        const res2 = await getTagIdsInfo({
          idList: newAutoFilterConditionKey.tagIdList,
        });
        if (res2.code === '200' && res2?.data) {
          tagNameList = res2.data.map((tagItem) => tagItem.name);
        }
      }
      tempItem = {
        ...item,
        autoFilterCondition: {
          ...newAutoFilterConditionKey,
          categoryName,
          tagNameList,
        },
        welfareContentList: selectedProductList,
      };
    } else {
      tempItem = {
        ...item,
        welfareContentList: selectedProductList,
        autoFilterCondition: undefined,
      };
    }

    formRef.current.setFieldValue(
      ['welfareTabInfoList', editProductIndex],
      tempItem,
    );
    handlePreview();
  };

  return (
    <Drawer
      width={1400}
      title={titleMap[actionType] || ''}
      onClose={() => resetDialog()}
      open={modalVisible}
      destroyOnClose
      footer={(
        <Space>
          <Button
            onClick={() => {
              resetDialog();
            }}
          >
            {actionType === 'view' ? '关闭' : '取消'}
          </Button>
          {['edit', 'add', 'copy'].includes(actionType) && (
            <>
              <Button
                type="primary"
                onClick={() => {
                  handleSubmit();
                }}
              >
                提交
              </Button>
            </>
          )}
          {actionType === 'audit' && (
            <>
              <Button
                type="primary"
                onClick={() => {
                  handleAudit({
                    id: initialConfig?.pageConfigId,
                    pageConfigType: initialConfig?.pageConfigType,
                    auditOperation: 3,
                  });
                }}
              >
                审核驳回
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  handleAudit({
                    id: initialConfig?.pageConfigId,
                    pageConfigType: initialConfig?.pageConfigType,
                    auditOperation: 2,
                  });
                }}
              >
                审核通过
              </Button>
            </>
          )}
        </Space>
      )}
    >
      <div
        style={{
          height: '100%',
          overflow: 'hidden',
          position: 'relative',
          display: 'flex',
          paddingBottom: 20,
        }}
      >
        <div
          style={{
            height: '100%',
            flex: 1,
            overflow: 'scroll',
            display: 'flex',
            flexDirection: 'column',
          }}
        >
          <ProCard
            title="页面配置"
            colSpan="70%"
            style={{ height: '100%', overflow: 'hidden' }}
          >
            <div
              style={{ height: '100%', overflow: 'scroll', paddingBottom: 10 }}
            >
              <DndProvider backend={HTML5Backend}>
                <ConfigProvider>
                  <ProForm
                    formRef={formRef}
                    onValuesChange={debounce(() => {
                      handlePreview();
                    }, 500)}
                    shouldUpdate={false}
                    layout="horizontal"
                    labelCol={{ span: 3 }}
                    wrapperCol={{ span: 20 }}
                    submitter={false}
                    initialValues={initialConfig}
                    disabled={formDisabled}
                  >
                    <BetaSchemaForm layoutType="Embed" columns={columns} />
                  </ProForm>
                </ConfigProvider>
              </DndProvider>
            </div>
          </ProCard>
        </div>
        <div style={{ width: 375 }}>
          <ProCard
            title="页面预览"
            colSpan="30%"
            extra={(
              <Popover
                content={(
                  <Image
                    width={300}
                    src="https://gaia-xy.vivo.com.cn/wallet/polaris/365380a0-06a4-11ef-b9a2-1501668c0520.png.webp"
                  />
                )}
                title=""
                placement="bottom"
                trigger="click"
              >
                <Button>点击查看页面元素</Button>
              </Popover>
            )}
          >
            <div
              style={{ height: 'calc(100vh - 200px)', position: 'relative' }}
            >
              {!noHistoryTable && (
                <iframe
                  style={{
                    width: '100%',
                    height: 'calc(100vh - 200px)',
                    display: 'block',
                    overflow: 'scroll',
                    border: '1px solid #cccccd',
                  }}
                  src="//m.vivojrkj.com/favour/home/<USER>"
                  // src="//10.15.49.114:8080/home/<USER>"
                  id="ifr"
                  title="page1"
                />
              )}
              {noHistoryTable && (
                <iframe
                  style={{
                    width: '100%',
                    height: 'calc(100vh - 200px)',
                    display: 'block',
                    overflow: 'scroll',
                    border: '1px solid #cccccd',
                  }}
                  src="//m.vivojrkj.com/favour/home/<USER>"
                  id="ifr1"
                  title="page2"
                />
              )}
            </div>
          </ProCard>
        </div>
      </div>
      {/* 操作记录 */}
      {!noHistoryTable && (actionType === 'audit' || actionType === 'view') && (
        operateHistoryRender({ initialConfig })
      )}
      <ProductModal
        setModalVisible={setProductModalVisible}
        modalVisible={productModalVisible}
        welfareContentList={welfareContentList}
        setWelfareContentList={setWelfareContentList}
        autoFilterCondition={autoFilterCondition}
        setAutoFilterCondition={setAutoFilterCondition}
        setEditProductIndex={setEditProductIndex}
        handleProductSelected={handleProductSelected}
      />
      {/* )} */}
    </Drawer>
  );
};

export default memo(DetailModal);
