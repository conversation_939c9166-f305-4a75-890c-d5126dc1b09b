import dayjs from 'dayjs';
import { dmpConfig, fetchOptions } from '../../../config';
import DebounceSelect from '@/components/DebounceSelect';
import { colorRule, mainColorRule } from '@/utils/validate.js';

export const bannerColumns = [
  {
    title: 'banner图',
    dataIndex: 'pictureUrl',
    key: 'pictureUrl',
    tooltip: '图片尺寸，960px * 315px， 不超过150kb',
    valueType: 'inputUpload',
    align: 'center',
    formItemProps: {},
    width: 200,
    fieldProps: {
      limit: {
        width: 960,
        height: 315,
        size: 150 * 1024,
        type: ['png', 'jpg', 'jpeg'],
        maxCount: 1,
        description: false,
      },
      appKey: 'finance-operation-admin.vmic.xyz',
      appName: 'finance-operation-admin',
    },
  },
  {
    title: '跳转类型',
    dataIndex: 'redirectType',
    key: 'redirectType',
    valueType: 'select',
    align: 'center',
    fieldProps: {
      options: [
        { value: '2', label: 'h5' },
        { value: '1', label: 'deepLink' },
      ],
    },
  },
  {
    title: '跳转链接',
    dataIndex: 'redirectUrl',
    key: 'redirectUrl',
    align: 'center',
    formItemProps: {
      rules: [
        ({ getFieldValue }) => ({
          validator(_, value) {
            if (!value || value.includes('://')) {
              return Promise.resolve();
            }
            return Promise.reject(new Error('链接格式错误'));
          },
        }),
      ],
    },
  },
  {
    title: '无障碍文案',
    dataIndex: 'talkBackDesc',
    key: 'talkBackDesc',
    align: 'center',
  },
  {
    title: 'DMP ID',
    dataIndex: 'dmpId',
    valueType: 'select',
    renderFormItem: (schema, config, form) => {
      const dmpId = config?.record?.dmpId || '';
      return (
        <DebounceSelect
          fetchOptions={fetchOptions}
          {...schema.fieldProps}
          value={dmpId}
          addParam={{ initialValue: dmpId }}
          setOptionByAddParam={true}
        />
      );
    },
    fieldProps: {
      showSearch: true,
      allowClear: true,
    },
    align: 'center',
  },
  {
    title: '操作',
    valueType: 'option',
    width: 120,
    key: 'option',
    align: 'center',
    fixed: 'right',
    render: () => {
      return null;
    },
  },
];
export const activitySettingColumn = [
  {
    dataIndex: ['activityConfig', 'dmpId'],
    title: 'DMP ID',
    valueType: 'select',
    renderFormItem: (schema, config, form) => {
      const dmpId = form.getFieldValue(['activityConfig', 'dmpId']);
      return (
        <DebounceSelect
          fetchOptions={fetchOptions}
          {...schema.fieldProps}
          value={dmpId}
          addParam={{ initialValue: dmpId }}
          setOptionByAddParam={true}
        />
      );
    },
    fieldProps: {
      showSearch: true,
      allowClear: true,
    },
    align: 'center',
  },
  {
    title: '活动氛围图',
    dataIndex: ['activityConfig', 'activityPictureInfo', 'pictureUrl'],
    valueType: 'inputUpload',
    width: 'md',
    fieldProps: {
      limit: {
        width: 1080,
        height: 772,
        size: 150 * 1024,
      },
      appKey: 'finance-operation-admin.vmic.xyz',
      appName: 'finance-operation-admin',
    },
    formItemProps: {
      extra: '常规屏，图片尺寸1080px*772px,大小不超过150kb',
    },
  },
  {
    title: '活动氛围图',
    dataIndex: [
      'activityConfig',
      'activityPictureInfo',
      'foldableScreenPictureUrl',
    ],
    valueType: 'inputUpload',
    width: 'md',
    fieldProps: {
      limit: {
        width: 1914,
        height: 772,
        size: 150 * 1024,
      },
      appKey: 'finance-operation-admin.vmic.xyz',
      appName: 'finance-operation-admin',
    },
    formItemProps: {
      extra: '折叠屏， 图片尺寸1914px*772px,大小不超过150kb',
    },
  },
  {
    title: '页面背景(活动)',
    dataIndex: ['activityConfig', 'activityThemeColorConfig', 'pageBg'],
    width: 's',
    formItemProps: {
      extra: '支持为空，为空则为默认背景色（灰）',
      rules: [colorRule],
    },
  },
  {
    title: 'tab背景(常规)',
    dataIndex: ['activityConfig', 'activityThemeColorConfig', 'tabBgNormal'],
    width: 's',
    formItemProps: {
      extra: '支持为空，为空则为默认背景色（灰）',
      rules: [colorRule],
    },
  },
  {
    title: 'tab背景(吸顶)',
    dataIndex: ['activityConfig', 'activityThemeColorConfig', 'tabBgTop'],
    width: 's',
    formItemProps: {
      extra: '支持为空，为空则为默认背景色（白）',
      rules: [colorRule],
    },
  },
  {
    title: 'tab文字-未选(常规)',
    dataIndex: [
      'activityConfig',
      'activityThemeColorConfig',
      'tabTextUnselectedNormal',
    ],
    width: 's',
    formItemProps: {
      extra: '支持为空，为空则为默认背景色（灰）',
      rules: [mainColorRule],
    },
  },
  {
    title: 'tab文字-未选(吸顶)',
    dataIndex: [
      'activityConfig',
      'activityThemeColorConfig',
      'tabTextUnselectedTop',
    ],
    width: 's',
    formItemProps: {
      extra: '支持为空，为空则为默认背景色（灰）',
      rules: [mainColorRule],
    },
  },
  {
    title: 'tab文字-选中(常规)',
    dataIndex: [
      'activityConfig',
      'activityThemeColorConfig',
      'tabTextSelectedNormal',
    ],
    width: 's',
    formItemProps: {
      extra: '支持为空，为空则为默认背景色（黑）',
      rules: [mainColorRule],
    },
  },
  {
    title: 'tab文字-选中(吸顶)',
    dataIndex: [
      'activityConfig',
      'activityThemeColorConfig',
      'tabTextSelectedTop',
    ],
    width: 's',
    formItemProps: {
      extra: '支持为空，为空则为默认背景色（黑）',
      rules: [mainColorRule],
    },
  },
  {
    title: 'tab选中标识(常规)',
    dataIndex: [
      'activityConfig',
      'activityThemeColorConfig',
      'tabSelectedMarkNormal',
    ],
    width: 's',
    formItemProps: {
      extra: '支持为空，为空则为默认背景色（蓝）',
      rules: [mainColorRule],
    },
  },
  {
    title: 'tab选中标识(吸顶)',
    dataIndex: [
      'activityConfig',
      'activityThemeColorConfig',
      'tabSelectedMarkTop',
    ],
    width: 's',
    formItemProps: {
      extra: '支持为空，为空则为默认背景色（蓝）',
      rules: [mainColorRule],
    },
  },
  {
    title: '标题栏背景颜色',
    dataIndex: [
      'activityConfig',
      'activityThemeColorConfig',
      'titleBarBgColor',
    ],
    width: 's',
    formItemProps: {
      extra: '支持为空，为空则为页面背景色，页面背景色为空则为默认背景色（白）',
      rules: [colorRule],
    },
  },
  {
    title: '标题栏文字颜色',
    dataIndex: ['activityConfig', 'activityThemeColorConfig', 'titleTextColor'],
    valueType: 'radio',
    width: 's',
    formItemProps: {
      extra: '默认为黑色',
    },
    initialValue: '#000000',
    fieldProps: {
      options: [
        { label: '黑色', value: '#000000' },
        { label: '白色', value: '#ffffff' },
      ],
    },
  },
];
