import React, { useState } from 'react';
import CommonTableList from '@/components/CommonTableList';
import { queryWalletLogList } from '@/services/benefitCenter.js';
import { Button } from 'antd';
import { operateBasicColumn } from '@/constant/config.js';
import DetailModal from '../DetailModal/index';

const OperateHistory = ({ id, bizCode }) => {
  const [modalVisible, setModalVisible] = useState(false); // 弹窗可见性
  const [actionType, setActionType] = useState('');
  const [initialConfig, setInitialConfig] = useState({});

  // 操作记录列表数据请求
  const getOperationHistory = async (params) => {
    const param = {
      page: params.current,
      pageSize: params.pageSize,
      operationId: id,
      bizCode,
    };
    const res = await queryWalletLogList(param);

    if (res?.code === '200' && res.data) {
      return {
        data: res.data.list,
        total: res.data.total,
      };
    }
  };

  const columns = [
    ...operateBasicColumn,
    {
      title: '操作详情',
      dataIndex: 'actionType',
      key: 'actionType',
      align: 'center',
      fixed: 'right',
      hideInSearch: true,
      render: (text, record) => (
        <Button
          key="edit"
          type="link"
          size="small"
          onClick={() => {
            const { startValidity, endValidity } = record.contentBefore;
            const tempRecord = {
              ...record.contentBefore,
              activityTime: [startValidity, endValidity],
            };
            setInitialConfig(tempRecord);
            setActionType('view');
            setModalVisible(true);
          }}
          disabled={false}
        >
          查看详情
        </Button>
      ),
    },
  ];
  return (
    <>
      <CommonTableList
        key="OperateHistory"
        columns={columns}
        search={false}
        bordered
        request={getOperationHistory}
        pagination={{
          pageSize: 10,
          showSizeChanger: false,
          showTotal: (total) => `共${total}项`,
        }}
        headerTitle="操作记录"
      />
      <DetailModal
        key="historyDetailModal"
        modalVisible={modalVisible}
        actionType={actionType}
        setActionType={setActionType}
        setModalVisible={setModalVisible}
        setInitialConfig={setInitialConfig}
        initialConfig={initialConfig}
        noHistoryTable
      />
    </>
  );
};

export default OperateHistory;
