import React, {
  useRef,
  useState,
  useCallback,
  memo,
} from 'react';
import {
  Button,
  Divider,
  Space,
  Popconfirm,
  notification,
  Spin,
  Tooltip,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { ProCard } from '@ant-design/pro-components';
import CommonTableList from '@/components/CommonTableList';
import {
  getBenefitPageList,
  benefitPageOnline,
  getBenefitPageDetail,
  pageCacheClear,
} from '@/services/benefitCenter.js';
import { nanoid } from 'nanoid';
import { copyContent } from '@/utils/util.js';
import DetailModal from './components/DetailModal/index';
import OperateHistory from './components/OperateHistory';

const onlineValueEnum = {
  1: { text: '禁用', status: 'Default' },
  2: { text: '启用', status: 'Success' },
};

const pageValueEnum = {
  1: { text: '禁用', status: 'Default' },
  2: { text: '已上线', status: 'Success' },
  3: { text: '有修改待上线', status: 'Error' },
  4: { text: '已过期', status: 'Default' },
};

const auditValueEnum = {
  1: { text: '待审核', status: 'Processing' },
  2: { text: '审核通过', status: 'Success' },
  3: { text: '审核驳回', status: 'Error' },
};

const ConfigManagement = () => {
  const actionRef = useRef();
  const [modalVisible, setModalVisible] = useState(false);
  const [actionType, setActionType] = useState('');
  const [loading, setLoading] = useState(false);
  const [initialConfig, setInitialConfig] = useState({});

  const operateHistoryRender = (props) => (
    <>
      <Divider type="horizontal" />
      <OperateHistory
        key="operateHistory"
        id={props.initialConfig?.pageConfigId}
        bizCode="welfarePage"
        disabled={false}
      />
    </>
  );

  // 刷新列表
  const handleReload = useCallback(() => {
    actionRef.current?.reload();
  }, [actionRef]);

  // 活动上下线
  const changeOnlineStatus = async (record) => {
    const params = {
      id: record.pageConfigId,
      pageConfigType: record?.pageConfigType,
      onlineOperation: record.onlineStatus === 1 ? 2 : 1,
    };
    const res = await benefitPageOnline(params);
    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
      });
      handleReload();
    }
  };

  // 操作
  const handleAction = async (record, actionTypeParam) => {
    const { pageConfigId, pageConfigType, pageId } = record;
    if (pageConfigId || pageId) {
      // 请求详情信息
      setLoading(true);
      const params = {
        pageConfigId: pageId || pageConfigId,
        pageConfigType: pageId ? 1 : pageConfigType,
      };
      const res = await getBenefitPageDetail(params);
      if (res?.code === '200' && res.data) {
        const { welfareBannerInfoList, startValidity, endValidity } = res.data;
        const tempRecord = {
          ...res.data,
          welfareBannerInfoList:
            welfareBannerInfoList?.map((item) => ({
              ...item,
              id: item.id || nanoid(),
            })) || [],
          pageConfigType,
        };
        if (startValidity || endValidity) {
          tempRecord.activityTime = [startValidity, endValidity];
        }
        setInitialConfig(tempRecord);
      }
    } else {
      setInitialConfig({});
    }
    setActionType(actionTypeParam);
    setLoading(false);
    setModalVisible(true);
  };

  // 更新页面数据
  const handleUpdate = async (record) => {
    const { pageId } = record;
    const params = {
      pageId,
    };
    const res = await pageCacheClear(params);
    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
      });
    }
  };

  // 复制链接
  const handleCopy = (record) => {
    const { pageKey, pageId } = record;
    let link = '';
    if (pageKey === 'walletWelfareCenter') {
      link = `${window.location.protocol}//m.vivojrkj.com/favour/home/<USER>
    } else {
      link = `${window.location.protocol}//m.vivojrkj.com/favour/home/<USER>
    }
    copyContent(link);
    notification.success({ message: '已经复制了～', duration: 1 });
  };

  const childrenColumns = [
    {
      title: '配置状态',
      dataIndex: 'pageConfigType',
      key: 'pageConfigType',
      align: 'center',
      valueEnum: {
        1: '线上生效配置',
        2: '最新提交配置',
      },
      width: 200,
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      key: 'auditStatus',
      align: 'center',
      valueEnum: auditValueEnum,
      width: 200,
    },
    {
      title: '上线状态',
      dataIndex: 'onlineStatus',
      key: 'onlineStatus',
      valueEnum: onlineValueEnum,
      ellipsis: true,
      align: 'center',
      width: 200,
    },
    {
      title: '操作人',
      dataIndex: 'operatorName',
      key: 'operatorName',
      hideInSearch: true,
      ellipsis: true,
      align: 'center',
      width: 200,
    },
    {
      title: '操作时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      valueType: 'dateTime',
      hideInSearch: true,
      ellipsis: true,
      align: 'center',
      width: 200,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      hideInSearch: true,
      align: 'center',
      fixed: 'right',
      width: 350,
      render: (text, record) => (
        <Space split={<Divider type="vertical" />} key={record.pageConfigId}>
          <Button
            key="edit"
            type="link"
            size="small"
            onClick={() => {
              handleAction(record, 'edit');
            }}
          >
            编辑
          </Button>
          {[2, 3].includes(record.auditStatus) && (
            <Button
              key="detail"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'view');
              }}
            >
              详情
            </Button>
          )}
          {record.auditStatus === 1 && (
            <Button
              key="audit"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'audit');
              }}
            >
              审核
            </Button>
          )}
          {record.onlineStatus === 2 && (
            <Popconfirm
              title="紧急下线操作"
              description="下线操作会导致C端用户活动不可见，确认要下线吗？"
              onConfirm={() => changeOnlineStatus(record)}
              key="offline"
              onCancel={() => { }}
              okText="确定"
              cancelText="取消"
            >
              <Button key="offlinebtn" type="link" size="small">
                下线
              </Button>
            </Popconfirm>
          )}
          {record.onlineStatus === 1 && record.auditStatus === 2 && (
            <Button
              key="online"
              type="link"
              size="small"
              onClick={() => changeOnlineStatus(record)}
            >
              上线
            </Button>
          )}
        </Space>
      ),
    },
  ];

  const columns = [
    {
      title: '页面状态',
      dataIndex: 'pageStatus',
      key: 'pageStatus',
      hideInSearch: true,
      valueEnum: pageValueEnum,
      ellipsis: true,
      align: 'center',
      fixed: 'left',
      width: 150,
    },
    {
      title: '上线状态',
      dataIndex: 'onlineStatus',
      key: 'onlineStatus',
      hideInTable: true,
      valueEnum: onlineValueEnum,
      ellipsis: true,
      align: 'center',
      fixed: 'left',
      width: 150,
    },
    {
      title: '页面名称',
      dataIndex: 'pageName',
      key: 'pageName',
      ellipsis: true,
      align: 'center',
      fixed: 'left',
      width: 150,
    },
    {
      title: '页面位置',
      valueType: 'select',
      dataIndex: 'pageKey',
      key: 'pageKey',
      valueEnum: {
        walletWelfareCenter: '省钱首页入口',
        otherAdEntrance: '其他投放入口',
      },
      hideInSearch: true,
      ellipsis: true,
      align: 'center',
      width: 150,
    },
    {
      title: '面客tab',
      dataIndex: 'welfareTabNameList',
      key: 'welfareTabNameList',
      hideInSearch: true,
      ellipsis: true,
      align: 'center',
      width: 200,
      render: (text, record) => (
        <Tooltip placement="top" title={record.welfareTabNameList?.join(',')}>
          {record.welfareTabNameList?.join(',')}
        </Tooltip>
      ),
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      key: 'auditStatus',
      align: 'center',
      hideInTable: true,
      ellipsis: true,
      valueEnum: auditValueEnum,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      hideInSearch: true,
      align: 'center',
      fixed: 'right',
      width: 350,
      render: (text, record) => (
        <Space split={<Divider type="vertical" />} key={record.pageConfigId}>
          <Button
            key="copy"
            type="link"
            size="small"
            onClick={() => {
              handleAction(record, 'copy');
            }}
          >
            复制
          </Button>

          <Popconfirm
            title="是否立即更新页面数据"
            onConfirm={() => handleUpdate(record)}
            key="offline"
            onCancel={() => { }}
            okText="确定"
            cancelText="取消"
          >
            <Button
              key="update"
              type="link"
              size="small"
            >
              更新
            </Button>
          </Popconfirm>
          {[2, 3].includes(record.pageStatus)
            && record.pageKey !== 'walletWelfareCenter' && (
              <Button
                key="link"
                type="link"
                size="small"
                onClick={() => {
                  handleCopy(record);
                }}
              >
                复制链接
              </Button>
          )}
        </Space>
      ),
    },
  ];

  // 列表数据请求
  const getTableData = async (params) => {
    const param = {
      page: params.current,
      ...params,
    };
    delete param.current;

    const res = await getBenefitPageList(param);
    const { data } = res;
    return {
      data: (data.list || []).map((item) => ({
        ...item,
        rowKey: nanoid(),
      })),
      total: data.total,
    };
  };

  const ExpandedRowRender = memo(({ record }) => (
    <CommonTableList
      columns={childrenColumns}
      headerTitle={false}
      search={false}
      options={false}
      dataSource={record.pageConfigList}
      pagination={false}
    />
  ));

  return (
    <>
      <ProCard title="页面管理">
        <Spin spinning={loading}>
          <CommonTableList
            search={{ defaultCollapsed: false }}
            columns={columns}
            actionRef={actionRef}
            request={getTableData}
            expandable={{
              expandedRowRender: (record) => (
                <ExpandedRowRender record={record} />
              ),
            }}
            rowKey="rowKey"
            toolBarRender={() => [
              <Button
                key="button"
                icon={<PlusOutlined />}
                onClick={() => {
                  handleAction({}, 'add');
                }}
                type="primary"
              >
                新建
              </Button>,
            ]}
          />
        </Spin>
      </ProCard>
      <DetailModal
        setModalVisible={setModalVisible}
        modalVisible={modalVisible}
        setActionType={setActionType}
        initialConfig={initialConfig}
        setInitialConfig={setInitialConfig}
        actionType={actionType}
        handleReload={handleReload}
        operateHistoryRender={operateHistoryRender}
      />
    </>
  );
};

export default ConfigManagement;
