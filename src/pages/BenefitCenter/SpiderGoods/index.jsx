import React, { Fragment, useEffect, useRef, useState } from 'react';
import CommonTableList from '@/components/CommonTableList';
import { getSpiderContentList, changeSpiderStatus } from '@/services/spider.js';
import { ModalForm, BetaSchemaForm } from '@ant-design/pro-components';
import { checkNumericRange } from '@/utils/validate.js';
import {
  Row,
  Image,
  Tree,
  Space,
  Divider,
  Button,
  Notification,
  Spin,
  Input,
  notification,
  Popconfirm,
} from 'antd';
const { TextArea } = Input;
import NumericRange from '@/components/NumericRange';
import dayjs from 'dayjs';
import { copyContent, extractPlainTextFromRichText } from '@/utils/util.js';
import { getCategories } from '@/services/benefitCenter.js';
import { getAutoPushDetail } from '@/services/spider.js';
import DetailModal from '../ContentManagement/components/DetailModal'; // 复用选品弹窗 通过isAutomaticPush标记

const SpiderGoods = () => {
  const actionRef = useRef();
  const [categoryOptions, setCategoryOptions] = useState([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [actionType, setActionType] = useState('add');
  const [initialConfig, setInitialConfig] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getCategoryOptions();
  }, []);

  /**
   * 获取福利内容列表
   */
  const getCategoryOptions = async () => {
    const res = await getCategories();
    if (res?.code === '200') {
      setCategoryOptions(res.data);
    }
  };

  /**
   * 列表数据请求
   */
  const getTableData = async (params) => {
    const { publishedTimeRange, fetchTimeRange } = params;
    const tempParams = {
      page: params.current,
      ...params,
    };
    delete tempParams.current;
    if (publishedTimeRange) {
      tempParams.publishedTimeRange = {
        startTime: dayjs(publishedTimeRange[0]).format('YYYY-MM-DDT00:00:00'), // 2024-05-18T00:00:00
        endTime: dayjs(publishedTimeRange[1]).format('YYYY-MM-DDT23:59:59'),
      };
    }
    if (fetchTimeRange) {
      tempParams.fetchTimeRange = {
        startTime: dayjs(fetchTimeRange[0]).format('YYYY-MM-DDT00:00:00'),
        endTime: dayjs(fetchTimeRange[1]).format('YYYY-MM-DDT23:59:59'),
      };
    }
    const res = await getSpiderContentList(tempParams);
    if (res?.code === '200' && res.data) {
      return {
        data: res.data.list,
        total: res.data.total,
      };
    } else {
      notification.error({
        message: '获取数据失败',
        description: res?.msg,
      });
      return {
        data: [],
        total: 0,
      };
    }
  };

  /**
   * 列表刷新
   */
  const handleReload = () => {
    actionRef.current?.reload();
  };

  /**
   * 修改选品状态
   */
  const changeSelectedStatus = async (record) => {
    const params = {
      id: record.id,
      selected: record.productStatus === 0 ? true : false,
    };
    const res = await changeSpiderStatus(params);
    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
      });
      handleReload();
    }
  };

  /**
   * 自动推送
   */
  const handleAutomaticPush = async (record) => {
    // 获取信息详情
    setLoading(true);
    const res = await getAutoPushDetail({ id: record.id });
    if (res?.code === '200') {
      const { data } = res;
      const { welfareDiscountInfo, startValidity, endValidity } = data;
      let tempWelfareDiscountInfo = {};
      if (welfareDiscountInfo) {
        const { discountPrice, originPrice } = welfareDiscountInfo;
        tempWelfareDiscountInfo = {
          ...welfareDiscountInfo,
          discountPrice: discountPrice / 100,
          originPrice: originPrice / 100,
        };
      }
      const tempData = {
        ...data,
        welfareDiscountInfo: tempWelfareDiscountInfo,
      };

      setInitialConfig(tempData);
      setActionType('add');
      setModalVisible(true);
    }
    setLoading(false);
  };

  const contentColumn = [
    {
      title: '原文链接',
      dataIndex: 'detailUrl',
      key: 'detailUrl',
      render: (text) => {
        return (
          <a href={text} target="_blank" rel="noreferrer">
            {text}
          </a>
        );
      },
    },
    {
      title: '原文内容',
      dataIndex: 'content',
      key: 'content',
      render: (schema, config, form) => {
        return (
          <>
            <Button
              type="primary"
              style={{ marginBottom: 10 }}
              onClick={() => {
                const value = extractPlainTextFromRichText(config.value);
                copyContent(value);
                notification.success({ message: '已经复制了～', duration: 1 });
              }}
            >
              一键复制
            </Button>
            <div
              style={{
                maxHeight: 200,
                overflow: 'scroll',
                border: '1px solid #999',
                borderRadius: 10,
                padding: 5,
              }}
              dangerouslySetInnerHTML={{ __html: config.value }}
            ></div>
          </>
        );
      },
    },
    {
      title: '直达链接',
      dataIndex: 'sourceUrl',
      key: 'sourceUrl',
      render: (text) => {
        return (
          <a href={text} target="_blank" rel="noreferrer">
            {text}
          </a>
        );
      },
    },
    {
      title: '福利位置',
      dataIndex: 'navigation',
      key: 'navigation',
    },
  ];

  const columns = [
    {
      title: '序号',
      dataIndex: 'id',
      key: 'id',
      valueType: 'digit',
      align: 'center',
      width: 90,
      fixed: 'left',
      ellipsis: true,
      fieldProps: {
        precision: 0,
      },
    },
    {
      title: '介绍头图',
      dataIndex: 'pic',
      key: 'pic',
      hideInSearch: true,
      width: 120,
      align: 'center',
      ellipsis: true,
      fixed: 'left',
      render: (schema, config, form) => {
        return <Image src={config.picUrl} referrerPolicy="no-referrer" />;
      },
    },
    {
      title: '商品标题',
      dataIndex: 'title',
      key: 'title',
      width: 220,
      align: 'center',
      fixed: 'left',
    },
    {
      title: '价格',
      dataIndex: 'price',
      key: 'price',
      valueType: 'digit',
      hideInSearch: true,
      fixed: 'left',
      width: 120,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '信息来源平台',
      dataIndex: 'infoPlatform',
      key: 'infoPlatform',
      valueType: 'select',
      valueEnum: {
        1: '什么值得买', // 1-什么值得买
      },
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
      width: 140,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '信息展示渠道',
      dataIndex: 'infoShowType',
      key: 'infoShowType',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
      valueEnum: {
        1: '优惠券',
        2: '白菜头条',
        3: '好价排行榜',
      },
      width: 140,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '福利所属平台',
      dataIndex: 'source',
      key: 'source',
      width: 140,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '价格',
      dataIndex: 'priceRange',
      key: 'priceRange',
      hideInTable: true,
      formItemProps: (form) => {
        return {
          rules: [
            {
              validator: checkNumericRange,
            },
          ],
        };
      },
      renderFormItem: (schema, config, form) => {
        return <NumericRange />;
      },
    },
    {
      title: '点值数',
      dataIndex: 'worthy',
      key: 'worthy',
      valueType: 'digit',
      hideInSearch: true,
      width: 140,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '点值数',
      dataIndex: 'worthyRange',
      key: 'worthyRange',
      hideInTable: true,
      formItemProps: (form) => {
        return {
          rules: [
            {
              validator: checkNumericRange,
            },
          ],
        };
      },
      renderFormItem: (schema, config, form) => {
        return <NumericRange />;
      },
    },
    {
      title: '收藏数',
      dataIndex: 'star',
      key: 'star',
      valueType: 'digit',
      hideInSearch: true,
      width: 140,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '收藏数',
      dataIndex: 'starRange',
      key: 'starRange',
      hideInTable: true,
      formItemProps: (form) => {
        return {
          rules: [
            {
              validator: checkNumericRange,
            },
          ],
        };
      },
      renderFormItem: (schema, config, form) => {
        return <NumericRange />;
      },
    },
    {
      title: '评论数',
      dataIndex: 'comments',
      key: 'comments',
      valueType: 'digit',
      hideInSearch: true,
      width: 140,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '评论数',
      dataIndex: 'commentsRange',
      key: 'commentsRange',
      hideInTable: true,
      formItemProps: (form) => {
        return {
          rules: [
            {
              validator: checkNumericRange,
            },
          ],
        };
      },
      renderFormItem: (schema, config, form) => {
        return <NumericRange />;
      },
    },
    {
      title: '不值数',
      dataIndex: 'unworthy',
      key: 'unworthy',
      valueType: 'digit',
      hideInSearch: true,
      width: 140,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '爆料发布时间',
      dataIndex: 'publishedTime',
      key: 'publishedTime',
      valueType: 'dateTime',
      hideInSearch: true,
      width: 180,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '爆料发布时间',
      dataIndex: 'publishedTimeRange',
      key: 'publishedTimeRange',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '信息获取时间',
      dataIndex: 'fetchTime',
      key: 'fetchTime',
      valueType: 'dateTime',
      hideInSearch: true,
      width: 180,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '信息获取时间',
      dataIndex: 'fetchTimeRange',
      key: 'fetchTimeRange',
      valueType: 'dateRange',
      hideInTable: true,
    },
    {
      title: '直达链接',
      dataIndex: 'sourceUrl',
      key: 'sourceUrl',
      hideInSearch: true,
      copyable: true,
      width: 250,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '内容分类',
      dataIndex: 'categoryPathStr',
      key: 'categoryPathStr',
      hideInSearch: true,
      width: 180,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '选品操作人',
      dataIndex: 'operatorName',
      key: 'operatorName',
      hideInSearch: true,
      width: 140,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '选品更新时间',
      dataIndex: 'operateTime',
      key: 'updateTime',
      valueType: 'dateTime',
      hideInSearch: true,
      width: 180,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '商品id',
      dataIndex: 'productId',
      key: 'productId',
      width: 130,
      align: 'center',
      copyable: true,
      ellipsis: true,
      fixed: 'right',
    },
    {
      title: '选品状态',
      dataIndex: 'productStatus',
      key: 'productStatus',
      valueType: 'select',
      fixed: 'right',
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
      valueEnum: {
        0: { text: '未选中', status: 'Default' },
        1: { text: '已选中', status: 'Success' },
        2: { text: '已推送', status: 'Processing' },
        3: { text: '推送失败', status: 'Error' },
        4: { text: '已上架', status: 'Success' },
        5: { text: '已下架', status: 'Warning' },
      },
      width: 120,
      align: 'center',
      ellipsis: true,
    },
    // {
    //   dataIndex: 'empty', // 空内容占位
    //   key: 'empty',
    //   renderFormItem: () => {
    //     return <div></div>
    //   }
    // },
    // {
    //   dataIndex: 'empty1', // 空内容占位
    //   key: 'empty1',
    //   renderFormItem: () => {
    //     return <div></div>
    //   }
    // },
    {
      title: '内容分类',
      dataIndex: 'categoryIdList',
      key: 'categoryIdList',
      valueType: 'cascader',
      hideInTable: true,
      fieldProps: {
        multiple: true,
        showSearch: true,
        fieldNames: {
          label: 'name',
          children: 'children',
          value: 'id',
        },
        allowClear: true,
        options: categoryOptions,
        showCheckedStrategy: 'SHOW_PARENT',
        displayRender: (selectedOptions) => {
          return selectedOptions.join('/');
        },
      },
      formItemProps: {
        style: {
          width: '600px',
        },
      },
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作',
      valueType: 'action',
      key: 'action',
      hideInSearch: true,
      fixed: 'right',
      width: 200,
      align: 'center',
      render: (text, record, _, action) => {
        return (
          <>
            {record.productStatus === 0 && (
              <Button
                key="edit"
                type="link"
                size="small"
                onClick={() => {
                  changeSelectedStatus(record);
                }}
              >
                纳入选品
              </Button>
            )}
            {record.productStatus === 1 && (
              <Popconfirm
                title="确认取消选中吗"
                onConfirm={() => changeSelectedStatus(record)}
                key="offline"
                onCancel={() => { }}
                okText="确定"
                cancelText="取消"
              >
                <Button key="edit" type="link" size="small" onClick={() => { }}>
                  取消选中
                </Button>
              </Popconfirm>
            )}
            {/* 平台是 京东、拼多多 & 渠道是 好价排行、白菜头条 */}
            {['京东', '拼多多'].indexOf(record.source) !== -1 &&
              [2, 3].indexOf(record.infoShowType) !== -1 && (
                <Button
                  key="auto"
                  type="link"
                  size="small"
                  onClick={() => {
                    handleAutomaticPush(record);
                  }}
                  disabled={[0, 1].indexOf(record.productStatus) === -1}
                >
                  自动推送
                </Button>
              )}
            <ModalForm
              title="福利详情"
              modalProps={{
                destroyOnClose: true,
              }}
              layout={'horizontal'}
              trigger={<Button type="link">查看详情</Button>}
              initialValues={record}
              submitter={false}
              readonly
              labelCol={{ span: 3 }}
              wrapperCol={{ span: 20 }}
            >
              <BetaSchemaForm layoutType="Embed" columns={contentColumn} />
            </ModalForm>
          </>
        );
      },
    },
  ];

  return (
    <Fragment>
      <Spin spinning={loading}>
        <CommonTableList
          key="spiderGoods"
          columns={columns}
          request={getTableData}
          actionRef={actionRef}
          toolbar={{
            subTitle: '仅【京东】【拼多多】且渠道为【白菜头条】【好价排行榜】支持纳入选品自动推送至【内容管理】，其余平台与优惠券渠道内容暂不支持',
          }}
          form={{
            ignoreRules: false,
          }}
          search={{
            defaultCollapsed: false,
            labelWidth: 100,
          }}
        />
      </Spin>
      {/* 自动推送弹窗 */}
      <DetailModal
        title="自动推送"
        setModalVisible={setModalVisible}
        modalVisible={modalVisible}
        setActionType={setActionType}
        actionType={actionType}
        handleReload={handleReload}
        setInitialConfig={setInitialConfig}
        initialConfig={initialConfig}
        isAutomaticPush={true}
      />
    </Fragment>
  );
};

export default SpiderGoods;
