import React, { useEffect, useRef, useState } from 'react';
import {
  BetaSchemaForm,
  ProCard,
  EditableProTable,
} from '@ant-design/pro-components';
import {
  Button, notification, Row, Space, Notification,
} from 'antd';
import {
  getOnlineCommissionConfig,
  getAuditCommissionConfig,
  getCategoryIdsInfo,
  getTagIdsInfo,
  submitCommissionConfig,
  commissionConfigAudit,
} from '@/services/benefitCenter';
import { nanoid } from 'nanoid';
import AuditCard from '@/components/AuditCard';
import ProductModal from '../PageManagement/components/ProductModal';

const CommissionConfig = (props) => {
  console.log('props', props);
  const { actionType, configData, upDateHistory } = props;
  const formRef = useRef();
  const [initialConfig, setInitialConfig] = useState({});
  const [editableKeys, setEditableRowKeys] = useState([]);
  const [productModalVisible, setProductModalVisible] = useState(false);
  const [autoFilterCondition, setAutoFilterCondition] = useState({});
  const [welfareContentList, setWelfareContentList] = useState([]); // 选中的品
  const [editProductIndex, setEditProductIndex] = useState(''); // 选中的选品行
  const [auditData, setAuditData] = useState({}); // 审核人和审核状态

  const formDisabled = ['audit', 'view'].includes(actionType);

  // 获取审核数据
  const getAuditConfig = async (noDetail) => {
    const res = await getAuditCommissionConfig();
    console.log('res====', res);
    if (res?.code === '200') {
      const data = res.data || {};
      const { auditStatus } = data;
      setAuditData({ auditStatus, operatorName: '' });
      if (!noDetail) {
        setInitialConfig(data);
        formRef.current?.setFieldsValue(data);
      }
    }
  };

  // 获取最新线上数据
  const getOnlineConfig = async () => {
    const res = await getOnlineCommissionConfig() || {};
    if (res?.code === '200') {
      console.log('====getOnlineConfig=====', res.data);
      const { specialCommissionRateList = [] } = res.data || {};
      if (specialCommissionRateList.length > 0) {
        setEditableRowKeys(specialCommissionRateList.map((item) => item.id));
      }
      setInitialConfig(res.data);
      formRef.current?.setFieldsValue(res.data);
    }
  };

  // 选品弹窗出现
  const showProductModal = (params) => {
    const { row } = params;
    const { selectedContentList } = row;
    console.log('======params====', params);
    setProductModalVisible(true);
    setEditProductIndex(row.index);
    setWelfareContentList(selectedContentList);
    setAutoFilterCondition(row.autoFilterCondition);
  };

  // 选品弹窗
  const handleProductSelected = async (values) => {
    const {
      selectedProductList = [],
      autoFilterCondition: newAutoFilterConditionKey,
      checkedCondition,
    } = values;
    const item = formRef.current.getFieldValue([
      'specialCommissionRateList',
      editProductIndex,
    ]) || {};

    try {
      let tagNameList = null;
      let categoryName = null;
      let tempItem = {};
      if (checkedCondition) {
        if (newAutoFilterConditionKey?.categoryId) {
          const res1 = await getCategoryIdsInfo({
            idList: [newAutoFilterConditionKey.categoryId],
          });
          if (res1?.code === '200' && res1?.data) {
            categoryName = res1.data.map((categoryItem) => categoryItem.name).join(',');
          }
        }
        if ((newAutoFilterConditionKey?.tagIdList || []).length > 0) {
          const res2 = await getTagIdsInfo({
            idList: newAutoFilterConditionKey.tagIdList,
          });
          if (res2.code === '200' && res2?.data) {
            tagNameList = res2.data.map((tagItem) => tagItem.name);
          }
        }
        tempItem = {
          ...item,
          autoFilterCondition: {
            ...newAutoFilterConditionKey,
            categoryName,
            tagNameList,
          },
          selectedContentList: selectedProductList,
          type: 1,
        };
      } else {
        tempItem = {
          ...item,
          selectedContentList: selectedProductList,
          autoFilterCondition: null,
          type: 2,
        };
      }

      let tempList = JSON.parse(JSON.stringify(formRef.current.getFieldValue('specialCommissionRateList')));
      tempList = tempList.map((cItem, i) => {
        if (i === editProductIndex) {
          return tempItem;
        }
        return cItem;
      });

      formRef.current.setFieldsValue({
        specialCommissionRateList: tempList,
      });
    } catch (error) {
      console.log('err=====', error);
    }
  };

  // 配置数据展示处理
  const handleConfigRender = (record) => {
    let descDom;
    const { autoFilterCondition: newAutoFilterConditionKey, selectedContentList } = record;
    if (newAutoFilterConditionKey) {
      const {
        categoryId, autoPush, tagIdList = [], categoryName, tagNameList,
      } = newAutoFilterConditionKey;
      if (
        categoryId
        || tagIdList.length > 0
        || [0, 1].includes(autoPush)
      ) {
        descDom = (
          <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'start' }}>
            <div>【自动更新】</div>
            {categoryName && (
              <div style={{ textAlign: 'start' }}>
                已选类目：
                {categoryName}
              </div>
            )}
            {tagNameList?.length > 0 && (
              <div style={{ textAlign: 'start' }}>
                已选标签：
                {tagNameList.join(',')}
              </div>
            )}
            {[0, 1].includes(autoPush) && (
              <div>
                自动推送：
                {autoPush === 1 ? '是' : '否'}
              </div>
            )}
          </div>
        );
      }
    } else if (selectedContentList?.length > 0) {
      const descData = selectedContentList.map(
        (contentItem) => contentItem.welfareMainTitle,
      );
      descDom = (
        <div style={{
          display: 'flex', flexDirection: 'column', alignItems: 'start', maxHeight: 120, overflow: 'scroll',
        }}
        >
          <div>【自选】</div>
          <div>{descData.join(',')}</div>
        </div>
      );
    }
    return descDom;
  };

  const fetchData = () => {
    if (actionType === 'edit') {
      getOnlineConfig();
      // 只获取审核人和状态
      getAuditConfig(true);
    } else if (actionType === 'audit') {
      getAuditConfig();
    } else {
      setInitialConfig(configData);
      formRef.current.setFieldsValue(configData);
    }
  };

  useEffect(() => {
    fetchData();
  }, []);

  const editableColumn = [
    {
      title: '配置',
      align: 'center',
      renderFormItem: (schema, config) => {
        const dom = handleConfigRender(config.record);
        return dom;
      },
      render: (text, record) => handleConfigRender(record),
    },
    {
      title: '分佣比例',
      dataIndex: 'commissionRate',
      valueType: 'digit',
      // width: 150,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      align: 'center',
      fieldProps: {
        max: 100,
        min: 1,
        precision: 0,
        placeholder: '1-100的数字',
        addonAfter: '%',
      },
      render: (text, record) => `${record.commissionRate}%`,
    },
    {
      title: '操作',
      valueType: 'option',
      width: 200,
      key: 'option',
      align: 'center',
      fixed: 'right',
      render: () => null,
    },
  ];

  const columns = [
    {
      title: '通用分佣比例',
      dataIndex: 'commonCommissionRate',
      valueType: 'digit',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
        extra: '此分佣比例适用所有除应用特殊分佣比例外的商品分佣，目前支持拼多多与京东商品',
      },
      fieldProps: {
        max: 100,
        min: 1,
        precision: 0,
        placeholder: '1-100的数字',
        addonAfter: '%',
        style: { width: '300px' },
      },
    },
    {
      title: '特殊分佣比例',
      renderFormItem: () => (
        <>
          <div style={{ marginBottom: 10, color: 'rgba(0, 0, 0, 0.45)' }}>此分佣比例支持为对应商品统一配置对应分佣比例</div>
          <EditableProTable
            name="specialCommissionRateList"
            columns={editableColumn}
            rowKey="id"
            scroll={{
              y: 400,
            }}
            // initialValue={initialConfig.specialCommissionRateList}
            recordCreatorProps={{
              newRecordType: 'dataSource',
              creatorButtonText: '新增特殊分佣',
              record: () => ({
                id: nanoid(),
              }),
            }}
            editable={{
              type: 'multiple',
              editableKeys,
              actionRender: (row, config, defaultDoms) => [<Button type="link" onClick={() => showProductModal({ row, config })}>选品</Button>, defaultDoms.delete],
              onChange: setEditableRowKeys,
            }}
          />
        </>
      ),
    },
  ];

  // 提交审核
  const onSubmit = async () => {
    const val = await formRef.current?.validateFields();
    delete val[1];
    if (val.specialCommissionRateList?.length) {
      const emptyItem = val.specialCommissionRateList.find((item) => {
        const a = item.autoFilterCondition;
        let emptyCondition = false;
        if (!a?.categoryId && !a?.tagIdList?.length && ![0, 1].includes(a?.autoPush)) {
          emptyCondition = true;
        }
        return (!(item.selectedContentList || []).length) && emptyCondition;
      });
      if (emptyItem) {
        notification.error({
          message: '配置项缺失',
        });
        return;
      }
    }

    const res = await submitCommissionConfig(val);
    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
      });
      fetchData();
    }
  };

  // 审核通过/审核驳回
  const onAudit = async (isAllow) => {
    const params = {
      auditOperation: isAllow ? 2 : 3,
      id: initialConfig.id,
    };
    const res = await commissionConfigAudit(params);
    if (res?.code === '200') {
      Notification.success({ description: '操作成功' });
      getAuditConfig();
      // 刷新操作记录
      upDateHistory();
    }
  };

  return (
    <>
      {
        !['view', 'audit'].includes(actionType) && (
          <AuditCard
            auditData={auditData}
            onSubmit={onSubmit}
            type="BenefitCenter"
            scene="commission"
            id={initialConfig?.id}
          />
        )
      }
      <ProCard
        style={{
          marginBlockEnd: 10,
          marginBlockStart: 10,
        }}
        title="佣金配置管理"
        subTitle="此配置审核通过后实时更新（此处配置的分佣比例为税前 请谨慎配置）"
      >
        <BetaSchemaForm
          formRef={formRef}
          columns={columns}
          layout="horizontal"
          submitter={false}
          labelCol={{ span: 3 }}
          wrapperCol={{ span: 20 }}
          disabled={formDisabled}
          initialValues={initialConfig}
        />

        <ProductModal
          setModalVisible={setProductModalVisible}
          modalVisible={productModalVisible}
          welfareContentList={welfareContentList}
          setWelfareContentList={setWelfareContentList}
          autoFilterCondition={autoFilterCondition}
          setAutoFilterCondition={setAutoFilterCondition}
          setEditProductIndex={setEditProductIndex}
          handleProductSelected={handleProductSelected}
        />
        {/* 审核按钮 */}
        {
          actionType === 'audit' && initialConfig.id && (
            <Row>
              <Space>
                <Button
                  key="pass"
                  onClick={() => {
                    onAudit(true);
                  }}
                  type="primary"
                >
                  审核通过
                </Button>
                <Button
                  key="reject"
                  onClick={() => {
                    onAudit(false);
                  }}
                  type="primary"
                >
                  审核驳回
                </Button>
              </Space>
            </Row>
          )
        }
      </ProCard>
    </>
  );
};

export default CommissionConfig;
