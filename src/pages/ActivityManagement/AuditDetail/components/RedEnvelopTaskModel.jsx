import React, { useState } from "react";
import { Drawer, Modal } from "antd";
import { ProCard } from "@ant-design/pro-components";

import RedEnvelopTaskDetail from "../../RedEnvelopTaskConfig/index";
import RedEnvelopTaskDetailNew from "../../RedEnvelopTaskConfigNew/index";
import AuditInfoCard from "@/components/AuditInfoCard";

const ExchangeModel = ({ modalVisible, setVisible, detailData, scene }) => {
  return (
    <Drawer
      title="操作详情"
      width={1500}
      open={modalVisible}
      onClose={() => {
        setVisible(false);
      }}
    >
      <ProCard title="审核详情">
        {scene === "redEnvelopTask" && (
          <RedEnvelopTaskDetail isAudit={true} detailData={detailData} />
        )}
        {scene === "redEnvelopTaskNew" && (
          <RedEnvelopTaskDetailNew isAudit={true} detailData={detailData} />
        )}
      </ProCard>
      <AuditInfoCard itemOperationLog={detailData} />
    </Drawer>
  );
};

export default ExchangeModel;
