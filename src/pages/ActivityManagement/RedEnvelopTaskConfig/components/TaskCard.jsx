import { Button, Space } from 'antd';
import { BetaSchemaForm, ProCard } from '@ant-design/pro-components';
import { useDrop, useDrag } from 'react-dnd';
import { Fragment, useRef, forwardRef } from 'react';
import { getFormItemsByTaskType, getFormItemsByShowList } from './config';
import BraftEditor from 'braft-editor';
import 'braft-editor/dist/index.css';
import './BraftEditor.css';

const DragItem = forwardRef(
  ({ id, index, text, changePosition, type, showList }, formRef) => {
    const ref = useRef(null);

    const [, drop] = useDrop({
      accept: 'DragDropBox', // 只对useDrag的type的值为DragDropBox时才做出反应
      drop: (item, monitor) => {
        const dragIndex = item.index;
        const hoverIndex = index;
        if (dragIndex === hoverIndex) return; // 如果回到自己的坑，那就什么都不做
        changePosition(dragIndex, hoverIndex); // 调用传入的方法完成交换
      },
    });

    const columns = showList
      ? [
          {
            valueType: 'group',
            columns: getFormItemsByShowList(showList),
          },
        ]
      : [
          {
            valueType: 'group',
            columns: getFormItemsByTaskType(type),
          },
        ];

    const dragIcon = (
      <svg
        viewBox="64 64 896 896"
        focusable="false"
        data-icon="holder"
        width="1em"
        height="1em"
        fill="currentColor"
        aria-hidden="true"
      >
        <path d="M300 276.5a56 56 0 1056-97 56 56 0 00-56 97zm0 284a56 56 0 1056-97 56 56 0 00-56 97zM640 228a56 56 0 10112 0 56 56 0 00-112 0zm0 284a56 56 0 10112 0 56 56 0 00-112 0zM300 844.5a56 56 0 1056-97 56 56 0 00-56 97zM640 796a56 56 0 10112 0 56 56 0 00-112 0z" />
      </svg>
    );
    const title = (
      <div style={{ display: 'flex', alignItems: 'center' }}>
        {text}&nbsp;{dragIcon}
      </div>
    );

    const [{ isDragging }, drag] = useDrag(() => ({
      type: 'DragDropBox',
      item: {
        id,
        index,
        text,
      },
      collect: (monitor) => ({
        isDragging: monitor.isDragging(), // css样式需要
      }),
    }));
    return (
      <div ref={drag(drop(ref))} style={{ opacity: isDragging ? 0.5 : 1 }}>
        <ProCard title={title} boxShadow>
          <BetaSchemaForm
            formRef={formRef}
            autoFocusFirstInput={false}
            layoutType="Embed"
            columns={columns}
            submitter={{
              resetButtonProps: {
                style: {
                  display: 'none',
                },
              },
              submitButtonProps: {
                style: {
                  display: 'none',
                },
              },
            }}
          />
        </ProCard>
      </div>
    );
  },
);

export default DragItem;
