import { BetaSchemaForm, ProCard } from "@ant-design/pro-components";
import { forwardRef } from "react";
import { getTitle, getFormItemsByShowList } from "./config";
import "braft-editor/dist/index.css";
import "./BraftEditor.css";

const DragItem = forwardRef(({ showItem }, formRef) => {
  const cardList = getFormItemsByShowList(showItem);
  const columns = cardList;
  // test
  return (
    <div>
      <ProCard title={getTitle(showItem)} boxShadow>
        <BetaSchemaForm
          formRef={formRef}
          autoFocusFirstInput={false}
          layoutType="Embed"
          columns={columns}
          submitter={{
            resetButtonProps: {
              style: {
                display: "none",
              },
            },
            submitButtonProps: {
              style: {
                display: "none",
              },
            },
          }}
        />
      </ProCard>
    </div>
  );
});

export default DragItem;
