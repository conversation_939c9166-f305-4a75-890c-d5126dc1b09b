/* eslint-disable */
import BraftEditor from 'braft-editor';
import 'braft-editor/dist/index.css';
import './BraftEditor.css';
import {
  validateTrialDuration,
  validateMoneyNotNegativeInteger,
  validateNotNegativeInteger,
  validatePositiveInteger,
  validateAlphanumeric,
  validatePositiveMoney,
} from '@/utils/validate';
import React from 'react';
// 公共
export const FORM_ITEMS_CONFIG_COMMON = {
  copyWriting: {
    title: '引导文案',
    dataIndex: 'guideText',
    valueType: 'input',
    width: 'md',
    formItemProps: {
      help: '（仅可配置一行，不超过13个字）',
      rules: [
        {
          required: true,
          message: '请填写引导文案',
        },
      ],
    },
    render: (val) => {
      return <div dangerouslySetInnerHTML={{ __html: val }} />;
    },
    renderFormItem: (schema, config, form) => {
      const id = config.id;
      const data = form.getFieldsValue();
      let value = '';
      let key = '';
      let index = '';

      if (id) {
        // 对照组
        const [listKey, listIndex, listField] = id.split('_');
        value = data?.[listKey]?.[+listIndex]?.[listField] || config.value;
        key = listKey;
        index = listIndex;
      }

      const onBlur = (value) => {
        const domStr = value.toHTML();
        const ele = document.createElement('div');
        ele.innerHTML = domStr;
        const text = ele.innerText;
        form.setFieldValue([key, index, 'guideText'], domStr);
      };

      value = BraftEditor.createEditorState(value);
      return (
        <BraftEditor
          value={value}
          controls={['text-color']}
          colors={['#000000', '#EA7509']}
          textBackgroundColor={false}
          onBlur={onBlur}
        />
      );
    },
  },
  count: {
    valueType: 'group',
    columns: [
      {
        title: '每日可观看次数',
        dataIndex: 'dailyLimit',
        valueType: 'input',
        width: 'md',
        fieldProps: {
          suffix: '次',
        },
        formItemProps: {
          rules: [
            {
              required: true,
              message: '请填写每日可观看次数',
            },
            { validator: validateTrialDuration },
          ],
        },
      },
    ],
  },
  duration: {
    valueType: 'group',
    columns: [
      {
        title: '体验时长',
        dataIndex: 'trialDuration',
        valueType: 'input',
        width: 'md',
        fieldProps: {
          suffix: '秒',
        },
        formItemProps: {
          rules: [
            {
              required: true,
              message: '请填写体验时长',
            },
            { validator: validatePositiveInteger },
          ],
        },
      },
    ],
  },
  durations: {
    valueType: 'group',
    columns: [
      {
        dataIndex: '1',
        valueType: 'input',
        title: '体验时长',
        width: 'md',
        fieldProps: {
          suffix: '秒',
          prefix: '第一日',
        },
        formItemProps: {
          rules: [
            {
              required: true,
              message: '请填写第一日体验时长',
            },
            { validator: validatePositiveInteger },
          ],
        },
      },
      {
        title: '体验时长',
        dataIndex: '2',
        valueType: 'input',
        width: 'md',
        fieldProps: {
          suffix: '秒',
          prefix: '第二日',
        },
        formItemProps: {
          rules: [
            {
              required: true,
              message: '请填写第二日体验时长',
            },
            { validator: validatePositiveInteger },
          ],
        },
      },
    ],
  },
  moneyBeanAmount: {
    valueType: 'group',
    columns: [
      // {
      //   title: '发放钱豆数',
      //   dataIndex: 'eachAppReward',
      //   valueType: 'input',
      //   width: 'sm',
      //   fieldProps: {
      //     suffix: '个',
      //   },
      //   formItemProps: {
      //     extra:
      //       '为保证用户体验，发放当日调整后，不会立刻生效，会从次日0点起生效',
      //     rules: [
      //       {
      //         required: true,
      //         message: '请填写任务钱豆发放奖励数量',
      //       },
      //       { validator: validatePositiveInteger },
      //     ],
      //   },
      // },
      {
        title: '发放红包数',
        dataIndex: 'eachAppCashRedReward',
        valueType: 'money',
        width: 'sm',
        fieldProps: {
          suffix: '元',
        },
        formItemProps: {
          extra:
            '为保证用户体验，发放额当日调整后，不会立刻生效，会从次日0点起生效',
          rules: [
            {
              required: true,
              message: '请填写任务红包发放奖励数量',
            },
            { validator: validatePositiveMoney },
          ],
        },
      },
    ],
  },
  // 注册任务 安装、注册下发钱豆数
  moneyBeanAmounts: {
    valueType: 'group',
    columns: [
      {
        valueType: 'group',
        columns: [
          {
            title: '发放钱豆数（完成安装）',
            dataIndex: 'eachAppReward',
            valueType: 'input',
            width: 'md',
            fieldProps: {
              suffix: '个',
            },
            formItemProps: {
              extra:
                '为保证用户体验，发放当日调整后，不会立刻生效，会从次日0点起生效',
              rules: [
                {
                  required: true,
                  message: '请填写任务钱豆发放奖励数量',
                },
                { validator: validatePositiveInteger },
              ],
            },
          },
          {
            title: '发放钱豆数（完成注册）',
            dataIndex: 'registeredAppQdAmount',
            valueType: 'input',
            width: 'md',
            fieldProps: {
              suffix: '个',
            },
            formItemProps: {
              extra:
                '为保证用户体验，发放当日调整后，不会立刻生效，会从次日0点起生效。完成注册阶段，不允许钱豆、提现卡同时为0',
              rules: [
                {
                  required: true,
                  message: '请填写任务钱豆发放奖励数量',
                },
                { validator: validateNotNegativeInteger },
              ],
            },
          },
        ],
      },
    ],
  },
  // 注册任务 安装、注册下发提现卡数
  creditAmounts: {
    valueType: 'group',
    columns: [
      {
        title: '发放提现卡数（完成安装）',
        dataIndex: 'eachAppCashCardReward',
        valueType: 'money',
        width: 'sm',
        fieldProps: {
          suffix: '元',
        },
        formItemProps: {
          extra:
            '为保证用户体验，发放额当日调整后，不会立刻生效，会从次日0点起生效',
          rules: [
            {
              required: true,
              message: '请填写任务提现卡发放奖励数量',
            },
            { validator: validateMoneyNotNegativeInteger },
          ],
        },
      },
      {
        title: '发放提现卡数（完成注册）',
        dataIndex: 'registeredAppCashCardAmount',
        valueType: 'money',
        width: 'sm',
        fieldProps: {
          suffix: '元',
        },
        formItemProps: {
          extra:
            '为保证用户体验，发放额当日调整后，不会立刻生效，会从次日0点起生效。完成注册阶段，不允许钱豆、提现卡同时为0',
          rules: [
            {
              required: true,
              message: '请填写任务提现卡发放奖励数量',
            },
            { validator: validatePositiveMoney },
          ],
        },
      },
    ],
  },
  showAmount: {
    valueType: 'group',
    columns: [
      {
        title: '展示个数',
        dataIndex: 'dailyLimit',
        valueType: 'input',
        width: 'md',
        fieldProps: {
          suffix: '个',
        },
        formItemProps: {
          extra:
            '为保证用户体验，发放当日调整后，不会立刻生效，会从次日0点起生效',
          rules: [
            {
              required: true,
              message: '请填写展示个数',
            },
            { validator: validateTrialDuration },
          ],
        },
      },
    ],
  },
  showDailyAwardsLimit: {
    valueType: 'group',
    columns: [
      {
        title: '单日任务领奖个数限制（必须大于0）',
        dataIndex: 'dailyAwardsLimit',
        valueType: 'input',
        width: 'md',
        fieldProps: {
          suffix: '个',
        },
        formItemProps: {
          extra:
            '为保证用户体验，发放当日调整后，不会立刻生效，会从次日0点起生效',
          rules: [
            {
              required: true,
              message: '请填写展示个数',
            },
            { validator: validatePositiveInteger },
          ],
        },
      },
    ],
  },
  showSwitch: {
    valueType: 'group',
    columns: [
      {
        dataIndex: 'taskSwitch',
        title: '是否展示',
        valueType: 'switch',
      },
    ],
  },
};
// 新人任务专用
export const FORM_ITEMS_CONFIG_NEW_COMER = {
  title: {
    valueType: 'group',
    columns: [
      {
        title: '标题',
        dataIndex: 'guideTaskTitle',
        valueType: 'input',
        width: 'md',
        formItemProps: {
          extra: '最多输入4个字',
          rules: [
            {
              required: true,
              message: '不能为空',
            },
            { validator: validateAlphanumeric(4) },
          ],
        },
      },
      {
        title: '气泡',
        dataIndex: 'bubbleText',
        valueType: 'input',
        width: 'md',
        formItemProps: {
          help: '最多输入4个字',
          rules: [
            {
              required: true,
              message: '请填写气泡文案',
            },

            { validator: validateAlphanumeric(4) },
          ],
        },
      },
      {
        title: '任务时长',
        dataIndex: 'guideTaskCountDownHour',
        valueType: 'input',
        width: 'md',
        fieldProps: {
          suffix: '小时',
        },
        formItemProps: {
          rules: [
            {
              required: true,
              message: '请填写任务时长',
            },
            { validator: validateTrialDuration },
          ],
        },
      },
    ],
  },
  // 教学任务配置
  teachingReward: {
    valueType: 'group',
    columns: [
      {
        title: '发放钱豆数（教学任务）',
        dataIndex: 'guideAppQdAmount',
        valueType: 'input',
        width: 'sm',
        fieldProps: {
          suffix: '个',
        },
        formItemProps: {
          extra:
            '为保证用户体验，发放额当日调整后，不会立刻生效，会从次日0点起生效',
          rules: [
            {
              required: true,
              message: '请填写教学任务钱豆数',
            },
            { validator: validateMoneyNotNegativeInteger },
          ],
        },
      },
      {
        title: '发放提现卡数（教学任务）',
        dataIndex: 'guideAppCashCardAmount',
        valueType: 'money',
        width: 'sm',
        fieldProps: {
          suffix: '元',
        },
        formItemProps: {
          extra:
            '为保证用户体验，发放额当日调整后，不会立刻生效，会从次日0点起生效',
          rules: [
            {
              required: true,
              message: '请填写教学任务提现卡数',
            },
            { validator: validateMoneyNotNegativeInteger },
          ],
        },
      },
    ],
  },
  moneyBeanAmountNewcomer: {
    valueType: 'group',
    columns: [
      {
        title: '发放钱豆数（下载任务）',
        dataIndex: 'eachAppReward',
        valueType: 'input',
        width: 'sm',
        fieldProps: {
          suffix: '个',
        },
        formItemProps: {
          extra:
            '为保证用户体验，发放当日调整后，不会立刻生效，会从次日0点起生效',
          rules: [
            {
              required: true,
              message: '请填写下载任务钱豆数',
            },
            { validator: validatePositiveInteger },
          ],
        },
      },
      {
        title: '发放提现卡数（下载任务）',
        dataIndex: 'eachAppCashCardReward',
        valueType: 'money',
        width: 'sm',
        fieldProps: {
          suffix: '元',
        },
        formItemProps: {
          extra:
            '为保证用户体验，发放额当日调整后，不会立刻生效，会从次日0点起生效',
          rules: [
            {
              required: true,
              message: '请填写下载任务提现卡数',
            },
            { validator: validateMoneyNotNegativeInteger },
          ],
        },
      },
    ],
  },
};
export const FORM_ITEMS_CONFIG_continuous = {
  // rewardListGroup: {
  //   valueType: 'group',
  //   tooltip: '如果是一项，则c端页面上展示总的；如果是两项，则C端分2阶段依次展示',
  rewardListGroup: {
    title: '奖励配置',
    valueType: 'formList',
    width: 1000,
    fieldProps: {
      max: 2,
      min: 2,
      initialValue: [
        { qdRewardAmount: '', cashCardRewardAmount: '', stage: 1 },
        { qdRewardAmount: '', cashCardRewardAmount: '', stage: 2 },
      ],
      copyIconProps: false,
      alwaysShowItemLabel: true,
      tooltip: '如果要只展示第二阶段，那就第一阶段都配成0即可',
    },
    dataIndex: 'cpdRewardConfigDTOList',
    columns: [
      {
        valueType: 'group',
        columns: [
          {
            title: '阶段序号',
            dataIndex: 'stage',
            valueType: 'input',
            width: 'sm',
            colProps: {
              xs: 24,
              sm: 12,
            },
            fieldProps: {
              disabled: true,
            },
            formItemProps: {
              rules: [
                {
                  required: true,
                },
              ],
            },
          },
          {
            title: '发放钱豆数',
            dataIndex: 'qdRewardAmount',
            valueType: 'digit',
            width: 'sm',
            colProps: {
              xs: 24,
              sm: 12,
            },
            fieldProps: {
              suffix: '个',
            },
            formItemProps: {
              extra:
                '为保证用户体验，发放当日调整后，不会立刻生效，会从次日0点起生效',
              rules: [
                {
                  required: true,
                  message: '请填写本阶段连续任务钱豆发放奖励数量',
                },
                { validator: validateNotNegativeInteger },
              ],
            },
          },
          {
            title: '发放提现卡数',
            dataIndex: 'cashCardRewardAmount',
            valueType: 'input',
            width: 'sm',
            colProps: {
              xs: 24,
              sm: 12,
            },
            fieldProps: {
              suffix: '元',
            },
            formItemProps: {
              // extra: '为保证用户体验，发放额当日调整后，不会立刻生效，会从次日0点起生效',
              rules: [
                {
                  required: true,
                  message: '请填写本阶段连续任务提现卡发放奖励数量',
                },
                { validator: validateMoneyNotNegativeInteger },
              ],
            },
          },
        ],
      },
    ],
  },
  rewardListGroupV2: {
    title: '奖励配置',
    valueType: 'formList',
    width: 1000,
    fieldProps: {
      max: 2,
      min: 2,
      initialValue: [
        { cashRedRewardAmount: '', stage: 1 },
        { cashRedRewardAmount: '', stage: 2 },
      ],
      copyIconProps: false,
      alwaysShowItemLabel: true,
      tooltip: '如果要只展示第二阶段，那就第一阶段都配成0即可',
    },
    dataIndex: 'cpdRewardConfigDTOList',
    columns: [
      {
        valueType: 'group',
        columns: [
          {
            title: '阶段序号',
            dataIndex: 'stage',
            valueType: 'input',
            width: 'sm',
            colProps: {
              xs: 24,
              sm: 12,
            },
            fieldProps: {
              disabled: true,
            },
            formItemProps: {
              rules: [
                {
                  required: true,
                },
              ],
            },
          },
          {
            title: '发放红包数',
            dataIndex: 'cashRedRewardAmount',
            valueType: 'money',
            width: 'sm',
            fieldProps: {
              suffix: '元',
            },
            formItemProps: {
              extra:
                '为保证用户体验，发放额当日调整后，不会立刻生效，会从次日0点起生效',
              rules: [
                {
                  required: true,
                  message: '请填写任务红包发放奖励数量',
                },
                { validator: validatePositiveMoney },
              ],
            },
          },
        ],
      },
    ],
  },
  showPriority: {
    valueType: 'group',
    columns: [
      {
        dataIndex: 'priority',
        title: '排序',
        valueType: 'input',
        formItemProps: {
          rules: [
            {
              required: true,
              message: '请填写排序',
            },
          ],
        },
      },
    ],
  },
  moneyBeanAmountV2: {
    // title: '奖励配置', 
    valueType: 'group',
    columns: [
      {
        title: '发放红包数',
        dataIndex: 'eachAppCashRedReward',
        valueType: 'money',
        width: 'sm',
        fieldProps: {
          suffix: '元',
        },
        formItemProps: {
          extra:
            '为保证用户体验，发放额当日调整后，不会立刻生效，会从次日0点起生效',
          rules: [
            {
              required: true,
              message: '请填写任务红包发放奖励数量',
            },
            { validator: validatePositiveMoney },
          ],
        },
      },
      {
        title: '单日任务领奖个数限制（必须大于0）',
        dataIndex: 'dailyAwardsLimit',
        valueType: 'input',
        width: 'md',
        fieldProps: {
          suffix: '个',
        },
        formItemProps: {
          extra:
            '为保证用户体验，发放当日调整后，不会立刻生效，会从次日0点起生效',
          rules: [
            {
              required: true,
              message: '请填写展示个数',
            },
            { validator: validatePositiveInteger },
          ],
        },
      }
    ],
  },
  
  // },
};
export const FORM_ITEMS_CONFIG = {
  ...FORM_ITEMS_CONFIG_COMMON,
  ...FORM_ITEMS_CONFIG_continuous,
  ...FORM_ITEMS_CONFIG_NEW_COMER,
};
export const TASK_CONFIG = {
  activityRuleUrl: {
    text: '活动规则详情',
    formItems: [],
  },
  downloadAppConfig: {
    text: '下载任务',
    // formItems: ['copyWriting', 'duration', 'moneyBeanAmount', 'showAmount', 'showDailyAwardsLimit'],
    formItems: ['copyWriting', 'showPriority', 'showSwitch', 'duration', 'showAmount', 'moneyBeanAmountV2'],
  },
  continuousAppConfig: {
    text: '连续任务',
    formItems: ['copyWriting', 'showPriority', 'showSwitch', 'durations',  'showAmount', 'rewardListGroupV2', 'showDailyAwardsLimit'],
  },
  promotionalAppConfig: {
    text: '体验任务',
    formItems: ['copyWriting', 'duration', 'moneyBeanAmount', 'showAmount'],
  },
  adVideoConfig: {
    text: '视频任务',
    formItems: ['copyWriting', 'count', 'moneyBeanAmount'],
  },
  registerAppConfig: {
    text: '注册任务',
    formItems: [
      'copyWriting',
      'duration',
      'moneyBeanAmounts',
      'creditAmounts',
      'showAmount',
      // 'showSwitch',
    ],
  },
  newcomerAppConfig: {
    text: '新人任务',
    formItems: [
      'showSwitch',
      'title',
      'moneyBeanAmountNewcomer',
      'teachingReward'
    ],
  },
};

export const getFormItemsByTaskType = (type) => {
  const task = TASK_CONFIG[type];
  const formItemNames = task.formItems;
  return formItemNames.map((name) => {
    return FORM_ITEMS_CONFIG[name];
  });
};
export const getFormItemsByShowList = (showItem) => {
  const res = TASK_CONFIG[showItem].formItems.map((name) => {
    return FORM_ITEMS_CONFIG[name];
  });
  return res;
};

export const getTitle = (item) => {
  return TASK_CONFIG[item].text;
};
