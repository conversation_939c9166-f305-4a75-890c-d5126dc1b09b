/* eslint-disable */
import { createRef, Fragment, useEffect, useRef, useState } from 'react';
import AuditCard from '@/components/AuditCard';
import TaskCard from './components/TaskCard';
import { message, Space, Tabs, Spin } from 'antd';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { cloneDeep } from 'lodash';
import {
  BetaSchemaForm,
  ProCard,
  ProForm,
  ProFormGroup,
  ProFormList,
  ProFormTextArea,
} from '@ant-design/pro-components';
import TaskCardAbtest from './components/TaskCardAbtest';
import {
  getRedEnvelopTaskConfigV2,
  getRedEnvelopTaskDetailV2,
  submitRedEnvelopTaskConfigV2,
} from '@/services/redEnvelopTask';

const taskArr = [
  {
    text: '下载任务',
    cpdTaskType: 'app',
    configType: 'downloadAppConfig',
  },
  {
    text: '连续任务',
    cpdTaskType: 'continuous_app',
    configType: 'continuousAppConfig',
  },
  {
    text: '体验任务',
    cpdTaskType: 'promotional_app',
    configType: 'promotionalAppConfig',
  },
  {
    text: '视频任务',
    cpdTaskType: 'adVideo',
    configType: 'adVideoConfig',
  },
  {
    text: '注册任务',
    cpdTaskType: 'register_app',
    configType: 'registerAppConfig',
  },
  {
    text: '新人任务(不参与排序，无论如何移动，在C端都是固定位置)',
    cpdTaskType: 'newcomer_app',
    configType: 'newcomerAppConfig',
  },
];

const cpdTaskType2ConfigType = (cpdTaskType) => {
  const configType = taskArr.find(
    (task) => task.cpdTaskType === cpdTaskType,
  ).configType;
  return configType;
};

const configType2CpdTaskType = (configType) => {
  const cpdTaskType = taskArr.find(
    (task) => task.configType === configType,
  ).cpdTaskType;
  return cpdTaskType;
};

const TaskConfig = ({ isAudit }) => {
  const baseFormRef = useRef();
  const defaultFormRef = useRef();
  const [loading, setLoading] = useState(true);
  const [checkedCase, setCheckedCase] = useState([
    {
      key: 'downloadAppConfig',
      value: false,
    },
    {
      key: 'continuousAppConfig',
      value: false,
    },
    {
      key: 'promotionalAppConfig',
      value: false,
    },
    {
      key: 'adVideoConfig',
      value: false,
    },
    {
      key: 'registerAppConfig',
      value: false,
    },
    {
      key: 'newcomerAppConfig',
      value: false,
    },
  ]);
  const [tabList, setTabList] = useState([]);
  const [showTab, setShowTab] = useState(false);
  const [auditData, setAuditData] = useState();

  const initData = () => {
    const func = isAudit ? getRedEnvelopTaskDetailV2 : getRedEnvelopTaskConfigV2;
    func().then((res) => {
      if (!res) {
        return;
      }
      setAuditData(res.data);

      const { experimentConfigDTO, activityRuleUrl, cpdCommonTaskDTOList } =
        res.data;
      const {
        enable,
        experimentContent,
        experimentContentDTOS = [],
      } = experimentConfigDTO;

      const experimentConfig = {
        ...experimentConfigDTO,
        enable: enable.toString(),
      };
      if (experimentContent) {
        const checkedCaseArr = experimentContent.split(',');
        experimentConfig.experimentContent = checkedCaseArr;

        const temp = checkedCase.map((obj) => {
          return {
            key: obj.key,
            value: checkedCaseArr.includes(obj.key),
          };
        });
        setCheckedCase(temp);
      }
      baseFormRef.current?.setFieldsValue(experimentConfig);

      defaultFormRef.current?.setFieldsValue({
        activityRuleUrl,
        cpdCommonTaskDTOList: cpdCommonTaskDTOList.map((item) => {
          let data = { ...item };
          if (data.cpdTaskType !== 'continuous_app') {
            data = {
              ...data,
              eachAppCashRedReward: (
                +item.eachAppCashRedReward / 100
              ).toFixed(2),
            };
          }
          if (data.cpdTaskType === 'newcomer_app') {
            data = {
              ...data,
              guideAppCashCardAmount: (
                +item.guideAppCashCardAmount / 100
              ).toFixed(2),
            };
          }
          if (data.cpdTaskType === 'continuous_app') {
            data['1'] = item.trialDurationConfigDTOList[0].trialDuration;
            data['2'] = item.trialDurationConfigDTOList[1].trialDuration;
            // 处理钱豆、提现卡金额
            data.cpdRewardConfigDTOList = data.cpdRewardConfigDTOList?.map(
              (item) => {
                return {
                  ...item,
                  cashRedRewardAmount: (
                    +item.cashRedRewardAmount / 100
                  ).toFixed(2),
                };
              },
            );
          }
          if (data.cpdTaskType === 'register_app') {
            data.registeredAppCashCardAmount = (
              +item.registeredAppCashCardAmount / 100
            ).toFixed(2);
          }
          return data;
        }),
      });

      setShowTab(experimentConfig.enable === '1');

      const experimentTabList = experimentContentDTOS.map((exp, index) => {
        const temp = [];
        exp.downloadAppConfig &&
          temp.push({
            ...exp.downloadAppConfig,
            eachAppCashRedReward: (
              +exp.downloadAppConfig.eachAppCashRedReward / 100
            ).toFixed(2),
            cpdTaskType: 'app',
          });
        if (exp.continuousAppConfig) {
          let data = {
            ...exp.continuousAppConfig,
            cpdTaskType: 'continuous_app',
            1: exp.continuousAppConfig.trialDurationConfigDTOList[0]
              .trialDuration,
            2: exp.continuousAppConfig.trialDurationConfigDTOList[1]
              .trialDuration,
          };
          // 处理提现卡金额
          data.cpdRewardConfigDTOList = data.cpdRewardConfigDTOList?.map(
            (item) => {
              return {
                ...item,
                cashRedRewardAmount: (
                  +item.cashRedRewardAmount / 100
                ).toFixed(2),
              };
            },
          );
          temp.push(data);
        }
        exp.promotionalAppConfig &&
          temp.push({
            ...exp.promotionalAppConfig,
            cpdTaskType: 'promotional_app',
            eachAppCashRedReward: (
              +exp.promotionalAppConfig.eachAppCashRedReward / 100
            ).toFixed(2),
          });
        exp.adVideoConfig &&
          temp.push({
            ...exp.adVideoConfig,
            cpdTaskType: 'adVideo',
            eachAppCashRedReward: (
              +exp.adVideoConfig.eachAppCashRedReward / 100
            ).toFixed(2),
          });
        exp.registerAppConfig &&
          temp.push({
            ...exp.registerAppConfig,
            cpdTaskType: 'register_app',
            eachAppCashRedReward: (
              +exp.registerAppConfig.eachAppCashRedReward / 100
            ).toFixed(2),
            registeredAppCashCardAmount: (
              +exp.registerAppConfig.registeredAppCashCardAmount / 100
            ).toFixed(2),
          });
        exp.newcomerAppConfig &&
          temp.push({
            ...exp.newcomerAppConfig,
            guideAppCashCardAmount: (
              +exp.newcomerAppConfig.guideAppCashCardAmount / 100
            ).toFixed(2),
            eachAppCashRedReward: (
              +exp.newcomerAppConfig.eachAppCashRedReward / 100
            ).toFixed(2),
          });
        return {
          key: index + 1,
          formRef: createRef(),
          actionRef: createRef(),
          initialValues: {
            activityRuleUrl: exp.activityRuleUrl,
            expCardList: temp,
          },
        };
      });

      setTabList(experimentTabList);
      setLoading(false);
    });
  };

  useEffect(() => {
    initData();
  }, []);

  const columns = [
    {
      title: '是否abtest实验',
      dataIndex: 'enable',
      valueType: 'radio',
      valueEnum: {
        0: '否（全量默认以下配置）',
        1: '是（仅对照组为以下实验）',
      },
      formItemProps: {
        onChange: (value) => {
          setShowTab(value.target.defaultValue === '1');
        },
      },
    },
    {
      valueType: 'dependency',
      name: ['enable'],
      columns: ({ enable }) => {
        return enable === '1'
          ? [
              {
                valueType: 'group',
                columns: [
                  {
                    title: '实验code',
                    dataIndex: 'experimentCode',
                    valueType: 'input',
                    width: 'md',
                    formItemProps: {
                      rules: [
                        {
                          required: true,
                          message: '请填写实验code',
                        },
                      ],
                    },
                  },
                  {
                    title: '实验场景选择',
                    dataIndex: 'experimentContent',
                    valueType: 'checkbox',
                    fieldProps: {
                      labelInValue: true,
                      style: {
                        minWidth: 140,
                      },
                    },
                    formItemProps: {
                      onChange: (e) => {
                        const target = e.target.defaultValue;
                        const temp = checkedCase.map((obj, index) => {
                          let newVal = obj.value;
                          if (obj.key === target) {
                            newVal = !newVal;
                            if (newVal === true) {
                              // add todo
                              tabList.map((exp) => {
                                exp.actionRef.current.add(
                                  {
                                    cpdTaskType: taskArr.find(
                                      (task) => task.configType === target,
                                    ).cpdTaskType,
                                  },
                                  index,
                                );
                              });
                            } else {
                              // remove
                              tabList.map((exp) => {
                                const res =
                                  exp.formRef.current.getFieldsValue();
                                const i = res.expCardList.findIndex((a1) => {
                                  return (
                                    cpdTaskType2ConfigType(a1.cpdTaskType) ===
                                    target
                                  );
                                });
                                exp.actionRef.current.remove(i);
                              });
                            }
                          }

                          return {
                            key: obj.key,
                            value: newVal,
                          };
                        });
                        console.log(8989, temp);
                        setCheckedCase(temp);
                      },
                      rules: [
                        {
                          required: true,
                          message: '请选择实验场景',
                        },
                      ],
                    },
                    valueEnum: {
                      downloadAppConfig: {
                        text: '下载任务',
                      },
                      continuousAppConfig: {
                        text: '连续任务',
                      },
                    },
                  },
                ],
              },
            ]
          : [];
      },
    },
  ];

  const changePosition = (dragIndex, hoverIndex) => {
    // 交换位置
    const res = defaultFormRef.current.getFieldValue('cpdCommonTaskDTOList');
    const a = res[dragIndex],
      b = res[hoverIndex];

    res.splice(dragIndex, 1, b);
    res.splice(hoverIndex, 1, a);
    defaultFormRef.current.setFieldValue('cpdCommonTaskDTOList', res);
  };

  const onSubmit = async () => {
    const res = await Promise.all([
      baseFormRef.current.validateFields(),
      defaultFormRef.current.validateFields(),
    ]).catch((e) => {
      message.error('请完善必填配置');
      throw e;
    });
    const experimentRes = await Promise.all(
      tabList.map((tab) => {
        return tab.formRef.current.validateFields();
      }),
    ).catch((e) => {
      message.error('请完善必填配置');
      throw e;
    });

    let experimentConfigDTO = {},
      activityRuleUrl,
      cpdCommonTaskDTOList = [];

    experimentConfigDTO.enable = +res[0].enable;
    if (experimentConfigDTO.enable) {
      experimentConfigDTO = {
        ...experimentConfigDTO,
        experimentCode: res[0].experimentCode,
        experimentContent: res[0].experimentContent.join(','),
        experimentContentDTOS: experimentRes.map((exp, index) => {
          const obj = {
            group: index + 1,
            activityRuleUrl: exp.activityRuleUrl,
          };
          exp.expCardList.map((a) => {
            let temp = { ...a };
            if (temp.cpdTaskType !== 'continuous_app') {
              temp = {
                ...temp,
                eachAppCashRedReward: +a.eachAppCashRedReward * 100,
              };
            }
            if (temp.cpdTaskType === 'newcomer_app') {
              temp = {
                ...temp,
                guideAppCashCardAmount: +a.guideAppCashCardAmount * 100,
              };
            }
            if (temp.cpdTaskType === 'register_app') {
              temp.registeredAppCashCardAmount =
                +a.registeredAppCashCardAmount * 100;
            } else if (temp.cpdTaskType === 'continuous_app') {
              temp.trialDurationConfigDTOList = [
                {
                  day: 1,
                  trialDuration: temp['1'],
                },
                {
                  day: 2,
                  trialDuration: temp['2'],
                },
              ];
              // 处理钱豆、提现卡金额
              temp.cpdRewardConfigDTOList = a.cpdRewardConfigDTOList?.map(
                (item) => {
                  return {
                    ...item,
                    cashRedRewardAmount: item.cashRedRewardAmount * 100,
                  };
                },
              );
            }
            obj[
              taskArr.find(
                (task) => task.cpdTaskType === a.cpdTaskType,
              ).configType
            ] = temp;
          });
          return obj;
        }),
      };
    }
    activityRuleUrl = res[1].activityRuleUrl;
    cpdCommonTaskDTOList = res[1].cpdCommonTaskDTOList.map((item, index) => {
      let data = { ...item };
      if (data.cpdTaskType !== 'continuous_app') {
        data = {
          ...data,
          eachAppCashRedReward: +item.eachAppCashRedReward * 100,
        };
      }
      if (data.cpdTaskType === 'newcomer_app') {
        data = {
          ...data,
          guideAppCashCardAmount: +item.guideAppCashCardAmount * 100,
        };
      }
      if (item.cpdTaskType === 'continuous_app') {
        data.cpdRewardConfigDTOList = data.cpdRewardConfigDTOList?.map(
          (item) => {
            return {
              ...item,
              cashRedRewardAmount: item.cashRedRewardAmount * 100,
            };
          },
        );
      }
      if (item.cpdTaskType === 'register_app') {
        data.registeredAppCashCardAmount =
          +item.registeredAppCashCardAmount * 100;
      }
      if (item.cpdTaskType === 'continuous_app') {
        data.trialDurationConfigDTOList = [
          {
            day: 1,
            trialDuration: item['1'],
          },
          {
            day: 2,
            trialDuration: item['2'],
          },
        ];
      }
      return data;
    });

    const submitRes = await submitRedEnvelopTaskConfigV2({
      id: auditData.id,
      experimentConfigDTO,
      activityRuleUrl,
      cpdCommonTaskDTOList,
    });
    if (submitRes && submitRes.code === '200') {
      initData();
      message.success('提交成功');
    }
  };

  const onTabEditHandle = (targetKey, type) => {
    const list = cloneDeep(tabList);
    if (type === 'add') {
      baseFormRef.current.validateFields().then((res) => {
        list.push({
          key: tabList.length === 0 ? 1 : tabList[tabList.length - 1].key + 1,
          formRef: createRef(),
          actionRef: createRef(),
          initialValues: {
            expCardList: checkedCase
              .filter((a) => a.value)
              .map((b) => {
                return {
                  cpdTaskType: taskArr.find((task) => task.configType === b.key)
                    .cpdTaskType,
                };
              }),
          },
        });
        setTabList(list);
      });
    } else {
      const newPanes = list.filter((item, index) => item.key !== +targetKey);
      setTabList(newPanes);
    }
  };

  return (
    <Fragment>
      <Spin spinning={loading}>
        <Space direction="vertical">
          {!isAudit && (
            <AuditCard title="下载应用赚红包审核" onSubmit={onSubmit} type="ActivityManagement" scene="redEnvelopTaskNew" auditData={auditData} />
          )}
          <ProCard
            title="下载应用赚红包"
            direction="column"
            style={{ width: '1500px' }}
          >
            <ProCard>
              <BetaSchemaForm
                columns={columns}
                formRef={baseFormRef}
                submitter={false}
                readonly={isAudit}
              />
            </ProCard>
            <Tabs
              defaultActiveKey={0}
              type="editable-card"
              hideAdd={!showTab || isAudit}
              addIcon={<>添加实验组➕</>}
              onEdit={onTabEditHandle}
            >
              <Tabs.TabPane
                tab="对照组-线上"
                key={0}
                forceRender={true}
                closable={false}
              >
                <div style={{ marginBottom: '10px' }}>
                  对照组：默认为线上兜底配置
                </div>
                <ProForm
                  formRef={defaultFormRef}
                  submitter={false}
                  readonly={isAudit}
                >
                  <ProFormTextArea
                    colProps={{ span: 24 }}
                    name="activityRuleUrl"
                    label="活动规则详情"
                    placeholder="请输入悟空页面dp链接"
                    rules={[
                      {
                        required: true,
                        message: '请填写活动规则详情',
                      },
                    ]}
                  />
                  <ProCard
                    title="优先级排序"
                    // subTitle="优先级自上而下降低，点击拖拽模块挪动位置即可"
                  >
                    <ProFormList
                      alwaysShowItemLabel
                      min={1}
                      name="cpdCommonTaskDTOList"
                      deleteIconProps={false}
                      copyIconProps={false}
                      creatorButtonProps={false}
                      itemRender={(doms, listMeta) => {
                        const { index, record: item } = listMeta;
                        return (
                          <ProFormGroup key="group">
                            <DndProvider backend={HTML5Backend}>
                              <Space direction="vertical">
                                <ProCard colSpan={24} layout="left" key={index}>
                                  <TaskCard
                                    text={
                                      taskArr.find(
                                        (task) =>
                                          task.cpdTaskType === item.cpdTaskType,
                                      ).text
                                    }
                                    index={index}
                                    changePosition={changePosition}
                                    key={item.cpdTaskType}
                                    type={
                                      taskArr.find(
                                        (task) =>
                                          task.cpdTaskType === item.cpdTaskType,
                                      ).configType
                                    }
                                  />
                                </ProCard>
                              </Space>
                            </DndProvider>
                          </ProFormGroup>
                        );
                      }}
                    />
                  </ProCard>
                </ProForm>
              </Tabs.TabPane>

              {tabList.map((item, index) => {
                return (
                  <Tabs.TabPane
                    tab={'实验' + item.key}
                    key={item.key}
                    forceRender={true}
                    closable={!isAudit}
                  >
                    <ProForm
                      formRef={item.formRef}
                      submitter={false}
                      initialValues={item.initialValues}
                      readonly={isAudit}
                    >
                      <ProFormTextArea
                        colProps={{ span: 24 }}
                        name="activityRuleUrl"
                        label="活动规则详情"
                        placeholder="请输入悟空页面dp链接"
                        rules={[
                          {
                            required: true,
                            message: '请填写活动规则详情',
                          },
                        ]}
                      />
                      <Space direction="vertical">
                        <ProFormList
                          name="expCardList"
                          actionRef={item.actionRef}
                          deleteIconProps={false}
                          copyIconProps={false}
                          creatorButtonProps={false}
                          itemRender={(doms, listMeta) => {
                            console.log(1212, listMeta);
                            const { index: jndex } = listMeta;
                            return (
                              <ProFormGroup key="group">
                                <ProCard colSpan={24} layout="left" key={jndex}>
                                  <TaskCardAbtest
                                    key={jndex}
                                    showItem={
                                      taskArr.find(
                                        (task) =>
                                          task.cpdTaskType ===
                                          listMeta.record.cpdTaskType,
                                      ).configType
                                    }
                                  />
                                </ProCard>
                              </ProFormGroup>
                            );
                          }}
                        />
                      </Space>
                    </ProForm>
                  </Tabs.TabPane>
                );
              })}
            </Tabs>
          </ProCard>
        </Space>
      </Spin>
    </Fragment>
  );
};

export default TaskConfig;
