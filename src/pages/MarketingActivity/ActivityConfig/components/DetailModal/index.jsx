/* eslint-disable */
import React, { useState, useRef, useEffect } from 'react';
import {
  BetaSchemaForm,
  ProForm,
  ModalForm
} from '@ant-design/pro-components';
import { UploadOutlined } from '@ant-design/icons';
import { auditColumns } from '@/constant/config.js';
import { debounce, downloadFile } from '@/utils/util.js';
import { getDmpTag } from '@/services/activityCommon.js';
import {
  auditActivity,
  saveActivityDetails,
  getActivityPlayInfo
} from '@/services/marketingActivity/activityConfig.js';
import OperateHistory from '../OperateHistory';

import dayjs from 'dayjs';
import {
  Upload,
  message,
  notification,
  Button,
  Row,
  Drawer,
  Form,
  Space,
} from 'antd';

const DetailModal = ({
  modalVisible,
  setModalVisible,
  actionType,
  setActionType,
  width = '60%',
  handleReload,
  setInitialConfig,
  initialConfig,
  noHistoryTable = false,
}) => {
  const formRef = useRef();
  const formDisabled = actionType === 'view' || actionType === 'audit';
  const [tagOptions, setTagOptions] = useState([]);
  const [fileList, setFileList] = useState([]); // 人群包文件
  const [params, setParams] = useState(initialConfig || {});
  const [playOptions, setPlayOptions] = useState([]);
  const [subscriptionOptions, setSubscriptionOptions] = useState([]);


  useEffect(() => {
    initialConfig.userTag &&
      getDmpTagData({ tagId: Number(initialConfig.userTag) });
      initialConfig.subscriptionCode &&
      getSubscriptionInfo({ type: 2, code: initialConfig.subscriptionCode });
      if (initialConfig.playList) {
      initialConfig.playList.map( item => {
        getPlayInfo({ type: 1, playType: item.playType, code: item.code });
      })
    }
    setParams(initialConfig);
  }, [initialConfig]);

  /**
   * 重置数据 关闭弹窗
   */
  const resetDialog = () => {
    setModalVisible(false);
    setActionType('');
    setInitialConfig({});
    setPlayOptions([])
    // 刷新列表
    handleReload && handleReload();
  };

  /**
   * 获取dmp详情
   */
  const getDmpTagData = async (params) => {
    if (!params.tagName && !params.tagId) return;
    const res = await getDmpTag(params);
    if (res?.code === '200') {
      const options = res.data.map((item) => ({
        value: item.id + '',
        label: item.tagIdWithName,
      }));
      setTagOptions(options);
    }
  };

      /**
   * 获取关联订阅详情
   */
  const getSubscriptionInfo = async (params) => {
      if (!params.type && !params.value) return;
      const res = await getActivityPlayInfo(params);
      if (res?.code === '200' && res.data) {
        const options = res.data.map((item) => ({
          value: item.code,
          label: item.code + '-' + item.name,
        }));
        setSubscriptionOptions(options);
      }
    };
    /**
   * 获取玩法详情
   */
    const getPlayInfo = async (params) => {
      if (!params.type && !params.playType && !params.value) return;
      const res = await getActivityPlayInfo(params);
      if (res?.code === '200' && res.data) {
        const options = res.data.map((item) => ({
          value: item.code,
          label: item.code + '-' + item.name + '-【' + item.startTime + '至' + item.endTime + '】',
        }));
        setPlayOptions((prevOptions) => {
          const allOptions = [...prevOptions, ...options];
          const deduplicatedOptions = Array.from(
            new Map(allOptions.map((option) => [option.value, option])).values()
          );
          return deduplicatedOptions;
        });
      }
    };

  // 表单提交
  const handleSubmit = async () => {
    const values = await formRef.current?.validateFields();
    const { activityTime = [] } = values;
    const { id, version } = initialConfig
    let params = {
      id,
      version,
      ...values,
    };
    // 参数处理
    if (activityTime) {
      params.startTime = dayjs(activityTime[0]).format('YYYY-MM-DD HH:mm:ss');
      params.endTime = dayjs(activityTime[1]).format('YYYY-MM-DD HH:mm:ss');
      delete params.activityTime;
    }
    if (['add', 'copy'].includes(actionType)) {
      delete params.id;
    }
    // 提交
    const res = await saveActivityDetails(params);
    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
      });
      resetDialog();
    }
  };

  // 修改审核状态
  const handleAudit = async (params) => {
    const res = await auditActivity(params);

    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
      });
      resetDialog();
    }
  };

  const basicColumns = [
    {
      title: '活动名称',
      dataIndex: 'name',
      key: 'name',
      width: 'lg',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          { max: 20, message: '限制20个字符' },
        ],
      },
      fieldProps: (form, config) => {
        return {
          maxLength: 20,
          showCount: true,
        };
      },
    },
    {
      title: '活动时间',
      valueType: 'dateTimeRange',
      dataIndex: 'activityTime',
      key: 'activityTime',
      width: 'lg',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (
                !value ||
                (value &&
                  dayjs(value[1]) > dayjs())
              ) {
                return Promise.resolve();
              }
              return Promise.reject(
                new Error('结束时间必须晚于当前时间'),
              );
            },
          }),
        ],
      },
    },
    {
      title: '活动规则',
      dataIndex: 'activityRuleUrl',
      key: 'activityRuleUrl',
      width: 'lg',
      fieldProps: {
        placeholder: '请输入悟空链接',
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          () => ({
            validator(_, value) {
              if (!value || value.includes('://')) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('链接格式错误'));
            },
          }),
        ],
      },
    },
    {
      title: '活动用户',
      dataIndex: 'userType',
      key: 'userType',
      width: 'lg',
      valueType: 'radio',
      fieldProps: (form) => {
        return {
          options: [
            { label: '按用户标签', value: 'dmpTag' },
            { label: '所有用户', value: 'allUsers' },
          ],
          onChange: (e) => {
            form.setFieldValue('userTag', '');
            setFileList([]);
          },
        };
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      valueType: 'dependency',
      name: ['userType'],
      columns: ({ userType }) => {
        return userType === 'dmpTag'
            ? [
              {
                title: '用户标签',
                dataIndex: 'userTag',
                key: 'userTag',
                width: 'lg',
                valueType: 'select',
                fieldProps: (form) => {
                  return {
                    onSearch: debounce((value) => {
                      getDmpTagData({ tagName: value });
                    }, 500),
                    showSearch: true,
                    options: tagOptions,
                  };
                },
                formItemProps: {
                  rules: [
                    {
                      required: true,
                      message: '此项为必填项',
                    },
                  ],
                },
              },
            ]
            : [];
      },
    },
    {
      title: '关联订阅',
      dataIndex: 'subscriptionCode',
      key: 'subscriptionCode',
      valueType: 'select',
      width: 'lg',
      fieldProps: (form) => {
        return {
          onSearch: debounce((value) => {
            value && getSubscriptionInfo({ type: 2 , code: value });
          }, 500),
          showSearch: true,
          options: subscriptionOptions,
        };
      },
    },
    {
      title: '活动链接',
      dataIndex: 'activityUrl',
      key: 'activityUrl',
      width: 'lg',
      fieldProps: {
        placeholder: '请输入链接',
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          () => ({
            validator(_, value) {
              if (!value || value.includes('://')) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('链接格式错误'));
            },
          }),
        ],
      },
    },
  ];
  const playColumns = [
    {
      valueType: 'formList',
      dataIndex: ['playList'],
      initialValue: [],
      columns: [
        {
          valueType: 'group',
          columns: [
            {
              title: '玩法类型',
              key: 'playType',
              dataIndex: 'playType',
              valueType: 'select',
              width: 'md',
              fieldProps: {
                placeholder: '请选择玩法类型',
              },
              fieldProps: (form) => {
                return {
                  options: [
                    { label: '常规玩法', value: 1 },
                    { label: '任务玩法', value: 2 },
                    { label: '签到玩法', value: 3 },
                  ],
                };
              },
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: '此项为必填项',
                  },
                ],
              },
            },
            {
              valueType: 'dependency',
              name: ['playType'],
              columns: ({ playType }) => {
                return [
                  {
                    title: '玩法code',
                    dataIndex: 'code',
                    valueType: 'select',
                    width: 'md',
                    fieldProps: (form) => {
                      return {
                        onSearch: debounce((value) => {
                          value && getPlayInfo({ type: 1, playType , code: value });
                        }, 500),
                        showSearch: true,
                        options: playOptions,
                      };
                    },
                    formItemProps: {
                      rules: [
                        {
                          required: true,
                          message: '此项为必填项',
                        },
                      ],
                    },
                  },
                ]
              }
            },
          ],
        },
      ],
    },
  ];

  return (
    <Drawer
      title={"活动配置"}
      width={'1000'}
      onClose={() => {
        resetDialog();
      }}
      destroyOnClose={true}
      open={modalVisible}
      styles={{
        body: {
          paddingBottom: 80,
        },
      }}
      footer={
        <Space>
          <Button
            onClick={() => {
              resetDialog();
            }}
          >
            {actionType === 'view' ? '关闭' : '取消'}
          </Button>
          {['edit', 'add', 'copy'].includes(actionType) && (
            <>
              <Button
                type="primary"
                onClick={() => {
                  handleSubmit();
                }}
              >
                提交
              </Button>
            </>
          )}
          {actionType === 'audit' && (
            <>
              <ModalForm
                trigger={
                  <Button type="primary" disabled={false}>
                    审核驳回
                  </Button>
                }
                title="请填写审核驳回意见"
                modalProps={{
                  destroyOnClose: true,
                  centered: true,
                }}
                onFinish={async (auditParams) => {
                  const tempAuditParams = {
                    ...auditParams,
                    id: initialConfig?.id,
                    auditOperation: 3,
                  };

                  const res = await auditActivity(tempAuditParams);

                  if (res?.code === '200') {
                    notification.success({
                      message: '操作成功',
                    });
                    resetDialog();
                    return true;
                  } else {
                    notification.error({
                      message: '操作失败',
                      description: res?.msg,
                    });
                  }
                }}
              >
                <BetaSchemaForm layoutType="Embed" columns={auditColumns} />
              </ModalForm>
              <Button
                type="primary"
                onClick={() => {
                  handleAudit({
                    id: initialConfig?.id,
                    auditOperation: 2,
                  });
                }}
              >
                审核通过
              </Button>
            </>
          )}
        </Space>
      }
    >
      <ProForm
        formRef={formRef}
        layout="vertical"
        submitter={false}
        onValuesChange={(changedValues, allValues) => {
          setParams({ ...params, ...changedValues });
        }}
        initialValues={initialConfig}
        disabled={formDisabled}
      >
        <h3>基础配置</h3>
        <BetaSchemaForm layoutType="Embed" columns={basicColumns} />
        <h3>玩法配置</h3>
        <BetaSchemaForm layoutType="Embed" columns={playColumns} />
      </ProForm>
      {/* 操作记录 */}
      {!noHistoryTable && (actionType === 'audit' || actionType === 'view') && (
        <OperateHistory
          key="operateHistory"
          id={initialConfig?.id}
          bizCode="activity"
          disabled={false}
        />
      )}
    </Drawer>
  );
};

export default DetailModal;
