import React, { useRef, useState, useEffect } from 'react';
import CommonTableList from '@/components/CommonTableList';
import {
  getActivityList,
  settingOnline,
  getActivityDetails,
  deleteActivity,
} from '@/services/marketingActivity/activityConfig.js';
import {
  Button, Space, Tag, Divider, Popconfirm, Spin, notification,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import { copyContent } from '@/utils/util.js';
import DetailModal from './components/DetailModal/index';

const ActivityConfig = () => {
  const actionRef = useRef();
  const [modalVisible, setModalVisible] = useState(false); // 弹窗可见性
  const [actionType, setActionType] = useState('');
  const [initialConfig, setInitialConfig] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
  }, []);

  /**
   * 列表数据请求
   */
  const gainActivitySettingList = async (params) => {
    const param = {
      page: params.current,
      ...params,
    };

    const res = await getActivityList(param);
    const { data } = res;
    return {
      data: data.list,
      total: data.total,
    };
  };

  /**
 * 表格刷新
 */
  const handleReload = () => {
    actionRef.current?.reload();
  };

  /**
   * 活动上下线
   * onlineOperation 2 启用 11 已下线（不可编辑和再上线）
   */
  const changeOnlineStatus = async (record) => {
    const params = {
      id: record.id,
      onlineOperation: record.onlineStatus === 1 ? 2 : 11,
    };
    const res = await settingOnline(params);
    if (res?.code === '200') {
      handleReload();
    }
  };

  /**
   * 列表操作
   * type: view edit delete audit add
   */
  const handleAction = async (record, type) => {
    // 删除
    if (type === 'delete') {
      const res = await deleteActivity({ id: record.id });
      if (res?.code === '200') {
        handleReload();
      }
      return;
    }
    // 复制链接
    if (type === 'copyUrl') {
      const { activityUrl, code } = record;
      const link = `${activityUrl}&activityCode=${code}`;
      copyContent(link);
      notification.success({ message: '已经复制了～', duration: 1 });
      return;
    }
    // 编辑详情
    if (record.id) {
      // 请求详情信息
      setLoading(true);
      const res = await getActivityDetails({ id: record.id });
      if (res?.code === '200' && res?.data) {
        const {
          startTime,
          endTime,
        } = res.data;
        const tempRecord = {
          ...res.data,
          activityTime: [startTime, endTime],
        };

        setInitialConfig(tempRecord);
        setLoading(false);
        setModalVisible(true);
      }
    } else {
      // 设置初始值
      setLoading(false);
      setModalVisible(true);
    }
    setActionType(type);
  };

  const tableColumns = [
    {
      title: '活动id',
      dataIndex: 'id',
      key: 'id',
      valueType: 'digit',
      fieldProps: {
        min: 1,
        precision: 0,
      },
      width: 120,
      fixed: 'left',
      align: 'center',
      render: (text, record) => <span>{record.id}</span>,
    },
    {
      title: '活动名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'center',
    },
    {
      title: '活动code',
      dataIndex: 'code',
      key: 'code',
      width: 140,
      align: 'center',
      copyable: true,
      ellipsis: true,
    },
    {
      title: '活动用户',
      dataIndex: 'userTypeName',
      key: 'userTypeName',
      hideInSearch: true,
      width: 120,
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      hideInSearch: true,
      valueType: 'dateTime',
      width: 140,
      align: 'center',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      valueType: 'dateTime',
      hideInSearch: true,
      width: 140,
      align: 'center',
    },
    {
      title: '操作员',
      dataIndex: 'operatorName',
      key: 'operatorName',
      width: 120,
      align: 'center',
    },
    {
      title: '审核员',
      dataIndex: 'auditorName',
      key: 'auditorName',
      hideInSearch: true,
      width: 120,
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'onlineStatus',
      key: 'onlineStatus',
      valueType: 'select',
      valueEnum: {
        1: {
          text: '禁用',
          status: 'Error',
        },
        2: {
          text: '启用',
          status: 'Success',
        },
        3: {
          text: '已失效',
          status: 'Default',
        },
        11: {
          text: '已下线',
          status: 'Default',
        },
      },
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
      width: 120,
      align: 'center',
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      key: 'auditStatus',
      valueType: 'select',
      valueEnum: {
        1: '待审核',
        2: '审核通过',
        3: '审核驳回',
      },
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
      render: (_, record) => {
        const auditStatusColorMap = {
          1: { text: '待审核', color: '#faad14' },
          2: { text: '审核通过', color: '#52c41a' },
          3: { text: '审核驳回', color: '#ff4d4f' },
        };
        return (
          <Space>
            <Tag
              color={auditStatusColorMap[record.auditStatus]?.color}
              key={_}
            >
              {auditStatusColorMap[record.auditStatus]?.text}
            </Tag>
          </Space>
        );
      },
      width: 120,
      align: 'center',
    },
    {
      title: '审核备注',
      dataIndex: 'remark',
      key: 'remark',
      hideInSearch: true,
      width: 120,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      hideInSearch: true,
      width: 400,
      align: 'center',
      fixed: 'right',
      render: (text, record) => (
        <Space split={<Divider type="vertical" />} key={record.id}>
          {record.supportEdit === 1 && (
          <Button
            key="edit"
            type="link"
            size="small"
            onClick={() => {
              handleAction(record, 'edit');
            }}
          >
            编辑
          </Button>
          )}
          {record.supportDelete === 1 && (
          <Popconfirm
            title="删除后无法恢复，确认删除吗？"
            onConfirm={() => {
              handleAction(record, 'delete');
            }}
            key="del"
            onCancel={() => {}}
            okText="确定"
            cancelText="取消"
          >
            <Button key="delete" type="link" size="small">
              删除
            </Button>
          </Popconfirm>
          )}
          {record.supportApprove === 1 && (
          <Button
            key="audit"
            type="link"
            size="small"
            onClick={() => {
              handleAction(record, 'audit');
            }}
          >
            审核
          </Button>
          )}
          {record.supportView === 1 && (
          <Button
            key="view"
            type="link"
            size="small"
            onClick={() => {
              handleAction(record, 'view');
            }}
          >
            详情
          </Button>
          )}
          {record.supportOffline === 1 && (
            <Popconfirm
              title="确认下线？"
              description="请谨慎上下线，活动上线后下线不支持再次编辑并上线，请在上线前确保配置正确？"
              onConfirm={() => changeOnlineStatus(record)}
              key="offline"
              onCancel={() => { }}
              okText="确定"
              cancelText="取消"
            >
              <Button key="offlinebtn" type="link" size="small">
                下线
              </Button>
            </Popconfirm>
          )}
          {record.supportOnline === 1 && (
            <Popconfirm
              title="确认上线？"
              description="请谨慎上下线，活动上线后下线不支持再次编辑并上线，请在上线前确保配置正确？"
              onConfirm={() => changeOnlineStatus(record)}
              key="online"
              onCancel={() => { }}
              okText="确定"
              cancelText="取消"
            >
              <Button key="onlinebtn" type="link" size="small">
                上线
              </Button>
            </Popconfirm>
          )}
          {record.productType !== 2 && record.productType !== 6 && (
          <Button
            key="copy"
            type="link"
            size="small"
            onClick={() => {
              handleAction(record, 'copy');
            }}
          >
            复制
          </Button>
          )}
          {record.onlineStatus === 2 && record.activityUrl && (
          <Button
            key="link"
            type="link"
            size="small"
            onClick={() => {
              handleAction(record, 'copyUrl');
            }}
          >
            复制链接
          </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <>
      <Spin spinning={loading}>
        <CommonTableList
          search={{ defaultCollapsed: false }}
          toolBarRender={() => [
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                handleAction({}, 'add');
              }}
            >
              新增
            </Button>,
          ]}
          columns={tableColumns}
          request={gainActivitySettingList}
          actionRef={actionRef}
        />
        {/* 详情弹窗 */}
        <DetailModal
          modalVisible={modalVisible}
          actionType={actionType}
          setActionType={setActionType}
          setModalVisible={setModalVisible}
          handleReload={handleReload}
          setInitialConfig={setInitialConfig}
          initialConfig={initialConfig}
        />
      </Spin>
    </>
  );
};

export default ActivityConfig;
