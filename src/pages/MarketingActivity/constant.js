export const playModelOptions = [
  { label: '抽奖玩法', value: 1 },
  { label: '集卡&活跃要求玩法', value: 2 },
];

/**
 * 模型类型
 */
export const strategyTypeOptions = [
  { label: '常规（同一时间，有一个或多个抽奖机会相同的抽奖策略）', value: 10 },
  {
    label: '机会不同，时间相同（同一时间，有多个抽奖机会不同的抽奖策略）',
    value: 20,
  },
  // {
  //   label: "机会相同，时间不同（不同时间，有多个抽奖机会相同的抽奖策略）",
  //   value: 30,
  // },
  // {
  //   label: "机会不同，时间不同（不同时间，有多个抽奖机会不同的抽奖策略）",
  //   value: 40,
  // },
];

export const rewardTypeOptions = [
  {
    value: 'qd',
    label: '钱豆',
  },
  {
    value: 'cashCoupon',
    label: '红包',
  },
  {
    value: 'outerRedeem',
    label: '会员兑换码',
  },
  {
    value: 'coupon',
    label: '贷款优惠券',
  },
  {
    value: 'payCoupon',
    label: '外部优惠券',
  },
  {
    value: 'redPacketCoupon',
    label: '贷款返现券',
  },
  {
    value: 'inKind',
    label: '实物礼品',
  },
  {
    value: 'virtual',
    label: '虚拟奖品',
  },
  {
    value: 'activityCoin',
    label: '活动货币',
  },
  {
    value: 'mallCoupon',
    label: '商城券',
  },
  {
    value: 'points',
    label: '积分',
  },
];
