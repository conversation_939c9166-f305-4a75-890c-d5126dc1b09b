/* eslint-disable */
import { getQuotaId, getTemplateBrief, getRewards } from "@/services/common.js";
import { rewardTypeOptions } from "../constant";
import RichTextEditor from "@/components/RichTextEditor";

// 获取库存id （多个）
export const groupQuotaIdRequest = async (values, propsValue, awardValue, awardType) => {
  console.log("==groupQuotaIdRequest==", { values, propsValue });
  try {
    let ids = [];
    if (values.keyWords) {
      ids = [Number(values.keyWords)];
    } else if (propsValue.text && Array.isArray(propsValue.text)) {
      ids = propsValue.text;
    } else if (awardValue && Array.isArray(awardValue)) {
      ids = awardValue;
    }
    if (!ids) return [];
    const { data = [] } = await getQuotaId({
      type: awardType,
      quotaIds: ids,
    });
    return data.map((item) => ({
      label: item.quotaIdWithName,
      value: item.id,
    }));
  } catch (error) {
    console.log("error=====", error);
  }
};

// 获取库存id
export const quotaIdRequest = async (param, props) => {
  const { keyWords, rewardType } = param;
  const { text } = props;

  if ((keyWords || text) && rewardType) {
    const { data = [] } = await getQuotaId({
      type: rewardType,
      quotaIdOrName: keyWords || text,
    });
    return data.map((item) => ({
      label: item.quotaIdWithName,
      value: item.id,
    }));
  }
  return [];
};

// 获取返现规则id
const getTemplateIdList = async (param, props) => {
  const { keyWords, rewardType } = param;
  const { text } = props;
  if (keyWords || text) {
    const { data } = await getTemplateBrief({
      id: keyWords || text,
      rewardType: rewardType === 'redPacketCoupon' ? 3 : 4
    });
    return [
      {
        label: `${data.id} - ${data.templateName}`,
        value: data.id,
      },
    ];
  }
  return [];
};

// 获取关联奖品ID
const getRewardList = async (param) => {
  const { quotaId } = param;
  if (quotaId) {
    const { data } = await getRewards({
      quotaId,
    });
    return data.map((item) => ({
      label: item.displayName,
      value: item.quotaDetailId,
    }));
  }
  return [];
};

export const awardShowConfigColumnsFn = () => [
  {
    title: "奖品名称",
    dataIndex: "showName",
    key: "showName",
    formItemProps: {
      rules: [
        {
          required: true,
          message: "此项为必填项",
        },
        {
          validator: (_, value) => {
            const reg = /(^\s+)|(\s+$)/;
            if (reg.test(value)) {
              return Promise.reject(new Error("前后不能有空格"));
            }
            return Promise.resolve();
          },
        },
      ],
    },
    fieldProps: {
      placeholder: "请输入奖品名称",
    },
  },
  {
    title: "奖品补充描述",
    dataIndex: "rewardTitle",
    key: "rewardTitle",
    fieldProps: {
      placeholder: "请输入奖品补充描述",
    },
  },
  {
    title: "奖品图片",
    dataIndex: "showPicUrl",
    key: "showPicUrl",
    // tooltip: '图片尺寸，960px * 315px， 不超过150kb',
    valueType: "inputUpload",
    align: "center",
    width: 200,
    fieldProps: {
      limit: {
        // width: 960,
        // height: 315,
        size: 150 * 1024,
        type: ["png", "jpg", "jpeg"],
        maxCount: 1,
        description: true,
      },
      appKey: "finance-operation-admin.vmic.xyz",
      appName: "finance-operation-admin",
    },
    formItemProps: {
      rules: [
        {
          required: true,
          message: "此项为必填项",
        },
      ],
    },
  },
  {
    title: "奖品描述",
    dataIndex: "showDesc",
    key: "showDesc",
    formItemProps: {
      rules: [
        {
          required: true,
          message: "此项为必填项",
        },
      ],
    },
    fieldProps: {
      placeholder: "填空格则为空",
    },
    renderFormItem: () => {
      return <RichTextEditor />;
    },
  },
  {
    title: "是否跳转",
    valueType: "radio",
    dataIndex: "shouldRedirect",
    key: "shouldRedirect",
    fieldProps: {
      options: [
        { label: "否", value: 0 },
        { label: "是", value: 1 },
      ],
    },
    formItemProps: {
      rules: [
        {
          required: true,
          message: "此项为必填项",
        },
      ],
    },
    initialValue: 1,
  },
  {
    valueType: "dependency",
    name: ["shouldRedirect"],
    columns: ({ shouldRedirect }) =>
      shouldRedirect === 1
        ? [
            {
              title: "跳转链接",
              dataIndex: "redirectUrl",
              key: "redirectUrl",
              align: "center",
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: "此项为必填项",
                  },
                  () => ({
                    validator(_, value) {
                      if (!value || value.includes("://")) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error("链接格式错误"));
                    },
                  }),
                ],
              },
            },
          ]
        : [],
  },
];

export const awardConfigColumnsFn = ({
  formDisabled,
  moduleIndex,
  awardIndex,
  quotaIdLimit
}) => [
  {
    title: "奖品类型",
    dataIndex: "awardType",
    key: "awardType", 
    valueType: "select",
    fieldProps: (form) => ({
      showSearch: true,
      allowClear: true,
      options: rewardTypeOptions,
      disabled: formDisabled || quotaIdLimit,
      onChange: () => {
        console.log('====awardType=====', {moduleIndex, awardIndex});
        
        // 奖池弹窗
        // eslint-disable-next-line
        if (isNaN(moduleIndex)) {
          form.setFieldValue("quotaId", "");
          form.setFieldValue("awardValue", "");
        } else {
          const awardConfigs = form.getFieldValue("awardConfigs");
          const awardList = form.getFieldValue([
            "awardConfigs",
            moduleIndex,
            "awardList",
          ]);
          const tempAwardList = awardList.map((item, i) => {
            if (i === awardIndex) {
              console.log('置空');
              
              item.quotaId = "";
              item.awardValue = "";
              return item;
            }
            return item;
          });
          const tempAwardConfigs = awardConfigs.map((config, i) => {
            if (i === moduleIndex) {
              config.awardList = tempAwardList;
              return config;
            }
            return config;
          });
          form.setFieldValue("awardConfigs", tempAwardConfigs);
        }
      },
    }),
    formItemProps: {
      rules: [
        {
          required: true,
          message: "此项为必填项",
        },
      ],
    },
  },
  {
    valueType: "dependency",
    name: ["awardType"],
    columns: ({ awardType }) => [
      {
        title: "奖品库存id",
        dataIndex: "quotaId",
        valueType: "select",
        request: quotaIdRequest,
        params: { rewardType: awardType },
        fieldProps: (form) => ({
          showSearch: true,
          disabled:
            formDisabled || awardType === "qd" || awardType === "cashCard" ||  quotaIdLimit,
          onChange: (e) => {
            // eslint-disable-next-line
            if (isNaN(moduleIndex)) {
              form.setFieldValue("awardValue", "");
            } else {
              const awardConfigs = form.getFieldValue("awardConfigs");
              const awardList = form.getFieldValue([
                "awardConfigs",
                moduleIndex,
                "awardList",
              ]);

              const tempAwardList = awardList.map((item, i) => {
                if (i === awardIndex) {
                  item.awardValue = "";
                  return item;
                }
                return item;
              });
              const tempAwardConfigs = awardConfigs.map((awardConfig, i) => {
                if (i === moduleIndex) {
                  awardConfig.awardList = tempAwardList;
                  return awardConfig;
                }
                return awardConfig;
              });
              form.setFieldValue("awardConfigs", tempAwardConfigs);
            }
          },
        }),
        formItemProps: {
          rules: [
            {
              required: awardType !== "qd" && awardType !== "cashCard",
              message: "此项为必填项",
            },
          ],
        },
      },
    ],
  },
  {
    valueType: "dependency",
    name: ["awardType", "quotaId"],
    columns: ({ awardType, quotaId }) => {
      if (awardType === "redPacketCoupon") {
        return [
          {
            title: "模版id",
            dataIndex: "awardValue",
            valueType: "select",
            request: getTemplateIdList,
            params: { rewardType: awardType },
            fieldProps: {
              showSearch: true,
            },
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: "此项为必填项",
                },
              ],
            },
          },
        ];
      }
      if (awardType === "coupon") {
        return [
          {
            title: "模版id",
            dataIndex: "awardValue",
            valueType: "select",
            params: { quotaId },
            request: getRewardList,
            fieldProps: {
              showSearch: true,
            },
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: "此项为必填项",
                },
              ],
            },
          },
        ];
      }
      return [];
    },
  },
  {
    title: "奖品数量",
    dataIndex: "awardCount",
    valueType: "digit",
    fieldProps: {
      min: 1,
      precision: 0,
      style: { width: "100%" },
      placeholder: "请输入正整数",
    },
    formItemProps: {
      rules: [
        {
          required: true,
          message: "此项为必填项",
        },
      ],
      extra: "如为数量，单位为个，如为金额，单位为分",
    },
  },
  {
    title: "奖品总库存",
    dataIndex: "stock",
    valueType: "digit",
    fieldProps: {
      min: 0,
      precision: 0,
      style: { width: "100%" },
      placeholder: "请输入整数",
    },
    formItemProps: {
      rules: [
        {
          required: true,
          message: "此项为必填项",
        },
      ],
      extra: "填入0，则为无上限",
    },
  },
];
