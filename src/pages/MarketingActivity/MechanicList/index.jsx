import React, { useRef, useState } from 'react';
import {
  Space,
  Divider,
  Button,
  Spin,
  notification,
  Popconfirm,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import CommonTableList from '@/components/CommonTableList';
import {
  getActivityPlayList, deleteActivityPlay, activityPlayOnline, getActivityPlayDetail,
} from '@/services/marketingActivity/mechanics.js';
import { nanoid } from 'nanoid';
import OperateHistory from './components/OperateHistory';
import { playModelOptions } from '../constant';
import DetailModal from './components/DetailModal/index';

const MechanicList = () => {
  const actionRef = useRef();
  const [modalVisible, setModalVisible] = useState(false);
  const [actionType, setActionType] = useState('');
  const [loading, setLoading] = useState(false);
  const [initialConfig, setInitialConfig] = useState({});

  const operateHistoryRender = (props) => (
    <>
      <OperateHistory
        key="operateHistory"
        id={props.initialConfig?.id}
        bizCode="play"
        disabled={false}
      />
    </>
  );

  /**
 * 表格刷新
 */
  const handleReload = () => {
    actionRef.current?.reload();
  };

  /**
 * 列表操作
 * actionType: view edit delete audit add copy
 */
  const handleAction = async (record, actionTypeParam) => {
    // 删除
    const { id } = record || {};
    if (actionTypeParam === 'delete') {
      const res = await deleteActivityPlay({ id });
      if (res?.code === '200') {
        handleReload();
      }
      return;
    }
    // 请求详情信息
    if (id) {
      setLoading(true);
      const res = await getActivityPlayDetail({ id });
      if (res?.code === '200' && res.data) {
        const { startTime, endTime } = res.data;
        const tempRecord = {
          ...res.data,
          activityTime: [startTime, endTime],
          onlineStatus: record.onlineStatus, // 为2为线上数据编辑 需要特殊处理
        };
        if (actionTypeParam === 'copy' || actionTypeParam === 'add') {
          delete tempRecord.id;
          delete tempRecord.code;
        }
        // 手动传入tabId
        if (tempRecord?.lotteryPlayStrategyList?.length) {
          const tempLotteryPlayStrategyList = tempRecord?.lotteryPlayStrategyList.map((strategy) => {
            strategy.tabId = nanoid();
            if (strategy?.playConditionGroup?.conditionList?.length) {
              strategy.playConditionGroup.conditionList = strategy.playConditionGroup.conditionList.map((item) => ({
                ...item,
                awardValue: (item?.awardValue?.split(',') || []).map((a) => Number(a)),
              }));
            }
            return strategy;
          });
          tempRecord.lotteryPlayStrategyList = tempLotteryPlayStrategyList;
        }
        console.log('===tempRecord===', tempRecord);

        setInitialConfig(tempRecord);
      }
    } else {
      setInitialConfig({});
    }
    setActionType(actionTypeParam);
    setLoading(false);
    setModalVisible(true);
  };

  /**
 * 活动上下线
 * onlineStatus 1 上线 11 下线后不支持编辑
 */
  const changeOnlineStatus = async (record, onlineOperation) => {
    const params = {
      id: record.id,
      onlineOperation,
    };
    const res = await activityPlayOnline(params);
    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
        description: res?.message || '',
      });
      handleReload();
    }
  };

  const columns = [
    {
      title: '玩法code',
      dataIndex: 'code',
      key: 'code',
      align: 'center',
      width: 180,
      fixed: 'left',
      ellipsis: true,
      copyable: true,
    },
    {
      title: '玩法名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      width: 200,
      fixed: 'left',
      ellipsis: true,
    },
    {
      title: '玩法模型',
      dataIndex: 'modelType',
      key: 'modelType',
      align: 'center',
      width: 200,
      ellipsis: true,
      valueType: 'select',
      fieldProps: {
        options: playModelOptions,
      },
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      valueType: 'dateTime',
      key: 'createTime',
      align: 'center',
      width: 180,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      valueType: 'dateTime',
      align: 'center',
      width: 180,
      ellipsis: true,
      hideInSearch: true,
    },
    {
      title: '操作员',
      key: 'operatorName',
      dataIndex: 'operatorName',
      align: 'center',
      width: 150,
    },
    {
      title: '审核员',
      key: 'auditorName',
      dataIndex: 'auditorName',
      hideInSearch: true,
      align: 'center',
      width: 150,
    },
    {
      title: '状态',
      dataIndex: 'onlineStatus',
      key: 'onlineStatus',
      valueType: 'select',
      valueEnum: {
        1: {
          text: '禁用',
          status: 'Error',
        },
        2: {
          text: '启用',
          status: 'Success',
        },
        3: {
          text: '已失效',
          status: 'Default',
        },
        4: {
          text: '数据准备/发送中',
          status: 'Default',
        },
        5: {
          text: '已完成',
          status: 'Default',
        },
        6: {
          text: '已冻结',
          status: 'Error',
        },
        11: {
          text: '已下线',
          status: 'Error',
        },
      },
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
      width: 150,
      align: 'center',
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      key: 'auditStatus',
      valueType: 'select',
      valueEnum: {
        1: {
          text: '待审核',
          status: 'Default',
        },
        2: {
          text: '审核通过',
          status: 'Success',
        },
        3: {
          text: '审核驳回',
          status: 'Error',
        },
      },
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
      width: 150,
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      hideInSearch: true,
      width: 400,
      align: 'center',
      fixed: 'right',
      render: (text, record) => (
        <Space split={<Divider type="vertical" />} key={record.id}>
          {record.supportEdit === 1 && (
            <Button
              key="edit"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'edit');
              }}
            >
              编辑
            </Button>
          )}
          {record.supportDelete === 1 && (
            <Popconfirm
              title="删除后无法恢复，确认删除吗？"
              onConfirm={() => {
                handleAction(record, 'delete');
              }}
              key="del"
              onCancel={() => { }}
              okText="确定"
              cancelText="取消"
            >
              <Button key="delete" type="link" size="small">
                删除
              </Button>
            </Popconfirm>
          )}
          {record.supportApprove === 1 && (
            <Button
              key="audit"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'audit');
              }}
            >
              审核
            </Button>
          )}
          {record.supportView === 1 && (
            <Button
              key="view"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'view');
              }}
            >
              详情
            </Button>
          )}
          {record.supportOffline === 1 && (
            <Popconfirm
              title="确认下线？"
              description={(
                <div style={{ width: 300, color: 'red' }}>
                  审核通过需点击上线、玩法上线后不支持上下线
                </div>
              )}
              onConfirm={() => {
                changeOnlineStatus(record, 11);
              }}
              key="offline"
              onCancel={() => { }}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                size="small"
              >
                下线
              </Button>
            </Popconfirm>

          )}
          {record.supportOnline === 1 && (
            <Popconfirm
              width={200}
              title="确认上线？"
              description={(
                <div style={{ width: 300, color: 'red' }}>
                  审核通过需点击上线、玩法上线后不支持上下线
                </div>
              )}
              onConfirm={() => {
                changeOnlineStatus(record, 2);
              }}
              key="online"
              onCancel={() => { }}
              okText="确定"
              cancelText="取消"
            >
              <Button
                type="link"
                size="small"
              >
                上线
              </Button>
            </Popconfirm>

          )}
          {record.supportCopy === 1 && (
            <Button
              key="copy"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'copy');
              }}
            >
              复制
            </Button>
          )}
        </Space>
      ),
    },
  ];

  /**
   * 列表数据请求
   */
  const getTableData = async (params) => {
    const tempParams = {
      page: params.current,
      ...params,
    };
    const res = await getActivityPlayList(tempParams);
    if (res?.code === '200' && res.data) {
      return {
        data: res.data.list,
        total: res.data.total,
      };
    }
    notification.error({
      message: '获取数据失败',
      description: res?.msg,
    });
    return {
      data: [],
      total: 0,
    };
  };

  return (
    <>
      <Spin spinning={loading}>
        <CommonTableList
          key="list"
          columns={columns}
          request={getTableData}
          actionRef={actionRef}
          search={{
            defaultCollapsed: false,
            labelWidth: 100,
          }}
          toolBarRender={() => [
            <Button
              key="button"
              icon={<PlusOutlined />}
              onClick={() => {
                handleAction({}, 'add');
              }}
              type="primary"
            >
              新建
            </Button>,
          ]}
        />
        {/* 弹窗 */}
        <DetailModal
          setModalVisible={setModalVisible}
          modalVisible={modalVisible}
          setActionType={setActionType}
          initialConfig={initialConfig}
          setInitialConfig={setInitialConfig}
          actionType={actionType}
          handleReload={handleReload}
          operateHistoryRender={operateHistoryRender}
        />
      </Spin>
    </>
  );
};

export default MechanicList;
