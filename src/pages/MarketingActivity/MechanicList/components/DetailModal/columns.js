/* eslint-disable */
import {
  BetaSchemaForm,
  ProCard,
  ProFormList,
  ProForm,
  ProFormDependency,
  ProFormSelect,
  ProFormDigit,
  FormControlRender,
  pickControlPropsWithId,
} from "@ant-design/pro-components";
import { rewardTypeOptions } from "../../../constant";
import { getCodeList } from "@/services/marketingActivity/mechanics.js";
import { getQuotaId } from "@/services/common.js";

export const getCommonColumns = ({ formDisabled, basicDisabled }) => {
  return [
    /** 通用配置 */
    {
      title: "",
      renderFormItem: () => (
        <div style={{ display: "flex", alignItems: "end" }}>
          <h2>通用配置</h2>
        </div>
      ),
    },
    {
      title: "生效时间",
      valueType: "dateTimeRange",
      dataIndex: "activityTime",
      key: "activityTime",
      formItemProps: {
        rules: [
          {
            required: true,
            message: "此项为必填项",
          },
        ],
      },
      fieldProps: {
        disabled: basicDisabled,
      },
    },
    {
      dataIndex: ["rotatingBroadcast", "lotteryRotatingBroadcast"],
      title: "是否进行抽奖轮播",
      valueType: "radio",
      fieldProps: {
        options: [
          { label: "是", value: 1 },
          { label: "否", value: 0 },
        ],
      },
      initialValue: 1,
      formItemProps: {
        rules: [
          {
            required: true,
            message: "此项为必填项",
          },
        ],
      },
    },
    {
      valueType: "dependency",
      name: [["rotatingBroadcast", "lotteryRotatingBroadcast"]],
      columns: ({ rotatingBroadcast }) =>
        rotatingBroadcast?.lotteryRotatingBroadcast === 1
          ? [
              {
                title: "抽奖轮播兜底文案",
                dataIndex: ["rotatingBroadcast", "rotatingBroadcastContent"],
                fieldProps: {
                  placeholder: "不限制输入， 多条文案以英文逗号分隔",
                },
              },
            ]
          : [],
    },
    {
      title: "关联订阅",
      dataIndex: "subscriptionCode",
      valueType: "select",
      params: { type: 2 },
      debounceTime: 500, // 优化请求频次
      request: async (values, propsValue) => {
        try {
          if (!values.keyWords && !propsValue.text) return [];

          const res = await getCodeList({
            type: values.type,
            code: values.keyWords || propsValue.text,
          });
          if (res?.data) {
            const { data } = res;
            return data.map((item) => ({
              value: item.code,
              label: item.name,
            }));
          }
          return [];
        } catch (error) {
          console.log("error=====", error);
        }
      },
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
    },
    {
      dataIndex: ["lowValueAwardConfig", "show"],
      title: "是否展示低价值奖品",
      valueType: "radio",
      fieldProps: {
        options: [
          { label: "是", value: 1 },
          { label: "否", value: 0 },
        ],
      },
      initialValue: 0,
      formItemProps: {
        rules: [
          {
            required: true,
            message: "此项为必填项",
          },
        ],
      },
    },
    {
      valueType: "dependency",
      name: ["lowValueAwardConfig", "show"],
      columns: ({ lowValueAwardConfig }) => {
        console.log("====lowValueAwardConfig====", lowValueAwardConfig);
        return lowValueAwardConfig?.show === 0
          ? [
              {
                title: "低价值奖品添加",
                renderFormItem: () => (
                  <ProFormList
                    alwaysShowItemLabel={true}
                    name={["lowValueAwardConfig", "lowValueAwardDetailDTOList"]}
                    creatorButtonProps={true}
                    min={1}
                    initialValue={[{}]}
                    disabled={formDisabled}
                    itemRender={({ listDom, action }) => (
                      <div
                        style={{
                          display: "flex",
                          alignItems: "center",
                          gap: "8px",
                        }}
                      >
                        {listDom}
                        {!formDisabled ? action : <></>}
                      </div>
                    )}
                  >
                    {(f, lowValueAwardDetailIndex, action) => {
                      const currentRowData = action.getCurrentRowData();

                      const { quotaIdList } = currentRowData;
                      return (
                        <>
                          <BetaSchemaForm
                            layoutType="Embed"
                            columns={[
                              {
                                title: "奖品类型",
                                dataIndex: "awardType",
                                key: "awardType",
                                valueType: "select",
                                fieldProps: (form) => ({
                                  showSearch: true,
                                  allowClear: true,
                                  options: rewardTypeOptions,
                                  onChange: () => {
                                    form.setFieldValue(
                                      [
                                        "lowValueAwardConfig",
                                        "lowValueAwardDetailDTOList",
                                        lowValueAwardDetailIndex,
                                        "quotaId",
                                      ],
                                      undefined
                                    );
                                  },
                                }),
                                formItemProps: {
                                  rules: [
                                    {
                                      required: true,
                                      message: "此项为必填项",
                                    },
                                  ],
                                },
                              },
                              {
                                valueType: "dependency",
                                name: [
                                  "lowValueAwardConfig",
                                  "lowValueAwardDetailDTOList",
                                  lowValueAwardDetailIndex,
                                  "awardType",
                                ],
                                columns: ({ lowValueAwardConfig }) => {
                                  console.log(
                                    "===lowValueAwardConfig====",
                                    lowValueAwardConfig
                                      ?.lowValueAwardDetailDTOList[
                                      lowValueAwardDetailIndex
                                    ].awardType
                                  );
                                  const awardType =
                                    lowValueAwardConfig
                                      ?.lowValueAwardDetailDTOList[
                                      lowValueAwardDetailIndex
                                    ].awardType;

                                  return [
                                    {
                                      title: "奖品库存id",
                                      dataIndex: "quotaIdList",
                                      valueType: "select",
                                      params: { awardType, quotaIdList },
                                      debounceTime: 500, // 优化请求频次
                                      request: async (values, propsValue) => {
                                        console.log(
                                          "values",
                                          values,
                                          "propsValue",
                                          propsValue,
                                          "quotaIdList",
                                          quotaIdList
                                        );
                                        try {
                                          // todo 回显调试
                                          let ids = [];
                                          if (values.keyWords) {
                                            ids = [Number(values.keyWords)];
                                          } else if (
                                            propsValue.text &&
                                            Array.isArray(propsValue.text)
                                          ) {
                                            ids = propsValue.text;
                                          } else if (
                                            quotaIdList &&
                                            Array.isArray(quotaIdList)
                                          ) {
                                            ids = quotaIdList;
                                          }
                                          if (
                                            !ids ||
                                            !ids?.length ||
                                            !awardType
                                          ) {
                                            return [];
                                          }
                                          const { data = [] } =
                                            await getQuotaId({
                                              type: awardType,
                                              quotaIds: ids,
                                            });
                                          return data.map((item) => ({
                                            label: item.quotaIdWithName,
                                            value: item.id,
                                          }));
                                        } catch (error) {
                                          console.log("error=====", error);
                                        }
                                      },
                                      width: "l",
                                      params: { awardType: awardType },
                                      fieldProps: (form) => ({
                                        showSearch: true,
                                        mode: "multiple", // 添加多选模式
                                        disabled:
                                          formDisabled ||
                                          ["qd", "cashCard"].indexOf(
                                            awardType
                                          ) !== -1,
                                        onChange: (e) => {
                                          console.log("===e==", e);
                                        },
                                      }),
                                      formItemProps: {
                                        rules: [
                                          {
                                            required:
                                              ["qd", "cashCard"].indexOf(
                                                awardType
                                              ) === -1,
                                            message: "此项为必填项",
                                          },
                                        ],
                                      },
                                    },
                                  ];
                                },
                              },
                            ]}
                          />
                        </>
                      );
                    }}
                  </ProFormList>
                ),
              },
            ]
          : [];
      },
    },
    /** 通用配置 */
  ];
};
