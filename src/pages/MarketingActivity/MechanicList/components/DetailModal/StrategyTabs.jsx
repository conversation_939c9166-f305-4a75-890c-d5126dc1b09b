import React, {
  useState, useRef, useImperativeHandle, forwardRef,
} from 'react';
import { notification, Tabs } from 'antd';
import { nanoid } from 'nanoid';
import PlayStrategyConfig from './PlayStrategyConfig';

const StrategyTabs = forwardRef((props, ref) => {
  const {
    value = [], onChange, actionType, modelType, strategyType, formDisabled, basicDisabled,
  } = props;
  console.log('StrategyTabs====props', props, 'value', value);
  const [activeKey, setActiveKey] = useState(value[0]?.tabId || null); // 添加 activeKey state
  const newTabIndex = useRef(1); // 添加 useRef 用于生成新标签的序号
  console.log('==StrategyTabs==props===', props);
  const noAdd = value?.length >= 1 && (modelType === 2 || (modelType === 1 && strategyType === 10));

  // 存储每个策略表单的 ref
  const strategyFormRefs = useRef({});

  // 暴露方法给父组件
  useImperativeHandle(ref, () => ({
    validate: async () => {
      // 实现验证逻辑
      // try {
      // 基础校验
      if (!value || value.length === 0) {
        console.log('111111111====111111111');
        throw new Error('请至少配置一个策略');
      }
      // 校验每个策略表单
      const validatePromises = value.map(async (strategy) => {
        const formRef = strategyFormRefs.current[strategy.tabId];
        console.log('formRef?.validate', formRef?.validate, 'formRef', formRef, 'current', strategyFormRefs.current, 'strategy', strategy);
        if (formRef) {
          if (!formRef?.validate) {
            console.log('111111111====22222');

            throw new Error(`策略 "${strategy.strategyName || '未命名策略'}" 配置不完整`);
          }
          return formRef.validate();
        }
        return true;
      });

      // 等待所有策略表单校验完成
      await Promise.all(validatePromises);
      return true;
      // } catch (error) {
      //   throw error;
      // }
    },
  }));

  /**
 * 配置策略变化
 */

  const onConfigChange = (payload, index) => {
    console.log('====onConfigChange===payload', payload);

    const newValue = value.map((item, i) => (i === index ? { ...item, ...payload } : item));
    console.log('配置策略变化====newValue', newValue);
    onChange(newValue);
  };

  // 策略配置tab操作
  const onTabEdit = (targetKey, action) => {
    if (formDisabled) {
      return notification.error({ message: '当前状态不允许删除策略' });
    }
    // 新增
    if (action === 'add') {
      const newTab = {
        tabId: nanoid(), // todo 最外层要处理tabid的逻辑
      };
      newTabIndex.current++;
      setActiveKey(newTab.tabId);
      const newList = [...value, newTab];
      console.log(' newList', newList);
      onChange(newList);
    }
    // 删除
    if (action === 'remove') {
      const targetIndex = value.findIndex((item) => item.tabId === targetKey);
      const newList = value.filter((item) => item.tabId !== targetKey);

      if (newList.length && targetKey === activeKey) {
        // 如果删除的是当前激活的标签，则激活相邻标签
        const newActiveKey = newList[targetIndex === newList.length ? targetIndex - 1 : targetIndex]?.tabId;
        setActiveKey(newActiveKey);
      }
      onChange(newList);
    }
    console.log('onTabEdit');
  };

  const items = value.map((config, index) => ({
    key: config.tabId,
    label: config.strategyName || '策略名称',
    children: (
      <PlayStrategyConfig
        key={config.tabId}
        ref={(curRef) => {
          // 保存每个策略表单的 ref
          console.log('====curRef===', curRef, 'strategyFormRefs', strategyFormRefs, strategyFormRefs.current);

          strategyFormRefs.current[config.tabId] = curRef;
        }}
        actionType={actionType}
        value={config}
        formDisabled={formDisabled}
        basicDisabled={basicDisabled}
        modelType={modelType}
        strategyType={strategyType}
        onChange={(payload) => onConfigChange(payload, index)}
      />
    ),
  }));

  const onTabChange = (key) => {
    setActiveKey(key);
  };

  return (
    <div>
      <Tabs
        type="editable-card"
        activeKey={activeKey}
        onChange={onTabChange}
        items={items}
        onEdit={onTabEdit}
        hideAdd={noAdd || formDisabled}
      />
    </div>
  );
});

export default StrategyTabs;
