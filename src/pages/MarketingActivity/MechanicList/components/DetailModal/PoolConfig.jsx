import React, { memo } from 'react';
import { CopyOutlined } from '@ant-design/icons';
import {
  ProCard,
  ProForm,
  ProFormList,
} from '@ant-design/pro-components';
import { cloneDeep } from 'lodash';
import RuleConfig from './RuleConfig';
import AwardConfigTable from './AwardConfigTable';

const LotteryPoolConfig = (props) => {
  console.log('===LotteryPoolConfig===props====', props);
  const {
    value = [], onChange, form, formDisabled,
  } = props;

  return (
    <>
      <ProFormList
        name="lotteryPoolConfigs"
        creatorButtonProps={{
          creatorButtonText: '新增奖池',
        }}
        alwaysShowItemLabel
        actionRender={(field, action, defaultActionDom) => {
          // 自定义复制按钮
          const copyButton = (
            <a
              key="copy"
              onClick={() => {
                const list = form.getFieldValue('lotteryPoolConfigs') || [];
                const record = list[field.name];
                const copyData = cloneDeep(record);
                // 删除奖池code
                delete copyData.lotteryPoolCode;

                // 如果存在 awardList，处理每个奖品的 code
                if (copyData.awardList?.length) {
                  copyData.awardList = copyData.awardList.map((award) => {
                    const { code, ...rest } = award;
                    return rest;
                  });
                }
                console.log('===copyData===', copyData);

                action.add(copyData);
              }}
            >
              <CopyOutlined />
            </a>
          );

          // 返回操作按钮数组，包含复制和默认的删除按钮
          return [copyButton, defaultActionDom[1]];
        }}
        itemRender={({ listDom, action }, { index, record }) => {
          // 一个分组的 概率共享
          const probability = record?.awardList?.reduce((acc, cur) => {
            const hitProbability = Number((cur.hitProbability || 0).toFixed(2)); // 保留2位小数

            if (cur.shareGroup) {
              if (!acc.checkedShareGroups.includes(cur.shareGroup)) {
                acc.checkedShareGroups.push(cur.shareGroup);
                acc.total = Number((acc.total + hitProbability).toFixed(2)); // 累加时也保留2位小数
              }
            } else {
              acc.total = Number((acc.total + hitProbability).toFixed(2)); // 累加时保留2位小数
            }
            return acc;
          }, { checkedShareGroups: [], total: 0 }).total;

          return (
            <ProCard
              bordered
              style={{ marginBlockEnd: 8, width: '100%' }}
              title={`奖池${index + 1} 现有奖品概率之和为${probability || 0}%`}
              hoverable
              extra={!formDisabled ? action : false}
            >
              {listDom}
            </ProCard>
          );
        }}
      >
        {(f, index, action) => {
          const currentRowData = action.getCurrentRowData();
          console.log('==currentRowData===', currentRowData);

          return (
            <>
              <RuleConfig currentRowData={currentRowData} moduleIndex={index} mode="lotteryPoolConfigs" />
              <ProForm.Group title="奖池奖品">
                <AwardConfigTable formDisabled={formDisabled} onChange={onChange} awardList={currentRowData?.awardList} lotteryPoolConfigs={value} moduleIndex={index} form={form} />
              </ProForm.Group>
            </>
          );
        }}
      </ProFormList>
    </>
  );
};

export default memo(LotteryPoolConfig);
