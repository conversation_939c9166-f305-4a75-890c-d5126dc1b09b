import React, { memo, useRef } from 'react';
import {
  Button, Space, Drawer, notification,
} from 'antd';
import { BetaSchemaForm, ProForm } from '@ant-design/pro-components';
import { ConfigProvider } from 'finance-busi-components-toB';
import { titleMap } from '@/constant/config.js';

import { auditActivityPlay, saveActivityPlay } from '@/services/marketingActivity/mechanics.js';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { playModelOptions, strategyTypeOptions } from '../../../constant';
import StrategyTabs from './StrategyTabs';
import { getCommonColumns } from './columns';

const DetailModal = (props) => {
  const {
    setModalVisible,
    modalVisible,
    actionType,
    setActionType,
    initialConfig = {},
    handleReload,
    setInitialConfig,
    noHistoryTable = false,
    operateHistoryRender,
  } = props;

  const formRef = useRef();
  const strategyTabsRef = useRef(); // 新增 strategyTabsRef
  const isOnlineEdit = initialConfig?.onlineStatus === 2 && actionType === 'edit';
  const basicDisabled = actionType === 'view' || actionType === 'audit'; // 大部分都是这个 （在线不可编辑）
  const formDisabled = basicDisabled || isOnlineEdit; // 上线状态仅有时间和奖励可编辑

  console.log('11', { basicDisabled, formDisabled });

  /**
  * 重置数据 关闭弹窗
  */
  const resetDialog = () => {
    setModalVisible(false);
    setActionType('');
    setInitialConfig({});
    // 刷新列表
    handleReload && handleReload();
  };

  // 保存数据
  const handleSave = async (values) => {
    const res = await saveActivityPlay(values);
    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
      });
      resetDialog();
    }
  };

  // 审核
  const handleAudit = async (param) => {
    const res = await auditActivityPlay({ ...param, id: initialConfig.id });
    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
      });
      resetDialog();
    }
  };

  // 表单提交
  const handleSubmit = async () => {
    // 1. 先校验主表单
    const values = await formRef.current?.validateFields();
    console.log('==values==', values);
    const { lotteryPlayStrategyList, modelType, strategyType } = values;
    // 2. 校验策略数量 (集卡玩法  抽奖玩法-常规模式 只支持单个策略配置)
    if (modelType === 2 || (modelType === 1 && strategyType === 10)) {
      if (lotteryPlayStrategyList?.length !== 1) {
        return notification.error({ message: '当前玩法和模型必须且仅能配置一个策略' });
      }
    }

    // 3.校验策略表单
    try {
      await strategyTabsRef.current?.validate(); // 父组件调用子组件方法
    } catch (error) {
      console.log('error', error, 'error.message', error.message);
      notification.error({
        message: '策略配置校验失败',
        description: error.message || '请检查策略配置是否完整',
      });
      return;
    }

    const params = {
      ...cloneDeep(initialConfig),
      ...cloneDeep(values),
    };
    // 删除record传入的特殊参数
    params.onlineStatus = undefined;

    // 活动时间格式处理
    if (params.activityTime) {
      params.startTime = dayjs(params.activityTime[0]).format(
        'YYYY-MM-DD HH:mm:ss',
      );
      params.endTime = dayjs(params.activityTime[1]).format(
        'YYYY-MM-DD HH:mm:ss',
      );
      params.activityTime = undefined;
    }
    // 策略配置-权益-权益id处理
    params.lotteryPlayStrategyList = params.lotteryPlayStrategyList?.map((strategy) => {
      // 置空tabId
      strategy.tabId = undefined;
      // 库存id格式处理
      if (strategy.playConditionGroup?.conditionList?.length) {
        // 这里目前写死条件类型and
        strategy.playConditionGroup.conditionType = 'and';
        const newConditionList = strategy.playConditionGroup.conditionList.map((item) => ({
          ...item,
          awardValue: item.awardValue ? item.awardValue.join(',') : '',
        }));
        strategy.playConditionGroup.conditionList = newConditionList;
        return strategy;
      }
      return strategy;
    });

    console.log('==params==', params);
    handleSave(params);
  };

  const columns = [
    {
      title: '',
      renderFormItem: () => (
        <div style={{ display: 'flex', alignItems: 'end' }}>
          <h2 style={{ margin: 0 }}>玩法配置</h2>
          {' '}
          {/* 去除默认 margin */}
          <span style={{ color: 'red', marginLeft: 10 }}>
            玩法库存配置请勿共用库存，共用库存无法校验数量是否充足，风险较大
          </span>
        </div>
      ),
    },
    {
      title: '玩法名称',
      dataIndex: 'name',
      key: 'name',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '玩法模型',
      dataIndex: 'modelType',
      key: 'modelType',
      valueType: 'select',
      formItemProps: () => ({
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
        extra: (
          <div>
            <div>【抽奖玩法】-使用抽奖机会进行抽奖</div>
            <div>【集卡&活跃要求玩法】-满足集卡条件&活跃要求 或 满足集卡条件&不满足活跃要求</div>
          </div>
        ),
      }),
      fieldProps: (form) => ({
        options: playModelOptions,
        onChange: (e) => {
          console.log('modelType====e===', e, form.getFieldsValue());
          const lotteryPlayStrategyList = form.getFieldValue('lotteryPlayStrategyList');
          console.log('lotteryPlayStrategyList', lotteryPlayStrategyList);
          // todo 这里有很多数据需要重置
          form.setFieldValue('strategyType', undefined);
          if (lotteryPlayStrategyList?.length) {
            const newLotteryPlayStrategyList = lotteryPlayStrategyList.map((strategy) => {
              strategy.playConditionGroup = {
                conditionType: 'and',
              };
              strategy.playConditionGroup.conditionList = e === 1 ? [{
                conditionBizType: 1, // 权益
                awardType: 'activityCoin',
                awardValue: undefined,
              }] : [{
                conditionBizType: 1, // 权益
                awardType: 'virtual',
                awardValue: undefined,
              },
              {
                conditionBizType: 2, // 活跃
              }];

              return strategy;
            });
            console.log('lotteryPlayStrategyList=====', newLotteryPlayStrategyList);

            form.setFieldValue('lotteryPlayStrategyList', newLotteryPlayStrategyList);
          }
        },
      }),
    },
    {
      valueType: 'dependency',
      name: ['modelType'],
      columns: ({ modelType }) => [
        {
          title: '模型类型',
          dataIndex: 'strategyType',
          key: 'strategyType',
          valueType: 'select',
          fieldProps: () => {
            const targetStrategyOptions = modelType === 1 ? strategyTypeOptions : strategyTypeOptions.slice(0, 1);
            return {
              options: targetStrategyOptions,
            };
          },
          formItemProps: {
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
            ],
          },
        },
      ],
    },
    /** 通用配置 */
    ...getCommonColumns({ actionType, formDisabled, basicDisabled }),
    {
      title: '',
      renderFormItem: () => (
        <div style={{ display: 'flex', alignItems: 'end' }}>
          <h2>策略配置</h2>
        </div>
      ),
    },
    {
      valueType: 'dependency',
      name: ['modelType', 'strategyType'],
      columns: ({ modelType, strategyType }) => [
        {
          title: '策略配置',
          key: 'lotteryPlayStrategyList',
          formItemProps: {
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
            ],
          },
          renderFormItem: (schema, config, form) => {
            const handleStrategyChange = (strategies) => {
              console.log('==strategies==', strategies, 'form', form);
              // 更新表单中的策略配置值
              form.setFieldValue('lotteryPlayStrategyList', strategies);
            };

            return (
              <StrategyTabs
                ref={strategyTabsRef} // 添加 ref
                onChange={handleStrategyChange}
                actionType={actionType}
                modelType={modelType}
                strategyType={strategyType}
                formDisabled={formDisabled} // 此处的disabled特殊处理 上线状态可编辑
                basicDisabled={basicDisabled}
              />
            );
          },
        },
      ],
    },
  ];

  return (
    <Drawer
      width={1200}
      title={titleMap[actionType] || ''}
      onClose={() => resetDialog()}
      open={modalVisible}
      destroyOnClose
      footer={(
        <Space>
          <Button
            onClick={() => {
              resetDialog();
            }}
          >
            {actionType === 'view' ? '关闭' : '取消'}
          </Button>
          {['edit', 'add', 'copy'].includes(actionType) && (
            <>
              <Button
                type="primary"
                onClick={() => {
                  handleSubmit();
                }}
              >
                提交
              </Button>
            </>
          )}
          {actionType === 'audit' && (
            <>
              <Button
                type="primary"
                onClick={() => {
                  handleAudit({ auditOperation: 3 });
                }}
              >
                审核驳回
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  handleAudit({ auditOperation: 2 });
                }}
              >
                审核通过
              </Button>
            </>
          )}
        </Space>
      )}
    >

      <ConfigProvider>
        <ProForm
          formRef={formRef}
          shouldUpdate={false}
          layout="horizontal"
          labelCol={{ span: 3 }}
          wrapperCol={{ span: 21 }}
          submitter={false}
          initialValues={initialConfig}
          disabled={formDisabled}
        >
          <BetaSchemaForm layoutType="Embed" columns={columns} />
        </ProForm>
      </ConfigProvider>
      {/* 操作记录 */}
      {!noHistoryTable && (basicDisabled) && (
        operateHistoryRender({ initialConfig })
      )}
    </Drawer>
  );
};

export default memo(DetailModal);
