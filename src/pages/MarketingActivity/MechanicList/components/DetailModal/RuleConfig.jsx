import React, { memo } from 'react';
import {
  ProForm,
  ProFormSelect,
  ProFormDigit,
  ProFormDependency,
  FormControlRender, pickControlPropsWithId,
} from '@ant-design/pro-components';
import NumericRange from '@/components/NumericRange';
import { checkNumericRange, checkNumericRangeRequired } from '@/utils/validate.js';

// 抽离公共的表单规则
const REQUIRED_RULE = [
  {
    required: true,
    message: '此项为必填项',
  },
];

// 规则类型选项
const ruleTypeOptions = [
  { label: '无特定规则', value: 1 },
  { label: '频次门槛', value: 2 },
  { label: '第n次抽奖指定获得', value: 5 },
  { label: '满足活跃条件指定获得 ', value: 6 },
];

// 频次周期选项
const frequencyTypeOptions = [
  { label: '每日', value: 1 },
  { label: '每周', value: 2 },
  { label: '每月', value: 3 },
  { label: '活动期间', value: 4 },
];

// 缓存数字输入框的通用属性
const digitFieldProps = {
  min: 1,
  precision: 0,
};

// 渲染频次门槛表单
const FrequencyThresholdForm = () => (
  <>
    <ProFormSelect
      name={['hitRule', 'frequencyType']}
      width="xl"
      label="频次门槛周期"
      options={frequencyTypeOptions}
      rules={[{ required: true, message: '此项为必填项' }]}
    />
    <ProForm.Item
      name={['hitRule', 'range']}
      label="频次门槛"
      rules={[
        { required: true, message: '这是必填项' },
        { validator: checkNumericRange },
        { validator: checkNumericRangeRequired },
      ]}
    >
      <FormControlRender>
        {(itemProps) => (
          <>
            <NumericRange {...pickControlPropsWithId(itemProps)} />
            次
          </>
        )}
      </FormControlRender>
    </ProForm.Item>
  </>
);

const RuleConfig = ({ moduleIndex, mode, formRef }) => {
  console.log('ruleRender----');
  const handleRuleTypeChange = (type) => {
    formRef?.current?.setFieldValue([mode, moduleIndex, 'hitRule'], { ruleType: type });
  };

  return (
    <ProForm.Group title="开奖规则">
      <ProFormSelect
        name={['hitRule', 'ruleType']}
        width="xl"
        label="命中规则类型"
        options={ruleTypeOptions}
        rules={[{ required: true, message: '此项为必填项' }]}
        onChange={handleRuleTypeChange}
      />
      <ProFormDependency name={['hitRule', 'ruleType']}>
        {({ hitRule }) => (
          <>
            {hitRule?.ruleType === 2 && <FrequencyThresholdForm />}
            {hitRule?.ruleType === 5 && (
              <ProFormDigit
                width="xl"
                name={['hitRule', 'logicValue']}
                label="指定次数"
                fieldProps={digitFieldProps}
                placeholder="请输入正整数"
                rules={REQUIRED_RULE}
                addonBefore="第"
                addonAfter="次"
              />
            )}
            {hitRule?.ruleType === 6 && (
              <ProFormDigit
                width="xl"
                name={['hitRule', 'logicValue']}
                label="活跃条件"
                fieldProps={digitFieldProps}
                placeholder="请输入正整数"
                rules={REQUIRED_RULE}
                addonBefore="累计活跃"
                addonAfter="天"
              />
            )}
            <ProFormDigit
              width="xl"
              name={['hitRule', 'priority']}
              label="奖励优先级"
              fieldProps={digitFieldProps}
              placeholder="请输入正整数"
              extra="数字越小优先级越高，如为1则最高优先级"
            />
          </>
        )}
      </ProFormDependency>
    </ProForm.Group>
  );
};

export default memo(RuleConfig);
