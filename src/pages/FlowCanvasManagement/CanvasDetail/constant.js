export const weekMap = {
  1: '一',
  2: '二',
  3: '三',
  4: '四',
  5: '五',
  6: '六',
  7: '日',
};

export const behaviorAList = [
  { label: '合作方维度授信成功', value: 'partnerCreditSuccess' },
  { label: '结清全部在贷', value: 'allPaidOff' },
  { label: '提额成功', value: 'increaseAmount' },
  { label: '临时提额成功', value: 'temporaryIncreaseAmount' },
  { label: '借款成功', value: 'loanSuccess' },
  { label: '用户维度授信成功', value: 'creditSuccess' },
];

export const behaviorBList = [
  { label: '借款成功', value: 'loanSuccess' },
  { label: '借款申请', value: 'loanSubmit' },
];

export const audienceTypeMap = {
  allUsers: '不限制',
  dmpTagUser: '限制dmp人群',
  userPackage: '按人群包',
  fundingChannel: '按资金方',
};

export const strategySelectMap = {
  systemRandom: {
    text: '系统抽样',
    desc: '（对照组无需输入，实验组输入排序倍数）',
  },
  simpleRandom: {
    text: '随机数随机',
    desc: '（各组填写分流比例，输入数字）',
  },
  saltHash: {
    text: '哈希随机',
    desc: '（各组填写分流比例，输入数字）',
    groupingKey: ['openId', 'imei'],
  },
  tail: {
    text: '尾号策略',
    desc: '（各组填写分组key尾号，输入0-9数字或a-f小写字母）',
  },
  custom: {
    text: '自定义标签',
    desc: '（各组填写dmp标签id）',
  },
  commerSpecialHash: {
    text: '商业化哈希算法',
    desc: '（各组填写从00-99的区间分段，区间分段的连接符号为-）',
  },
  loanCondition: {
    text: '按条件分流',
    desc: '（各组填写从00-99的区间分段，区间分段的连接符号为-）',
  },
};

export const strategySubSelectMap = {
  increaseAmount: '提额幅度',
  creditAmount: '授信金额',
};
