import React, {
  useRef,
} from 'react';
import CommonTableList from '@/components/CommonTableList';
import {
  queryLogList,
} from '@/services/canvas';
import dayjs from 'dayjs';
import { Button } from 'antd';

const opList = ({ id, handleDetailView }) => {
  const actionRef = useRef();
  const columns = [
    {
      title: '操作编号',
      key: 'id',
      dataIndex: 'id',
      align: 'center',
      hideInSearch: true,
      render: (text, record) => (
        <span style={{ color: '#1890ff' }}>{record.id}</span>
      ),
    },
    {
      title: '操作员',
      key: 'operatorName',
      dataIndex: 'operatorName',
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '操作内容',
      key: 'operatorType',
      dataIndex: 'operatorType',
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '操作时间',
      key: 'createTime',
      dataIndex: 'createTime',
      hideInSearch: true,
      align: 'center',
      renderText: (text) => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : ''),
    },
    {
      title: '操作备注',
      key: 'auditRemark',
      dataIndex: 'auditRemark',
      hideInSearch: true,
      align: 'center',
    },
    {
      title: '操作详情',
      dataIndex: 'actionType',
      key: 'actionType',
      align: 'center',
      fixed: 'right',
      hideInSearch: true,
      render: (text, record) => (
        <Button
          key="edit"
          type="link"
          size="small"
          onClick={() => {
            handleDetailView && handleDetailView(record, id);
          }}
          disabled={false}
        >
          查看详情
        </Button>
      ),
    },
  ];
  const getOperationHistory = async (params) => {
    const param = {
      page: params.current,
      pageSize: params.pageSize,
      operationId: id,
      bizCode: '1',
    };
    let res = {};
    res = await queryLogList(param);
    if (res?.code === '200' && res.data) {
      return {
        data: res.data.list,
        total: res.data.total,
      };
    }
  };
  return (
    <div className="canvas-op-list">
      <CommonTableList
        columns={columns}
        search={false}
        bordered
        scroll={{ x: 'max-content' }}
        pagination={{
          pageSize: 10,
          showSizeChanger: false,
          showTotal: (total) => `共${total}项`,
        }}
        headerTitle="操作记录"
        request={getOperationHistory}
        actionRef={actionRef}
      />
    </div>
  );
};

export default opList;
