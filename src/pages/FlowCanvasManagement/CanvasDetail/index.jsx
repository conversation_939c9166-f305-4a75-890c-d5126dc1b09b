/* eslint-disable max-classes-per-file */
import LogicFlow from '@logicflow/core';
import '@logicflow/core/dist/index.css';
import { register } from '@logicflow/react-node-registry';
import {
  Modal, Button, Drawer, Space, message,
  notification,
} from 'antd';
import React, {
  useRef, useState, useEffect, useMemo,
} from 'react';
import {
  ProForm, ProFormText, ProFormSelect, ProFormSwitch, ProFormDependency,
  ProFormTimePicker, StepsForm,
} from '@ant-design/pro-components';
import classNames from 'classnames';
import nanoid from 'nano-id';
// import { useParams } from 'react-router-dom';
import {
  getBusinessTypeList, getProductTypeList, saveCanvas, getCanvasDetail,
} from '@/services/canvas';
import { getUser } from '@/services/customerService/customerCommon';
import { getUsePackageView } from '@/services/common';
import dayjs from 'dayjs';
import TriggerForm from './components/triggerForm';
import AudienceForm from './components/audienceForm';
import CustomNode from './components/customNode';
import SplitForm from './components/splitForm';
import SupplyForm from './components/supplyForm';
import { autoLayout } from './layoutUtils';

import './index.less';

const VERTICAL_GAP = 110; // 垂直间距
const HORIZONTAL_GAP = 40; // 水平间距
const MIN_NODE_WIDTH = 300; // 节点最小宽度

const CanvasDetail = ({
  canvasId = '', optType = 'view', closeCanvas, content = null,
}) => {
  const lfRef = useRef(null);
  const canvasListRef = useRef(null);
  const parentChildrenMap = useRef(new Map());
  const childParentMap = useRef(new Map());
  const formCreateRef = useRef(null);
  const formStepsRef = useRef(null);
  const formTriggerRef = useRef(null);
  const formAudienceRef = useRef(null);
  const formSplitRef = useRef(null);
  const formSupplyRef = useRef(null);
  const root1Ref = useRef({});
  const root2Ref = useRef({});
  const [isModalOpenAddNode, setIsModalOpenAddNode] = useState(false);
  const [addNodeType, setAddNodeType] = useState('splitFlow');
  const [addNodeParent, setAddNodeParent] = useState(null);
  const [curNode, setCurNode] = useState(null);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [canvasDetail, setCanvasDetail] = useState({});
  const [visibleStepsForm, setVisibleStepsForm] = useState(false);
  const [stepCurrent, setStepCurrent] = useState(0);
  const [isModalOpenSplit, setIsModalOpenSplit] = useState(false);
  const [isModalOpenSupply, setIsModalOpenSupply] = useState(false);
  const [userPackageData, setUserPackageData] = useState([]);
  const [nodeMode, setNodeMode] = useState('');
  const [userInfo, setUserInfo] = useState({});
  const [curCopyNode, setCurCopyNode] = useState(null);
  const [productTypeLoading, setProductTypeLoading] = useState(false);

  const readonly = useMemo(() => ['view'].includes(optType), [optType]); // view edit copy add

  const getUserInfo = async () => {
    const res = await getUser();
    if (res?.code === '200' && res?.data) {
      setUserInfo({
        ...res.data,
      });
    }
  };
  const fetchDetailData = async () => {
    try {
      const res = await getCanvasDetail({ id: canvasId });
      return res.data || {};
    } catch (error) {
      message.error('获取详情失败');
      return {};
    }
  };

  function autoLayoutFuction() {
    autoLayout(
      lfRef.current,
      root1Ref.current,
      parentChildrenMap.current,
      childParentMap.current,
      VERTICAL_GAP,
      HORIZONTAL_GAP,
      MIN_NODE_WIDTH,
    );
  }

  function addChildNode(parentId, type = 'supply', ifBlankNode = true) {
    // 创建新节点
    const properties = {
      name: '',
      width: type === 'supply' ? 300 : 110,
      visible: 2,
    };
    if (type === 'supply' && ifBlankNode) {
      properties.isBlankNode = true;
    }
    const newNode = lfRef.current.addNode({
      id: nanoid(10),
      type,
      properties,
    });

    parentChildrenMap.current.set(parentId, [
      ...(parentChildrenMap.current.get(parentId) || []),
      newNode.id,
    ]);
    childParentMap.current.set(newNode.id, parentId);

    // 自动触发布局更新
    setTimeout(() => {
      newNode.setProperties({ visible: 1 });
      autoLayoutFuction();
    });
    return newNode;
  }
  const deleteNode = (nodeId) => {
    const recursiveDelete = (targetId) => {
      const children = parentChildrenMap.current.get(targetId) || [];
      children.forEach((childId) => recursiveDelete(childId));
      const edges = lfRef.current.getGraphData().edges || [];
      // 删除关联边
      edges.forEach((edge) => {
        if (edge.sourceNodeId === targetId || edge.targetNodeId === targetId) {
          lfRef.current.deleteEdge(edge.id);
        }
      });

      lfRef.current.deleteNode(targetId);
      parentChildrenMap.current.delete(targetId);
      childParentMap.current.delete(targetId);
    };

    // 更新父节点关系
    const parentId = childParentMap.current.get(nodeId);
    if (parentId) {
      parentChildrenMap.current.set(parentId,
        parentChildrenMap.current.get(parentId)?.filter((id) => id !== nodeId) || []);
    }

    recursiveDelete(nodeId);
    autoLayoutFuction();
  };
  function processAddNode(node) {
    setIsModalOpenAddNode(true);
    setNodeMode('add');
    setAddNodeParent(node);
  }
  function processClickNode(node) {
    setCurNode(node);
    setNodeMode('detail');
    // const properties = root1Ref.current.getProperties();
    // const properties2 = root2Ref.current.getProperties();
    switch (node.type) {
      case 'trigger':
        setStepCurrent(0);
        setVisibleStepsForm(true);
        // formTriggerRef?.current?.setFieldsValue({
        //   trigger: {
        //     ...properties,
        //     executeStardEnd: [properties.startTime, properties.endTime],
        //   },
        // });
        // formAudienceRef.current?.setFieldsValue({
        //   audience: properties2,
        // });
        break;
      case 'audience':
        setStepCurrent(1);
        setVisibleStepsForm(true);
        // formTriggerRef?.current?.setFieldsValue({
        //   trigger: properties,
        // });
        // formAudienceRef.current?.setFieldsValue({
        //   audience: properties2,
        // });
        break;
      case 'splitFlow':
        setIsModalOpenSplit(true);
        break;
      case 'supply':
        setIsModalOpenSupply(true);
        // formSupplyRef.current?.setFieldsValue({
        //   ...node.getProperties(),
        // });
        break;
      default:
        break;
    }
  }
  function processCopyNode(node) {
    setCurCopyNode(node);
  }
  function processAddCelue(node) {
    setCurNode(node);
    setIsModalOpenAddNode(true);
  }
  function onClickPasteNode(node, copyNode) {
    const realCpyNode = copyNode.getData().properties;
    const tarNode = node.getData().properties;
    delete realCpyNode.group;
    delete realCpyNode.basis;
    if (tarNode.isBlankNode) {
      node.setProperties({ ...realCpyNode, isBlankNode: false });
    } else {
      const newNode = addChildNode(node.id, node.type, false);
      newNode.setProperties(realCpyNode);
    }
    setTimeout(() => {
      lfRef.current.emit('custom:copy-node-change', copyNode);
    });
  }
  function setInitEdege() {
    if (['copy', 'edit', 'view'].includes(optType)) return;
    lfRef.current.addEdge({
      id: nanoid(10),
      sourceNodeId: root1Ref.current.id,
      targetNodeId: root2Ref.current.id,
      type: 'bezier',
      startPoint: {
        x: root1Ref.current.x,
        y: root1Ref.current.y + (root1Ref.current.properties?.height || 0) / 2 - 30,
      },
      endPoint: {
        x: root2Ref.current.x,
        y: root2Ref.current.y - (root2Ref.current.properties?.height || 0) / 2 + 30,
      },
    });
  }
  function registerNode() {
    register({
      type: 'trigger',
      component: (props) => (
        <CustomNode
          type="trigger"
          onClickNode={(node) => processClickNode(node)}
          optType={optType}
          lf={lfRef.current}
          {...props}
        />
      ),
    }, lfRef.current);
    register({
      type: 'audience',
      component: (props) => (
        <CustomNode
          type="audience"
          onConditionMount={() => setInitEdege()}
          onClickAddNode={(node) => processAddNode(node)}
          onClickNode={(node) => processClickNode(node)}
          optType={optType}
          lf={lfRef.current}
          {...props}
        />
      ),
    }, lfRef.current);
    register({
      type: 'splitFlow',
      component: (props) => (
        <CustomNode
          type="splitFlow"
          onClickAddNode={(node) => processAddNode(node)}
          onClickDelNode={(node) => deleteNode(node.id)}
          onClickNode={(node) => processClickNode(node)}
          autoLayoutFuction={() => autoLayoutFuction()}
          optType={optType}
          lf={lfRef.current}
          {...props}
        />
      ),
    }, lfRef.current);
    register({
      type: 'supply',
      component: (props) => (
        <CustomNode
          type="supply"
          onClickAddNode={(node) => processAddNode(node)}
          onClickDelNode={(node) => deleteNode(node.id)}
          onClickNode={(node) => processClickNode(node)}
          onAddCelue={(node) => processAddCelue(node)}
          onClickCopyNode={(node) => processCopyNode(node)}
          autoLayoutFuction={() => autoLayoutFuction()}
          onClickPasteNode={(node, copyNode) => onClickPasteNode(node, copyNode)}
          optType={optType}
          lf={lfRef.current}
          {...props}
        />
      ),
    }, lfRef.current);
  }
  const data = {
    // 节点数据
    nodes: [
    ],
    // 边数据
    edges: [
    ],
  };

  useEffect(() => {
    getUserInfo();
    const canvasList = canvasListRef.current;
    lfRef.current = new LogicFlow({
      container: canvasList,
      height: 1000,
      stopZoomGraph: true,
      stopScrollGraph: false,
      stopMoveGraph: false,
      adjustEdge: false,
      adjustEdgeStartAndEnd: false,
      adjustNodePosition: false,
      hideAnchors: true,
      nodeSelectedOutline: false,
      nodeTextEdit: false,
      edgeTextEdit: false,
      nodeTextDraggable: false,
      edgeTextDraggable: false,
      nodeHoverOutline: false,
      hoverOutline: false,
      background: {
        backgroundColor: 'rgb(238, 240, 243)',
      },
    });
    window.lf = lfRef;
    lfRef.current.setTheme({
      arrow: {
        offset: 0,
        verticalLength: 0,
      },
      bezier: {
        fill: 'none',
        stroke: '#64C2FF',
        strokeWidth: 6,
      },
    });
    lfRef.current.render(data);
    registerNode();
    const container = lfRef.current.container;
    const clientWidth = container.clientWidth;

    formCreateRef?.current?.resetFields();

    if (canvasId) {
      let promiseTask = null;
      if (content) {
        promiseTask = Promise.resolve(content);
      } else {
        promiseTask = fetchDetailData();
      }
      promiseTask.then((res) => {
        setCanvasDetail(res);
        if (res?.doNotDisturb?.startTime && res?.doNotDisturb?.endTime) {
          res.doNotDisturb.time = [dayjs(res.doNotDisturb.startTime, 'HH:mm:ss'), dayjs(res.doNotDisturb.endTime, 'HH:mm:ss')];
        }
        formCreateRef.current?.setFieldsValue(res);

        // 如果是复制操作，在渲染前重新生成所有ID
        if (optType === 'copy' && res.nodes && res.edges) {
          // 创建旧ID到新ID的映射
          const idMapping = new Map();

          // 为所有节点生成新ID并更新映射
          res.nodes.forEach((node) => {
            const oldId = node.id;
            const newId = nanoid(10);
            idMapping.set(oldId, newId);
            node.id = newId;
          });

          // 更新所有边的源节点和目标节点ID
          res.edges.forEach((edge) => {
            edge.id = nanoid(10);
            if (idMapping.has(edge.sourceNodeId)) {
              edge.sourceNodeId = idMapping.get(edge.sourceNodeId);
            }
            if (idMapping.has(edge.targetNodeId)) {
              edge.targetNodeId = idMapping.get(edge.targetNodeId);
            }
          });

          // 更新节点的父子关系
          res.nodes.forEach((node) => {
            if (node.parentNodeId && idMapping.has(node.parentNodeId)) {
              node.parentNodeId = idMapping.get(node.parentNodeId);
            }
            if (node.type === 'splitFlow') {
              node.properties?.groups?.forEach((group) => {
                group.nextNodeId = idMapping.get(group.nextNodeId);
              });
            }
          });
        }

        const newParentChildrenMap = new Map();
        const newChildParentMap = new Map();

        res.nodes?.forEach((node) => {
          if (node.type === 'empty') {
            node.type = 'supply';
            node.properties.isEmptyNode = true;
          }
        });

        lfRef.current.render({
          nodes: res.nodes,
          edges: res.edges,
        });
        let triggerNodeInstance;
        let audienceNodeInstance;
        res.nodes?.forEach((node) => {
          if (node.type === 'trigger') {
            triggerNodeInstance = lfRef.current.getNodeModelById(node.id); // 获取节点实例
            root1Ref.current = triggerNodeInstance;
          }
          if (node.type === 'audience') {
            audienceNodeInstance = lfRef.current.getNodeModelById(node.id); // 获取节点实例
            root2Ref.current = audienceNodeInstance;
          }
          if (node.parentNodeId) {
            if (!newParentChildrenMap.has(node.parentNodeId)) {
              newParentChildrenMap.set(node.parentNodeId, []);
            }
            newParentChildrenMap.get(node.parentNodeId).push(node.id);
            newChildParentMap.set(node.id, node.parentNodeId);
          }
        });

        // 更新引用
        parentChildrenMap.current = newParentChildrenMap;
        childParentMap.current = newChildParentMap;
      // formStepsRef?.current?.forEach((formInstanceRef) => {
      //   formInstanceRef?.current?.setFieldsValue(formValue);
      // });
      });
    } else {
      // 计算画布坐标
      const x = (clientWidth / 2);
      root1Ref.current = lfRef.current.addNode({
        id: nanoid(10),
        type: 'trigger',
        x,
        y: 80,
        properties: {
          width: 300,
          name: '',
        },
      });
      root2Ref.current = lfRef.current.addNode({
        id: nanoid(10),
        type: 'audience',
        x,
        y: 330,
        properties: {
          width: 300,
        },
      });

      parentChildrenMap.current.set(root1Ref.current.id, [
        root2Ref.current.id,
      ]);
      childParentMap.current.set(root2Ref.current.id, root1Ref.current.id);
    }
    setTimeout(() => {
      lfRef.current.translateCenter();
    });

    return () => {
      message.destroy('copeymsg');
    };
  }, []);
  useEffect(() => {
    registerNode();
  }, [optType]);
  useEffect(() => {
    if (lfRef.current) {
    // 触发自定义事件通知所有节点
      lfRef.current.emit('custom:copy-node-change', curCopyNode);
    }
  }, [curCopyNode]);

  useEffect(() => {
    if (visibleStepsForm) {
      formTriggerRef?.current?.resetFields();
      formAudienceRef.current?.resetFields();
      const properties = root1Ref.current.getProperties();
      const properties2 = root2Ref.current.getProperties();
      if (properties.type || properties2.type) {
        formTriggerRef?.current?.setFieldsValue({
          trigger: {
            ...properties,
            executeStardEnd: [properties.startTime, properties.endTime],
          },
        });
        const form2 = {
          ...properties2,
          partnerCodesList: properties2?.partnerCodes?.['10000'] || [],
        };
        if (form2.type === 'userPackage') {
          form2.uploadTxt = [{
            response: {
              data: {
                fileId: form2?.audienceTag,
                fileName: form2?.fileName,
              },
            },
          }];
        }
        formAudienceRef.current?.setFieldsValue({
          audience: form2,
        });
      }
    }
  }, [visibleStepsForm]);
  useEffect(() => {
    if (isModalOpenSplit && nodeMode === 'detail') {
      formSplitRef.current?.setFieldsValue({
        ...curNode.getProperties(),
      });
    }
  }, [isModalOpenSplit]);
  useEffect(() => {
    if (isModalOpenSupply && nodeMode === 'detail') {
      const properties = curNode?.getProperties();
      if (properties) {
        formSupplyRef.current?.setFieldsValue({
          ...properties,
          // eslint-disable-next-line no-nested-ternary
          rewardConfig: readonly ? (properties.rewardConfig ? [properties.rewardConfig] : []) : ([properties.rewardConfig]),
        });
      }
    }
  }, [isModalOpenSupply]);
  useEffect(() => {
    if (curCopyNode) {
      message.info({
        key: 'copeymsg',
        content: (<Space>
          复制成功，请选择粘贴位置
          <Button type="link" onClick={() => { setCurCopyNode(null); message.destroy('copeymsg'); }}>取消复制</Button>
        </Space>),
        duration: 0,
      });
    }
  }, [curCopyNode]);
  function handleOkAddNode() {
    setIsModalOpenAddNode(false);
    if (addNodeType === 'splitFlow') {
      setIsModalOpenSplit(true);
    }
    if (addNodeType === 'supply') {
      setIsModalOpenSupply(true);
    }
    // addChildNode(addNodeParent.id, addNodeType);
  }
  function onSaveCanvasForm(type) {
    setSubmitLoading(true);
    formCreateRef.current?.validateFieldsReturnFormatValue?.().then(async (valuesForm) => {
      if (valuesForm?.doNotDisturb?.time) {
        valuesForm.doNotDisturb.startTime = valuesForm.doNotDisturb.time[0];
        valuesForm.doNotDisturb.endTime = valuesForm.doNotDisturb.time[1];
      }
      try {
        const graphData = lfRef.current.getGraphData();
        const ifHasBlankNode = graphData.nodes.some((node) => {
          if (node.properties.isBlankNode) {
            return true;
          }
          return false;
        });
        if (ifHasBlankNode) {
          notification.warning({
            message: '画布中有占位组件，不能保存',
          });
          return;
        }

        graphData.nodes.forEach((node) => {
          if (node.properties.isEmptyNode) {
            node.type = 'empty';
          }
          if (!node.name) {
            node.name = `unionname-${node.id}`;
          }
          if (node.type === 'supply') {
            if (node.properties.name) {
              node.name = node.properties.name;
            }
            if (node.properties?.rewardConfig && Object.keys(node.properties.rewardConfig).length === 0) {
              delete node.properties.rewardConfig;
            }
            if (node.properties.rewardConfig) {
              node.properties.rewardConfig.componentDetailId = nanoid(8);
            }
            if (node.properties.messageConfig && node.properties.messageConfig.length > 0) {
              node.properties.messageConfig.forEach((element) => {
                element.componentDetailId = nanoid(8);
              });
            }
          }
          node.parentNodeId = childParentMap.current.get(node.id);
        });
        const realParma = {
          ...valuesForm, ...graphData, id: canvasId, userId: userInfo.userId, userName: userInfo.userName,
        };
        if (optType === 'copy' || optType === 'add') {
          delete realParma.id;
        }
        if (type === 'save') {
          realParma.submitType = 1;
        }
        if (type === 'audit') {
          realParma.submitType = 2;
          realParma.configAuditCommand = { resourceUrl: '/index/loan-carpenter/FlowCanvasManagement/CanvasList?type=view&id=' };
        }
        const res = await saveCanvas(realParma);
        if (res?.code === '200') {
          message.success('保存成功');
          closeCanvas();
        }
      } finally {
        setSubmitLoading(false);
        message.destroy('copeymsg');
      }
    }).finally(() => {
      setSubmitLoading(false);
    });
  }
  // async function onAuditCanvas() {
  //   setAuditLoading(true);
  //   try {
  //     const res = await auditCanvas({ id: canvasDetail.id, configAuditCommand: { resourceUrl: `/index/loan-carpenter/FlowCanvasManagement/CanvasDetail/view/${canvasDetail.id}` } });
  //     if (res?.code === '200') {
  //       message.success('提交审核成功');
  //     }
  //   } finally {
  //     setAuditLoading(false);
  //   }
  // }
  const handleUserPackage = async (audience) => {
    const param = {
      fileId: audience?.audienceTag,
    };
    const res = await getUsePackageView(param);
    if (res.code === '200' && res.data) {
      setUserPackageData(res.data?.split(','));
    }
  };
  const getSplitFormData = (splitValue) => {
    if (curNode && curNode.type === 'splitFlow' && nodeMode === 'detail') {
      curNode.setProperties(splitValue);
    } else if (curNode && curNode.type === 'supply' && curNode.properties.isBlankNode) {
      const groups = [];
      const parentId = childParentMap.current.get(curNode.id);
      const parentNode = lfRef.current.getNodeModelById(parentId);
      const temppID = curNode.id;
      deleteNode(curNode.id);
      const tempNode = addChildNode(parentId, 'splitFlow');
      tempNode.setProperties(splitValue);
      parentNode.properties?.groups?.forEach((ite) => {
        if (ite.nextNodeId === temppID) {
          ite.nextNodeId = tempNode.id;
        }
      });
      const strategyParam = JSON.parse(splitValue.strategyParam).groupConfigItems;
      strategyParam.forEach((item) => {
        const tmpNode = addChildNode(tempNode.id);
        tmpNode.setProperties({ ...item, strategySelect: splitValue.strategySelect });
        groups.push({ group: item.group, nextNodeId: tmpNode.id });
      });
      tempNode.setProperties({ groups });
    } else {
      const groups = [];
      const tempNode = addChildNode(addNodeParent.id, addNodeType);
      tempNode.setProperties(splitValue);
      const strategyParam = JSON.parse(splitValue.strategyParam).groupConfigItems;
      strategyParam.forEach((item) => {
        const tmpNode = addChildNode(tempNode.id);
        tmpNode.setProperties({ ...item, strategySelect: splitValue.strategySelect });
        groups.push({ group: item.group, nextNodeId: tmpNode.id });
      });
      tempNode.setProperties({ groups });
    }
  };
  const getSupplyFormData = (supplyValue) => {
    if (curNode && curNode.type === 'supply' && curNode.properties.isBlankNode) {
      // const parentId = childParentMap.current.get(curNode.id);
      // deleteNode(curNode.id);
      // const tempNode = addChildNode(parentId, 'supply', false);
      const realProperties = {
        ...curNode.properties,
        ...supplyValue,
        isBlankNode: false,
      };
      curNode.setProperties(realProperties);
    } else if (curNode && curNode.type === 'supply' && nodeMode === 'detail') {
      curNode.setProperties({ ...supplyValue, isEmptyNode: false });
    } else {
      const tempNode = addChildNode(addNodeParent.id, addNodeType, false);
      tempNode.setProperties(supplyValue);
    }
  };
  return (
    <div className="canvas-root-wrap">
      <ProForm
        layout="inline"
        className="canvas-wrap-form"
        formRef={formCreateRef}
        readonly={readonly}
        onValuesChange={(changeValues) => {
          if (changeValues.businessType) {
            formCreateRef.current.setFieldsValue({ productType: '' });
          }
        }}
        submitter={{
          render: () => (readonly ? [
            !content && canvasDetail.configAuditDTO?.flowUrl && (
            <Button loading={submitLoading} target="_blank" style={{ marginTop: 10 }} type="link" href={canvasDetail.configAuditDTO?.flowUrl} key="subaudi2">
              查看审核工单
            </Button>
            ),
          ] : [
            <Button loading={submitLoading} style={{ marginTop: 10 }} type="primary" onClick={() => onSaveCanvasForm('save')} key="save">
              保存
            </Button>,
            <Button loading={submitLoading} style={{ marginTop: 10 }} type="primary" onClick={() => onSaveCanvasForm('audit')} key="subaudi">
              提交审核
            </Button>,
          ]),
        }}
      >
        <ProFormText
          width="md"
          name="name"
          label="流程名称"
          tooltip="最长为30位"
          placeholder="请输入名称，不超过30个字"
          fieldProps={{ maxLength: 30 }}
          rules={[{ required: true, message: '请输入名称' }]}
        />
        <ProFormSelect
          width="md"
          name="businessType"
          label="业务类型"
          request={async () => {
            const res = await getBusinessTypeList();
            return res.data.map((item) => ({ label: item, value: item }));
          }}
          fieldProps={{
            loading: productTypeLoading,
            disabled: productTypeLoading,
          }}
          rules={[{ required: true, message: '请选择业务类型' }]}
        />
        <ProFormSelect
          width="md"
          name="productType"
          label="细分产品供给"
          dependencies={['businessType']}
          request={async (params) => {
            const param = {
            };
            if (params.businessType) {
              param.businessType = params.businessType;
            }
            setProductTypeLoading(true);
            try {
              const res = await getProductTypeList(param);
              return res.data.map((item) => ({ label: item, value: item }));
            } finally {
              setProductTypeLoading(false);
            }
          }}
          rules={[{ required: true, message: '请选择细分产品供给' }]}
        />

        <ProFormSwitch
          width="md"
          name={['doNotDisturb', 'enable']}
          label="勿扰设置"
          fieldProps={{
            checkedChildren: '开启',
            unCheckedChildren: '关闭',
          }}
          convertValue={(value) => !!value}
          transform={(value) => (value ? 1 : 0)}
        />
        <ProFormDependency name={['doNotDisturb', 'enable']}>
          {({ doNotDisturb }) => {
            if (doNotDisturb?.enable) {
              return (
                <ProFormTimePicker.RangePicker
                  valueFormat="HH:mm:ss"
                  fieldProps={{
                    order: false,
                  }}
                  width="md"
                  name={['doNotDisturb', 'time']}
                  label="勿扰时间段"
                  rules={[{ required: true, message: '请选择勿扰时间段' }]}
                />
              );
            }
            return null;
          }}
        </ProFormDependency>
        <ProFormDependency name={['doNotDisturb', 'enable']}>
          {({ doNotDisturb }) => {
            if (doNotDisturb?.enable) {
              return (
                <ProFormSelect
                  width="md"
                  request={async () => [
                    { label: '丢弃', value: 0 },
                    { label: '勿扰结束后触达', value: 1 },
                  ]}
                  name={['doNotDisturb', 'strategy']}
                  label="勿扰期间内触达则"
                  rules={[{ required: true, message: '请选择勿扰期间内触达结果' }]}
                />
              );
            }
            return null;
          }}
        </ProFormDependency>
      </ProForm>
      <div className="canvas-wrap" ref={canvasListRef} />
      <Modal title="选择策略器" open={isModalOpenAddNode} onOk={() => handleOkAddNode()} onCancel={() => setIsModalOpenAddNode(false)}>
        <div className="tool-node-type" onClick={() => setAddNodeType('splitFlow')}>
          <div className={classNames('tool-node-type-inner', { active: addNodeType === 'splitFlow' })}>
            <div className="tool-node-type-inner-title">分流组件</div>
            <div className="tool-node-type-inner-text">对进入流程的用户，配置用户分流方式，包含随机分流实验、分人群条件进行分流</div>
          </div>
        </div>
        <div className="tool-node-type" onClick={() => setAddNodeType('supply')}>
          <div className={classNames('tool-node-type-inner', { active: addNodeType === 'supply' })}>
            <div className="tool-node-type-inner-title">供给组件</div>
            <div className="tool-node-type-inner-text">对进入流程的用户，配置对应的权益内容、触达方式等</div>
          </div>
        </div>
      </Modal>
      <StepsForm
        current={stepCurrent}
        formRef={formStepsRef}
        stepsFormRender={(dom, submitter) => (
          <Drawer
            className="steps-form-drawer"
            width={1200}
            onClose={() => setVisibleStepsForm(false)}
            open={visibleStepsForm}
            footer={submitter}
          >
            {dom}
          </Drawer>
        )}
        submitter={{
          render: (props, doms) => (
            <Space>
              {stepCurrent === 0 && (
                <Button
                  type="primary"
                  onClick={async () => {
                    const valid = await formTriggerRef.current?.validateFields();
                    if (valid) {
                      if (['timedSingleTrigger', 'timedRecurringTrigger'].includes(valid?.trigger?.type) && !readonly) {
                        formAudienceRef.current?.setFieldValue(['audience', 'type'], '');
                      }
                      setStepCurrent(1);
                    }
                  }}
                >
                  下一步
                </Button>
              )}
              {stepCurrent === 1 && (
                <Button
                  type="primary"
                  onClick={() => {
                    setStepCurrent(0);
                  }}
                >
                  上一步
                </Button>
              )}
              {stepCurrent === 1 && !readonly && (
                <Button
                  type="primary"
                  onClick={async () => {
                    const valid = await formAudienceRef.current?.validateFields();
                    if (valid) {
                      try {
                        await formTriggerRef.current?.validateFields();
                      } catch (error) {
                        setStepCurrent(0);
                        return;
                      }

                      setVisibleStepsForm(false);
                      // validateFieldsReturnFormatValue
                      const triggerRefValue = formTriggerRef.current.getFieldsFormatValue();
                      const audienceRefValue = formAudienceRef.current.getFieldsFormatValue();
                      const triggerType = triggerRefValue.trigger.type;
                      const executeTimeKey = `${triggerType}executeTime`;
                      root1Ref.current.setProperties({
                        ...triggerRefValue.trigger,
                        executeTime: triggerRefValue.trigger[executeTimeKey],
                      });
                      root2Ref.current.setProperties({
                        ...audienceRefValue.audience,
                        partnerCodes: {
                          10000: audienceRefValue.audience.partnerCodesList,
                        },
                      });
                      setStepCurrent(0);
                      formTriggerRef.current?.resetFields();
                      formAudienceRef.current?.resetFields();
                    }
                  }}
                >
                  保存
                </Button>
              )}
              {!readonly && (
                <Button
                  onClick={() => {
                    setVisibleStepsForm(false);
                    setStepCurrent(0);
                    formTriggerRef.current?.resetFields();
                    formAudienceRef.current?.resetFields();
                  }}
                >
                  取消
                </Button>
              )}
            </Space>
          )
          ,
        }}
      >
        <TriggerForm
          optType={optType}
          formRef={formTriggerRef}
          formAudienceRef={formAudienceRef}
          initialValues={{
            trigger: {
              // type: 'timedSingleTrigger',
              timeWindowType: 'month',
              frequencyControl: {
                type: 'custom',
              },
            },
          }}
          readonly={readonly}
          canvasDetail={canvasDetail}
        />

        <AudienceForm
          formRef={formAudienceRef}
          formTriggerRef={formTriggerRef}
          initialValues={{
            audience: {
              type: 'allUsers',
              dmpTagType: 'open_id',
              dmpTagTypeBottom: 'open_id',
            },
          }}
          readonly={readonly}
          canvasDetail={canvasDetail}
          handleUserPackage={handleUserPackage}
          userPackageData={userPackageData}
        />
      </StepsForm>
      <SplitForm
        formRef={formSplitRef}
        isModalOpenSplit={isModalOpenSplit}
        closeSplitForm={() => { formSplitRef.current?.resetFields(); setIsModalOpenSplit(false); }}
        getSplitFormData={getSplitFormData}
        readonly={readonly}
      />
      <SupplyForm
        formRef={formSupplyRef}
        getRoot1Ref={() => root1Ref.current}
        getRoot2Ref={() => root2Ref.current}
        isModalOpenSupply={isModalOpenSupply}
        closeSupplyForm={() => setIsModalOpenSupply(false)}
        getSupplyFormData={getSupplyFormData}
        readonly={readonly}
      />
    </div>
  );
};

export default CanvasDetail;
