import React from 'react';
import dayjs from 'dayjs';

import {
  ProFormRadio,
  ProFormDateTimePicker,
  ProFormDateTimeRangePicker,
  ProFormSelect,
  ProFormDigit,
  ProFormText,
  ProFormSwitch,
  ProFormDependency,
  ProFormTimePicker,
  StepsForm,
} from '@ant-design/pro-components';
import { Space } from 'antd';
import { weekMap, behaviorAList, behaviorBList } from '../constant';

const TriggerForm = ({ formRef, initialValues, optType }) => (
  <StepsForm.StepForm
    name="trigger"
    title="进入条件"
    formRef={formRef}
    initialValues={initialValues}
    onValuesChange={(changeValues) => {
      if (changeValues?.trigger?.timeWindowType === 'month' || changeValues?.trigger?.timeWindowType === 'week') {
        formRef.current.setFieldsValue({
          trigger: {
            executeDays: [],
          },
        });
      }
      if (changeValues?.trigger?.expireNotice === 0) {
        formRef.current.setFieldsValue({
          trigger: {
            remindUser: '',
          },
        });
      }
      if (changeValues?.trigger?.type === 'singleEventTrigger' || changeValues?.trigger?.type === 'sequentialEventTrigger') {
        formRef.current.setFieldsValue({
          trigger: {
            behaviorA: '',
          },
        });
      }
    }}
  >
    <ProFormRadio.Group
      disabled={optType === 'copy'}
      name={['trigger', 'type']}
      label="触达任务类型"
      radioType="button"
      fieldProps={{ block: true, size: 'large' }}
      options={[
        {
          label: '定时型-单次',
          value: 'timedSingleTrigger',
        },
        {
          label: '定时型-重复',
          value: 'timedRecurringTrigger',
        },
        {
          label: '单场景触发型',
          value: 'singleEventTrigger',
        },
        {
          label: '序列行为触发',
          value: 'sequentialEventTrigger',
        },
      ]}
      rules={[{ required: true, message: '请选择触达任务类型' }]}
    />
    <ProFormDependency name={['trigger', 'type']}>
      {({ trigger }) => {
        if (trigger?.type === 'timedSingleTrigger') {
          return (
            <>
              <div className="self-item-error-not-blank" style={{ marginBottom: 8 }}>触达时间</div>
              <Space align="center">
                <div className="self-item-in-canvas-form-24">在</div>
                <ProFormDateTimePicker
                  convertValue={(value) => (value ? dayjs(value).format('YYYY-MM-DD HH:mm:ss') : null)}
                  valueFormat="YYYY-MM-DD HH:mm:ss"
                  name={['trigger', 'timedSingleTriggerexecuteTime']}
                  rules={[{ required: true, message: '请选择触达时间' }]}
                />
                <div className="self-item-in-canvas-form-24">时对受众用户进行触达</div>
              </Space>
            </>
          );
        }
        return null;
      }}
    </ProFormDependency>
    <ProFormDependency
      name={[['trigger', 'type'], ['trigger', 'timeWindowType'], ['trigger', 'expireNotice'],
        ['trigger', 'frequencyControl', 'type'], ['trigger', 'endTime'], ['trigger', 'startTime'], ['trigger', 'executeStardEnd']]}
    >
      {({ trigger }) => {
        if (trigger?.type === 'timedRecurringTrigger') {
          return (
            <>
              <div className="self-item-error-not-blank" style={{ marginBottom: 8 }}>触达时间</div>
              <Space align="center">
                <div className="self-item-in-canvas-form-24">在</div>
                <ProFormDateTimeRangePicker
                  valueFormat="YYYY-MM-DD HH:mm:ss"
                  name={['trigger', 'executeStardEnd']}
                  transform={(value) => ({
                    trigger: {
                      startTime: value?.[0],
                      endTime: value?.[1],
                    },
                  })}
                  convertValue={(value) => {
                    if (!trigger?.startTime || !trigger?.endTime) return null;
                    return [
                      dayjs(trigger.startTime),
                      dayjs(trigger.endTime),
                    ];
                  }}
                  rules={[{ required: true, message: '请选择触达时间' }]}
                />
                <div className="self-item-in-canvas-form-24">期间内</div>
              </Space>
              <div>
                <Space align="center">
                  <div className="self-item-in-canvas-form-24">用户</div>
                  <ProFormSelect
                    name={['trigger', 'timeWindowType']}
                    request={async () => [
                      { label: '每月', value: 'month' },
                      { label: '每周', value: 'week' },
                      { label: '每天', value: 'day' },
                    ]}
                  />
                  {(trigger?.timeWindowType === 'month' || trigger?.timeWindowType === 'week') && (
                    <ProFormSelect
                      key={`${trigger?.timeWindowType}-excutedDays`}
                      name={['trigger', 'executeDays']}
                      fieldProps={{
                        mode: 'multiple',
                        style: {
                          width: '200px',
                        },
                      }}
                      request={async () => {
                        if (trigger?.timeWindowType === 'month') {
                          return Array.from({ length: 31 }, (_, i) => ({ label: `${i + 1}号`, value: i + 1 }));
                        }
                        if (trigger?.timeWindowType === 'week') {
                          return Array.from({ length: 7 }, (_, i) => ({ label: `星期${weekMap[i + 1]}`, value: i + 1 }));
                        }
                        return [];
                      }}
                      rules={[{ required: true, message: '该项必填' }]}
                    />
                  )}
                  <ProFormTimePicker valueFormat="HH:mm:ss" name={['trigger', 'timedRecurringTriggerexecuteTime']} rules={[{ required: true, message: '请选择触发时间' }]} />
                  <div className="self-item-in-canvas-form-24">自动触发</div>
                </Space>
              </div>
              <div>
                <Space align="center">
                  <div className="self-item-in-canvas-form-24">到期提醒</div>
                  <ProFormSwitch
                    name={['trigger', 'expireNotice']}
                    convertValue={(value) => {
                      if (typeof value === 'number') {
                        return value === 1;
                      }
                      return value;
                    }}
                    transform={(value) => (value ? 1 : 0)}
                  />
                  <ProFormText
                    name={['trigger', 'remindUser']}
                    placeholder="请输入提醒人工号（以,分隔）"
                    fieldProps={{
                      style: {
                        width: '300px',
                      },
                      disabled: !trigger.expireNotice,
                    }}
                    rules={trigger.expireNotice ? [{ required: trigger.expireNotice, message: '该项必填' }, {
                      pattern: /^\d+(,\d+)*$/,
                      message: '请输入提醒人工号（以,分隔）',
                    }] : []}
                  />
                </Space>
              </div>
              <div className="self-item-error-not-blank" style={{ marginBottom: 8 }}>参与限制</div>
              <ProFormRadio.Group
                name={['trigger', 'frequencyControl', 'type']}
                options={[
                  {
                    label: '仅能参与1次',
                    value: 'once',
                  },
                  {
                    label: '可参与多次',
                    value: 'custom',
                  },
                ]}
                fieldProps={{
                  style: {
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 8,
                  },
                }}
              />
              { trigger?.frequencyControl?.type === 'custom' && (
                <div>
                  <Space align="center">
                    <div className="self-item-in-canvas-form-24">同一用户</div>
                    <ProFormDigit
                      name={['trigger', 'frequencyControl', 'timeWindowDays']}
                      min={1}
                      max={90}
                      fieldProps={{ style: { width: '100px' } }}
                      placeholder="正整数"
                      rules={[{ required: true, message: '该项必填' }]}
                    />
                    <div className="self-item-in-canvas-form-24">天内，最多参与</div>
                    <ProFormDigit
                      name={['trigger', 'frequencyControl', 'timesInWindow']}
                      min={1}
                      fieldProps={{ style: { width: '100px' } }}
                      placeholder="正整数"
                      rules={[{ required: true, message: '该项必填' }]}
                    />
                    <div className="self-item-in-canvas-form-24">次</div>
                  </Space>
                </div>
              )}
            </>
          );
        }
        return null;
      }}
    </ProFormDependency>
    <ProFormDependency
      name={[['trigger', 'type'], ['trigger', 'behaviorA'], ['trigger', 'expireNotice'],
        ['trigger', 'frequencyControl', 'type'], ['trigger', 'endTime'], ['trigger', 'startTime'], ['trigger', 'executeStardEnd']]}
    >
      {({ trigger }) => {
        if (trigger?.type === 'singleEventTrigger') {
          return (
            <>
              <div className="self-item-error-not-blank" style={{ marginBottom: 8 }}>触达时间</div>
              <Space align="center">
                <div className="self-item-in-canvas-form-24">在</div>
                <ProFormDateTimeRangePicker
                  valueFormat="YYYY-MM-DD HH:mm:ss"
                  name={['trigger', 'executeStardEnd']}
                  transform={(value) => ({
                    trigger: {
                      startTime: value?.[0],
                      endTime: value?.[1],
                    },
                  })}
                  convertValue={(value) => {
                    if (!trigger?.startTime || !trigger?.endTime) return null;
                    return [
                      dayjs(trigger.startTime),
                      dayjs(trigger.endTime),
                    ];
                  }}
                  rules={[{ required: true, message: '请选择触达时间' }]}
                />
                <div className="self-item-in-canvas-form-24">期间内</div>
              </Space>
              <div>
                <Space align="center">
                  <div className="self-item-in-canvas-form-24">用户</div>
                  <ProFormSelect
                    key={`${trigger?.type}-behaviorA-${Date.now()}`}
                    name={['trigger', 'behaviorA']}
                    placeholder="请选择触发场景"
                    fieldProps={{
                      style: {
                        width: '200px',
                      },
                    }}
                    request={async () => [...behaviorAList, { label: '新设备激活', value: 'deviceActivation' }]}
                    rules={[{ required: true, message: '请选择触发场景' }]}
                  />
                  <div className="self-item-in-canvas-form-24">立即触发</div>
                </Space>
              </div>
              <div>
                <Space align="center">
                  <div className="self-item-in-canvas-form-24">到期提醒</div>
                  <ProFormSwitch
                    name={['trigger', 'expireNotice']}
                    convertValue={(value) => {
                      if (typeof value === 'number') {
                        return value === 1;
                      }
                      return value;
                    }}
                    transform={(value) => (value ? 1 : 0)}
                  />
                  <ProFormText
                    name={['trigger', 'remindUser']}
                    placeholder="请输入提醒人工号（以,分隔）"
                    fieldProps={{
                      style: {
                        width: '300px',
                      },
                      disabled: !trigger.expireNotice,
                    }}
                    rules={trigger.expireNotice ? [{ required: trigger.expireNotice, message: '该项必填' }, {
                      pattern: /^\d+(,\d+)*$/,
                      message: '请输入提醒人工号（以,分隔）',
                    }] : []}
                  />
                </Space>
              </div>
              <div className="self-item-error-not-blank" style={{ marginBottom: 8 }}>参与限制</div>
              <ProFormRadio.Group
                name={['trigger', 'frequencyControl', 'type']}
                options={[
                  {
                    label: '仅能参与1次',
                    value: 'once',
                  },
                  {
                    label: '可参与多次',
                    value: 'custom',
                  },
                ]}
                fieldProps={{
                  style: {
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 8,
                  },
                }}
              />
              { trigger?.frequencyControl?.type === 'custom' && (
                <div>
                  <Space align="center">
                    <div className="self-item-in-canvas-form-24">同一用户</div>
                    <ProFormDigit
                      name={['trigger', 'frequencyControl', 'timeWindowDays']}
                      min={1}
                      max={90}
                      fieldProps={{ style: { width: '100px' } }}
                      placeholder="正整数"
                      rules={[{ required: true, message: '该项必填' }]}
                    />
                    <div className="self-item-in-canvas-form-24">天内，最多参与</div>
                    <ProFormDigit
                      name={['trigger', 'frequencyControl', 'timesInWindow']}
                      min={1}
                      fieldProps={{ style: { width: '100px' } }}
                      placeholder="正整数"
                      rules={[{ required: true, message: '该项必填' }]}
                    />
                    <div className="self-item-in-canvas-form-24">次</div>
                  </Space>
                </div>
              )}
            </>
          );
        }
        return null;
      }}
    </ProFormDependency>
    <ProFormDependency
      name={[['trigger', 'type'], ['trigger', 'behaviorA'], ['trigger', 'expireNotice'], ['trigger', 'frequencyControl', 'type'],
        ['trigger', 'endTime'], ['trigger', 'startTime'], ['trigger', 'executeStardEnd']]}
    >
      {({ trigger }) => {
        if (trigger?.type === 'sequentialEventTrigger') {
          return (
            <>
              <div className="self-item-error-not-blank" style={{ marginBottom: 8 }}>触达时间</div>
              <Space align="center">
                <div className="self-item-in-canvas-form-24">在</div>
                <ProFormDateTimeRangePicker
                  valueFormat="YYYY-MM-DD HH:mm:ss"
                  name={['trigger', 'executeStardEnd']}
                  transform={(value) => ({
                    trigger: {
                      startTime: value?.[0],
                      endTime: value?.[1],
                    },
                  })}
                  convertValue={(value) => {
                    if (!trigger?.startTime || !trigger?.endTime) return null;
                    return [
                      dayjs(trigger.startTime),
                      dayjs(trigger.endTime),
                    ];
                  }}
                  rules={[{ required: true, message: '请选择触达时间' }]}
                />
                <div className="self-item-in-canvas-form-24">期间内</div>
              </Space>
              <div>
                <Space align="center">
                  <div className="self-item-in-canvas-form-24">用户完成</div>
                  <ProFormSelect
                    key={`${trigger?.type}-behaviorA-${Date.now()}`}
                    name={['trigger', 'behaviorA']}
                    placeholder="请选择触发场景"
                    fieldProps={{
                      style: {
                        width: '200px',
                      },
                    }}
                    request={async () => behaviorAList}
                    rules={[{ required: true, message: '请选择触发场景' }]}
                  />
                  <div className="self-item-in-canvas-form-24">且在</div>
                  <ProFormDigit name={['trigger', 'timeWindow']} min={1} fieldProps={{ style: { width: '100px' } }} placeholder="正整数" rules={[{ required: true, message: '该项必填' }]} />
                  <div className="self-item-in-canvas-form-24">天内</div>
                  <ProFormSelect
                    name={['trigger', 'behaviorRelation']}
                    placeholder="请选择触发场景"
                    fieldProps={{
                      style: {
                        width: '200px',
                      },
                    }}
                    request={async () => [
                      // {
                      //   label: '已完成',
                      //   value: 'finish',
                      // },
                      {
                        label: '未完成',
                        value: 'notFinish',
                      },
                    ]}
                    rules={[{ required: true, message: '请选择触发场景' }]}
                  />
                  <ProFormSelect
                    name={['trigger', 'behaviorB']}
                    placeholder="请选择触发场景"
                    fieldProps={{
                      style: {
                        width: '200px',
                      },
                    }}
                    request={async () => behaviorBList}
                    rules={[{ required: true, message: '请选择触发场景' }]}
                  />
                </Space>
              </div>
              <div>
                <Space align="center">
                  <div className="self-item-in-canvas-form-24">到期提醒</div>
                  <ProFormSwitch
                    name={['trigger', 'expireNotice']}
                    convertValue={(value) => {
                      if (typeof value === 'number') {
                        return value === 1;
                      }
                      return value;
                    }}
                    transform={(value) => (value ? 1 : 0)}
                  />
                  <ProFormText
                    name={['trigger', 'remindUser']}
                    placeholder="请输入提醒人工号（以,分隔）"
                    fieldProps={{
                      style: {
                        width: '300px',
                      },
                      disabled: !trigger.expireNotice,
                    }}
                    rules={trigger.expireNotice ? [{ required: trigger.expireNotice, message: '该项必填' }, {
                      pattern: /^\d+(,\d+)*$/,
                      message: '请输入提醒人工号（以,分隔）',
                    }] : []}
                  />
                </Space>
              </div>
              <div className="self-item-error-not-blank" style={{ marginBottom: 8 }}>参与限制</div>
              <ProFormRadio.Group
                name={['trigger', 'frequencyControl', 'type']}
                options={[
                  {
                    label: '仅能参与1次',
                    value: 'once',
                  },
                  {
                    label: '可参与多次',
                    value: 'custom',
                  },
                ]}
                fieldProps={{
                  style: {
                    display: 'flex',
                    flexDirection: 'column',
                    gap: 8,
                  },
                }}
              />
              { trigger?.frequencyControl?.type === 'custom' && (
                <div>
                  <Space align="center">
                    <div className="self-item-in-canvas-form-24">同一用户</div>
                    <ProFormDigit
                      name={['trigger', 'frequencyControl', 'timeWindowDays']}
                      min={1}
                      max={90}
                      fieldProps={{ style: { width: '100px' } }}
                      placeholder="正整数"
                      rules={[{ required: true, message: '该项必填' }]}
                    />
                    <div className="self-item-in-canvas-form-24">天内，最多参与</div>
                    <ProFormDigit
                      name={['trigger', 'frequencyControl', 'timesInWindow']}
                      min={1}
                      fieldProps={{ style: { width: '100px' } }}
                      placeholder="正整数"
                      rules={[{ required: true, message: '该项必填' }]}
                    />
                    <div className="self-item-in-canvas-form-24">次</div>
                  </Space>
                </div>
              )}
            </>
          );
        }
        return null;
      }}
    </ProFormDependency>
  </StepsForm.StepForm>
);

export default TriggerForm;
