import React from 'react';
import {
  ProFormText,
  ProFormRadio,
  ProFormSelect,
  ProFormCheckbox,
  ProFormUploadButton,
  ProFormDependency,
  StepsForm,
  ModalForm,
} from '@ant-design/pro-components';
import {
  Space, Button, Row,
} from 'antd';
import { downloadFile } from '@/utils/util';
import {
  getFundChannel,
} from '@/services/canvas';
import { getDmpTag } from '@/services/common';

const AudienceForm = ({
  formRef, initialValues, readonly, canvasDetail, handleUserPackage, userPackageData, formTriggerRef,
}) => (
  <StepsForm.StepForm
    name="audience"
    title="受众用户"
    formRef={formRef}
    initialValues={initialValues}
    onValuesChange={(changeValues) => {
      if (changeValues?.audience?.type === 'dmpTagUser') {
        formRef.current.setFieldsValue({
          audience: {
            audienceTag: '',
            fileName: '',
          },
        });
      }
      if (changeValues?.audience?.type === 'userPackage') {
        formRef.current.setFieldsValue({
          audience: {
            partnerCodesList: [],
            audienceTag: '',
            fileName: '',
            dmpTagTypeBottom: 'open_id',
          },
        });
      }
      if (changeValues?.audience?.dmpTagType) {
        formRef.current.setFieldsValue({
          audience: {
            audienceTag: '',
            dmpFilterUserTags: [],
            dmpTagTypeBottom: changeValues?.audience?.dmpTagType,
          },
        });
      }
    }}
  >
    <>
      <div className="self-item-error-not-blank" style={{ marginBottom: 8, fontSize: 16, fontWeight: 800 }}>
        受众用户
      </div>
      <ProFormRadio.Group
        name={['audience', 'type']}
        options={[
          {
            label: '不限制',
            value: 'allUsers',
            disabled: ['timedSingleTrigger', 'timedRecurringTrigger'].includes(formTriggerRef?.current?.getFieldValue('trigger').type),
          },
          {
            label: '限制dmp人群',
            value: 'dmpTagUser',
          },
          {
            label: '按人群包',
            value: 'userPackage',
          },
          {
            label: '按资金方',
            value: 'fundingChannel',
            disabled: ['timedSingleTrigger', 'timedRecurringTrigger'].includes(formTriggerRef?.current?.getFieldValue('trigger').type),
          },
        ]}
        fieldProps={{
          style: {
            display: 'flex',
            flexDirection: 'column',
            gap: 8,
          },
        }}
        rules={[{ required: true, message: '请选择受众用户' }]}
      />
      <ProFormText
        name={['audience', 'fileName']}
        hidden
        noStyle
      />
      <ProFormDependency name={[['audience', 'type'], ['audience', 'dmpTagType'],
        ['audience', 'dmpFilterFlag'], ['audience', 'audienceTag'], ['audience', 'fileName'], ['audience', 'partnerCodesList'], ['audience', 'uploadTxt']]}
      >
        {({ audience }) => {
          if (audience.type === 'dmpTagUser') {
            return (
              <Space>
                <ProFormSelect
                  name={['audience', 'dmpTagType']}
                  fieldProps={{ style: { width: '200px' }, disabled: ['sequentialEventTrigger'].includes(formTriggerRef?.current?.getFieldValue('trigger').type) }}
                  options={[{ label: 'IMEI维度', value: 'imei' }, { label: 'OPENID维度', value: 'open_id' }]}
                />
                <ProFormSelect
                  key={`audienceTag-select-${audience?.dmpTagType}`}
                  name={['audience', 'audienceTag']}
                  placeholder="请选择DMP人群包"
                  fieldProps={{
                    style: {
                      width: '200px',
                    },
                    showSearch: true,
                    debounceTime: 300,
                    onSelect: async (value, option) => {
                      formRef.current.setFieldsValue({
                        audience: {
                          fileName: option.textLabel,
                        },
                      });
                    },
                  }}
                  request={async ({ keyWords }) => {
                    const params = keyWords ? { tagName: keyWords } : { };
                    // tagId: canvasDetail?.audience.audienceTag
                    if (canvasDetail?.audience?.audienceTag) {
                      delete params.tagName;
                      params.tagId = canvasDetail?.audience.audienceTag;
                    }
                    const res = await getDmpTag({ ...params, pageNumber: 1, pageSize: 20 });
                    return res.data?.filter((item) => item.dimension === (audience?.dmpTagType === 'open_id' ? 2 : 1)).map((item) => ({
                      label: item.tagIdWithName,
                      value: String(item.id),
                      textLabel: item.tagIdWithName,
                      dimension: item.dimension,
                    }));
                  }}
                  rules={[{ required: true, message: '请选择DMP人群包' }]}
                />
              </Space>
            );
          }
          if (audience.type === 'userPackage') {
            return (
              <ProFormUploadButton
                rules={[{ required: true }]}
                label="上传文件(txt)"
                name={['audience', 'uploadTxt']}
                title="上传文件"
                max={1} // 限制只上传一个文件
                      // eslint-disable-next-line no-undef
                action={`${API_BASE_URL}/coupon/campaign/upload/back-up`}
                accept=".txt"
                listType="text"
                fieldProps={{
                  withCredentials: true,
                }}
                convertValue={(value) => {
                  if (audience?.audienceTag) {
                    return [{
                      name: audience?.fileName,
                      status: 'done',
                      // eslint-disable-next-line no-undef
                      url: `${API_BASE_URL}/user/file/download?fileId=${audience?.audienceTag}`,
                    }];
                  }
                  return null;
                }}
                transform={(value) => {
                  if (value?.[0]?.response) {
                    const { data: { fileId, fileName } } = value?.[0]?.response;
                    return {
                      audience: {
                        audienceTag: fileId,
                        fileName,
                      },
                    };
                  }
                  return {
                    audience: {
                      audienceTag: '',
                      fileName: '',
                    },
                  };
                }}
                disabled={readonly}
                extra={(
                      readonly && (
                        <Space>
                          <ModalForm
                            trigger={(
                              <Button type="link" disabled={false} onClick={() => handleUserPackage(audience)}>
                                查看预览
                              </Button>
                            )}
                            title="人群包配置"
                            modalProps={{
                              destroyOnClose: true,
                              centered: true,
                            }}
                            submitter={false}
                          >
                            <div
                              style={{
                                height: 300,
                                width: '100%',
                                overflow: 'scroll',
                              }}
                            >
                              {userPackageData.map((i) => (
                                <Row>{i}</Row>
                              ))}
                            </div>
                          </ModalForm>
                          <Button
                            type="link"
                            disabled={false}
                            onClick={() => {
                              downloadFile(canvasDetail?.audience?.audienceTag);
                            }}
                          >
                            下载文件
                          </Button>
                        </Space>
                      )
                    )}
              />
            );
          }
          if (audience.type === 'fundingChannel') {
            return (
              <ProFormCheckbox.Group
                name={['audience', 'partnerCodesList']}
                layout="horizontal"
                fieldProps={{
                  style: {
                    width: '600px',
                  },
                }}
                // convertValue={(value) => (Array.isArray(audience?.partnerCodes?.['10000'])
                //   ? audience?.partnerCodes?.['10000']
                //   : [])}
                // transform={(value) => {
                //   console.log(value, 100000000);
                //   return {
                //     audience: {
                //       partnerCodes: {
                //         10000: value || [],
                //       },
                //     },
                //   };
                // }}
                request={async () => {
                  const res = await getFundChannel({ productCode: '10000' });
                  return res.data?.map((item) => ({
                    label: item.displayName,
                    value: item.partnerCode,
                  })) || [];
                }}
              />
            );
          }
          return null;
        }}
      </ProFormDependency>
      <ProFormDependency name={[['audience', 'type'], ['audience', 'dmpTagType'], ['audience', 'businessConditions']]}>
        {({ audience }) => {
          if (readonly && !audience?.businessConditions) {
            return null;
          }
          const disabled1 = ['timedSingleTrigger', 'timedRecurringTrigger'].includes(formTriggerRef?.current?.getFieldValue('trigger').type)
            && audience?.type === 'dmpTagUser' && audience?.dmpTagType === 'imei';
          const disabled2 = ['timedSingleTrigger', 'timedRecurringTrigger'].includes(formTriggerRef?.current?.getFieldValue('trigger').type);
          return (
            <>
              <div style={{ marginBottom: 8, fontSize: 16, fontWeight: 800 }}>业务条件限制（仅限openID维度生效）</div>
              <ProFormCheckbox.Group
                name={['audience', 'businessConditions']}
                layout="vertical"
                fieldProps={{
                  style: {
                    width: '600px',
                  },
                  // disabled: audience?.dmpTagType === 'imei',
                }}
                options={[
                  { label: '需校验用户维度可授信或可借款', value: 'userCanCreditOrLoanCondition', disabled: disabled1 },
                  // { label: '需校验用户维度可借款', value: 'userCanLoanCondition', disabled: disabled1 },
                  {
                    label: '需校验场景指定合作方维度可借款',
                    value: 'partnerCanLoanCondition',
                    disabled: disabled1 || disabled2,
                  },
                ]}
              />
            </>
          );
        }}
      </ProFormDependency>
      <ProFormDependency name={[['audience', 'dmpTagType'], ['audience', 'woodpeckerRejectFilter'], ['audience', 'dmpFilterFlag']]}>
        {({ audience }) => {
          if (readonly && !audience?.woodpeckerRejectFilter && !audience?.dmpFilterFlag) {
            return null;
          }
          return (
            <>
              <div style={{ marginBottom: 8, fontSize: 16, fontWeight: 800 }}>过滤用户</div>
              {readonly ? null : (
                <Space>
                  <ProFormCheckbox
                    name={['audience', 'woodpeckerRejectFilter']}
                    convertValue={(value) => {
                      if (typeof value === 'number') {
                        return value === 1;
                      }
                      return value;
                    }}
                    transform={(value) => ({ audience: { woodpeckerRejectFilter: value ? 1 : 0 } })}
                  />
                  <div className="self-item-in-canvas-form-24">客服营销拒绝用户</div>
                </Space>
              )}
              <br />
              {readonly ? null : (
                <Space style={{ position: 'relative', top: '-32px' }}>
                  <ProFormCheckbox
                    name={['audience', 'dmpFilterFlag']}
                    convertValue={(value) => {
                      if (typeof value === 'number') {
                        return value === 1;
                      }
                      return value;
                    }}
                    transform={(value) => ({ audience: { dmpFilterFlag: value ? 1 : 0 } })}
                  />
                  <div className="self-item-in-canvas-form-24" style={{ marginRight: 10 }}>dmp过滤用户人群</div>
                </Space>
              )}
            </>
          );
        }}
      </ProFormDependency>
      <ProFormDependency name={[['audience', 'dmpFilterFlag'], ['audience', 'dmpTagType'], ['audience', 'dmpTagTypeBottom']]}>
        {({ audience }) => {
          if (audience?.dmpFilterFlag) {
            return (
              <Space style={{ position: 'relative', top: '-32px' }}>
                {audience.dmpFilterFlag && (
                <ProFormSelect
                  name={['audience', 'dmpTagTypeBottom']}
                  options={[{ label: 'IMEI维度', value: 'imei' }, { label: 'OPENID维度', value: 'open_id' }]}
                  disabled={!!audience?.dmpTagType}
                />
                )}
                <ProFormSelect
                  key={`dmpFilterUserTags-select-${audience?.dmpTagTypeBottom}`}
                  name={['audience', 'dmpFilterUserTags']}
                  placeholder="请选择DMP人群包"
                  fieldProps={{
                    style: {
                      width: '200px',
                    },
                    showSearch: true,
                    debounceTime: 300,
                    mode: 'multiple',
                    onSelect: (value, option) => {
                      console.log(value, option);
                    },
                  }}
                  request={async ({ keyWords }) => {
                    const params = keyWords ? { tagName: keyWords } : { };
                    // tagId: canvasDetail?.audience.audienceTag
                    // if (canvasDetail?.audience?.audienceTag) {
                    //   delete params.tagName;
                    //   params.tagId = canvasDetail?.audience.audienceTag;
                    // }
                    // pageNumber: 1,pageSize: 2000,
                    const res = await getDmpTag({ ...params, pageNumber: 1, pageSize: 20 });
                    return res.data?.filter((item) => item.dimension === (audience?.dmpTagTypeBottom === 'open_id' ? 2 : 1)).map((item) => ({
                      label: item.tagIdWithName,
                      value: String(item.id),
                    }));
                  }}
                  rules={[{ required: true, message: '请选择DMP人群包' }]}
                />
              </Space>
            );
          }
          return null;
        }}
      </ProFormDependency>
    </>
  </StepsForm.StepForm>
);

export default AudienceForm;
