import React, { useRef, useState, useEffect } from 'react';
import { PlusOutlined, DeleteTwoTone, CopyOutlined } from '@ant-design/icons';
import AudienceNode from './audienceNode';
import TriggerNode from './triggerNode';
import SplitFlowNode from './splitFlowNode';
import SupplyNode from './supplyNode';

const mapCom = {
  audience: AudienceNode,
  trigger: TriggerNode,
  splitFlow: SplitFlowNode,
  supply: SupplyNode,
};

const App = ({
  node, type, onConditionMount, onClickAddNode, onClickDelNode, onClickNode, onAddCelue, autoLayoutFuction, onClickCopyNode, optType, onClickPasteNode, lf,
}) => {
  const RealNode = mapCom[type];
  const data = node.getData();
  const containerRef = useRef(null);
  const [showIcon, setShowIcon] = useState(false);
  const [curCopyNodeTemp, setCurCopyNodeTemp] = useState(null);
  console.log(data, node, 999999);

  if (!data.properties) data.properties = {};
  const properties = data.properties;
  const ifCanAdd = () => {
    const typeBtn = ['audience', 'supply'].includes(type);
    if (optType === 'view') return false;
    let flag = false;
    if (type === 'audience') {
      if (properties.type) {
        flag = true;
      }
    } else if (type === 'supply') {
      if (!properties.isBlankNode) {
        flag = true;
      }
    }
    return typeBtn && flag;
  };
  const ifCanDel = () => {
    if (optType === 'view') return false;
    return ['splitFlow', 'supply'].includes(type);
  };
  const ifCanCopy = () => {
    if (optType === 'view') return false;
    return ['supply'].includes(type) && !properties.isBlankNode;
  };
  const ifCanPaste = () => {
    if (optType === 'view') return false;
    return ['supply'].includes(type) && (!node.outgoing || node?.outgoing?.edges?.length === 0);
  };
  const { isBlankNode, isEmptyNode } = properties;
  useEffect(() => {
    const { height } = containerRef.current.getBoundingClientRect();
    node.setProperties({
      height,
    });
    setTimeout(() => {
      onConditionMount?.();
    });

    const handleCopyNodeChange = (cpnode) => {
      // 这里可以触发组件更新逻辑
      setCurCopyNodeTemp(cpnode);
    };

    lf.on('custom:copy-node-change', handleCopyNodeChange);

    return () => {
      lf.off('custom:copy-node-change', handleCopyNodeChange);
    };
  }, []);
  useEffect(() => {
    const { height } = containerRef.current.getBoundingClientRect();
    node.setProperties({
      height,
    });
    autoLayoutFuction?.();
  }, [isBlankNode, isEmptyNode]);
  useEffect(() => {
    const { height } = containerRef.current.getBoundingClientRect();
    const currentHeight = node.properties?.height;
    if (currentHeight === height) return;
    node.setProperties({
      height,
    });
    autoLayoutFuction?.();
  }, [properties]);

  return (
    <div
      ref={containerRef}
      style={{ cursor: 'pointer' }}
      className={`custom-node-wrap ${data.properties.visible === 2 ? 'hidenode' : ''}`}
      onMouseEnter={() => setShowIcon(true)}
      onMouseLeave={() => setShowIcon(false)}
      onClick={() => {
        if (curCopyNodeTemp) return;
        onClickNode?.(node);
      }}
    >
      <div className="custom-node-content">
        <RealNode
          node={node}
          type={type}
          setEmptyNode={() => node.setProperties({ isEmptyNode: true, isBlankNode: false })}
          addCelue={() => {
            onAddCelue?.(node);
          }}
        />
      </div>
      {ifCanPaste() && curCopyNodeTemp ? (
        <div
          className="custom-node-can-copy"
        >
          <span
            className="paste-btn"
            onClick={(e) => {
              e.stopPropagation();
              onClickPasteNode?.(node, curCopyNodeTemp);
            }}
          >
            粘贴
          </span>
        </div>
      ) : null}
      {showIcon && ifCanAdd() && !curCopyNodeTemp ? (
        <div
          className="custom-node-icon-plus"
          onClick={(e) => {
            e.stopPropagation();
            onClickAddNode?.(node);
          }}
        >
          <PlusOutlined />
        </div>
      ) : null}
      <div className="custom-node-op-wrap">
        {ifCanDel() && showIcon && !curCopyNodeTemp ? (
          <div className="custom-node-icon-del" onClick={(e) => { e.stopPropagation(); onClickDelNode?.(node); }}>
            <DeleteTwoTone />
          </div>
        ) : null}
        {ifCanCopy() && showIcon && !curCopyNodeTemp ? (
          <div className="custom-node-icon-copy" onClick={(e) => { e.stopPropagation(); onClickCopyNode?.(node); }}>
            <CopyOutlined />
          </div>
        ) : null}
      </div>

    </div>
  );
};

export default App;
