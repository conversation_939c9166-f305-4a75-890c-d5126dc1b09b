import React from 'react';
import { weekMap } from '../constant';

const behaviorAMap = {
  partnerCreditSuccess: '合作方维度授信成功',
  allPaidOff: '结清全部在贷',
  increaseAmount: '提额成功',
  temporaryIncreaseAmount: '临时提额成功',
  loanSuccess: '借款成功',
  creditSuccess: '用户维度授信成功',
  deviceActivation: '新设备激活',
};

const behaviorBMap = {
  loanSuccess: '借款成功',
  loanSubmit: '借款申请',
};

const mapType = {
  singleEventTrigger: '单场景触发型',
  sequentialEventTrigger: '序列行为触发',
  timedRecurringTrigger: '定时型-重复',
  timedSingleTrigger: '定时型-单次',
};
const monthType = {
  month: '月',
  day: '日',
  week: '周',
};
const App = ({ node }) => {
  const properties = node.getProperties();

  return (
    <div style={{ width: 240 }} className="trigger-wrap">
      <div className="trigger-title">进入条件</div>
      <div className="trigger-content">
        <div className="trigger-content-inner">
          <p>
            <span className="titlebold">触达任务类型: </span>
            {mapType[properties.type] || '待设置'}
          </p>
          <p style={{ paddingTop: 6 }}>
            <span className="titlebold">触达时间: </span>
            {(() => {
              const windoType = monthType[properties.timeWindowType];
              let typeDesc = '';
              if (properties.timeWindowType === 'month') {
                typeDesc = properties.executeDays.map((item) => `${item}日`).join('、');
              } else if (properties.timeWindowType === 'week') {
                typeDesc = properties.executeDays.map((item) => `周${weekMap[item]}`).join('、');
              }
              switch (properties.type) {
                case 'singleEventTrigger':
                  return `${properties.startTime}-${properties.endTime}, 用户完成${behaviorAMap[properties.behaviorA]}后自动触发`;
                case 'sequentialEventTrigger':
                  return `${properties.startTime}-${properties.endTime}, 用户完成${behaviorAMap[properties.behaviorA]}且在${properties.timeWindow}
                  内${properties.behaviorRelation === 'finish' ? '完成' : '未完成'}${behaviorBMap[properties.behaviorB]}后触发`;
                case 'timedRecurringTrigger':
                  return `${properties.startTime}-${properties.endTime}, 用户每${windoType}${typeDesc}${properties.executeTime} 自动触发`;
                case 'timedSingleTrigger':
                  return properties.executeTime;
                default:
                  return '待设置';
              }
            })()}
          </p>
        </div>
      </div>
    </div>
  );
};

export default App;
