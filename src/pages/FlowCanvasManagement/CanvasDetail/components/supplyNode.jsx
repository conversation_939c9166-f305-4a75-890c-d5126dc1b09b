import React from 'react';
import {
  PlusOutlined,
  BorderlessTableOutlined,
} from '@ant-design/icons';
import { strategySelectMap } from '../constant';

const rewardMap = {
  interestCoupon: '利息优惠券',
};
const messageMap = {
  smartSms: '富媒体短信',
  push: 'push',
  sms: '短信',
};

const App = (datap) => {
  const properties = datap?.node?.getProperties();
  if (properties.isBlankNode) {
    return (
      <div style={{ width: 240, height: 140 }} className="empty-wrap">
        {'group' in properties ? (
          <div className="top-desc">
            {'group' in properties
              ? `分组${properties.group}：${strategySelectMap[properties.strategySelect]?.text}-${properties.basis}` : ''}
          </div>
        ) : null}
        <div className="empty-title">
          <PlusOutlined />
          <span onClick={(e) => { e.stopPropagation(); datap?.addCelue?.(); }}>添加策略器</span>
        </div>
        <div className="empty-title">
          <BorderlessTableOutlined />
          <span onClick={(e) => { e.stopPropagation(); datap?.setEmptyNode?.(); }}>一键设为空白组</span>
        </div>
      </div>
    );
  }
  if (properties.isEmptyNode && !properties.name) {
    return (
      <div style={{ width: 240 }} className="supply-wrap">
        {'group' in properties ? (
          <div className="top-desc">
            {'group' in properties
              ? `分组${properties.group}：${strategySelectMap[properties.strategySelect]?.text}-${properties.basis}` : ''}
          </div>
        ) : null}
        <div className="top-content">
          <div className="top-title">空白组</div>
          <div className="reward-content item">
            <div className="leftBorder" />
            <div className="reward-content-inner">
              <div className="reward-content-inner-title">
                权益内容
              </div>
              <div className="reward-content-inner-list">
                无
              </div>
            </div>
          </div>
          <div className="message-content item">
            <div className="leftBorder" />
            <div className="message-content-inner">
              <div className="message-content-inner-title">
                触达消息
              </div>
              <div className="message-content-inner-list">
                无
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }
  return (
    <div style={{ width: 240 }} className="supply-wrap">
      {'group' in properties ? <div className="top-desc">{'group' in properties ? `分组${properties.group}：${strategySelectMap[properties.strategySelect]?.text}-${properties.basis}` : ''}</div> : null}
      <div className="top-content">
        <div className="top-title">{properties.name}</div>
        <div className="reward-content item">
          <div className="leftBorder" />
          <div className="reward-content-inner">
            <div className="reward-content-inner-title">
              权益内容
            </div>
            <div className="reward-content-inner-list">
              <div className="reward-content-inner-list-item">
                <div className="reward-content-inner-list-item-top">
                  权益内容：
                  {rewardMap[properties?.rewardConfig?.rewardType] || '待设置'}
                </div>
                {properties?.rewardConfig?.templateIdWithName
                  ? (
                    <div className="reward-content-inner-list-item-top">
                      {properties?.rewardConfig?.templateIdWithName}
                      {`-自发放后${properties?.rewardConfig?.validDays}天内有效`}
                    </div>
                  )
                  : null}
                <div className="reward-content-inner-list-item-bottm">
                  {properties?.rewardConfig?.delayConfig?.enable === 1 ? '延迟' : '立即'}
                  {properties?.rewardConfig?.delayConfig?.enable === 1 ? `${properties?.rewardConfig?.delayConfig?.delayDays}天` : ''}
                  发放
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="message-content item">
          <div className="leftBorder" />
          <div className="message-content-inner">
            <div className="message-content-inner-title">
              触达消息
            </div>
            <div className="message-content-inner-list">
              {properties.messageConfig?.length > 0 ? (
                properties.messageConfig.map((item, index) => (
                  <div className="message-content-inner-list-item">
                    <div className="message-content-inner-list-item-top">{`触达消息${index + 1}：${messageMap[item.messageType]}`}</div>
                    <div className="message-content-inner-list-item-bottm">
                      {item.delayConfig?.enable === 1 ? '延迟' : '立即'}
                      {item.delayConfig?.enable === 1 ? `${item.delayConfig?.delayDays}天` : ''}
                      发放
                      {item.tplTitle ? `-${item.tplTitle}` : ''}
                    </div>
                  </div>
                ))
              ) : '无'}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default App;
