import React, {
  useRef, useState, useEffect, useMemo,
} from 'react';
import {
  ProFormText,
  ProFormSelect,
  DrawerForm,
  ProForm,
  ProFormList,
  ProFormDependency,
  ProFormDigit,
  ProFormTimePicker,
  ProFormCheckbox,
} from '@ant-design/pro-components';
import { PlusSquareFilled, MinusSquareFilled } from '@ant-design/icons';
import {
  Space, Form, Spin, Button, notification, Collapse, Table, Image, Row, Flex, Checkbox,
} from 'antd';
import {
  getNotifyScene, getSMSTemplate, getSmartMessageTemplate,
} from '@/services/canvas';
import {
  getQuotaId,
  getTemplateBrief,
} from '@/services/common.js';
import DebounceSelect from '@/components/DebounceSelect';
import nanoid from 'nano-id';
import debounce from 'lodash/debounce';

const placeholderMap = {
  push: '消息中心场景模板',
  sms: '短信中心模板',
  smartSms: '短信中心富媒体短信模板',
};
const idMap = {
  push: 'sceneId',
  sms: 'smsTemplateId',
  smartSms: 'smsTemplateId',
};

const SupplyForm = ({
  formRef, isModalOpenSupply, closeSupplyForm, getSupplyFormData, readonly, getRoot1Ref, getRoot2Ref,
}) => {
  const [form] = Form.useForm();
  const [tableDataMap, setTableDataMap] = useState([]); // 改为数组存储每个子项的表格数据
  const [tableColumnsMap, setTableColumnsMap] = useState([]); // 改为数组存储每个子项的表格列定义
  const [loadingMap, setLoadingMap] = useState({});
  const actionRef = useRef();
  const actionRef2 = useRef();
  const [rewardConfigVersion, setRewardConfigVersion] = useState(0);
  window.SupplyForm = form;
  const onQueryTemplate = (id, messageType, index) => {
    setLoadingMap((prev) => ({
      ...prev,
      [index]: true,
    }));
    const realId = form.getFieldValue('messageConfig')[index][id];
    const columnsSms = [
      { title: '模板编码', dataIndex: 'tempCode', key: 'tempCode' },
      { title: '模板名称', dataIndex: 'title', key: 'title' },
      { title: '模版内容', dataIndex: 'template', key: 'template' },
      { title: '发送通道分类', dataIndex: 'sendChannelCategory', key: 'sendChannelCategory' },
      { title: '发送通道类型', dataIndex: 'sendChannelType', key: 'sendChannelType' },
      { title: '预算项目', dataIndex: 'budgetProject', key: 'budgetProject' },
      { title: '模版归属项目', dataIndex: 'projectName', key: 'projectName' },
      { title: '模版备注', dataIndex: 'memo', key: 'memo' },
    ];
    const columnsPush = [
      {
        title: '策略类型', dataIndex: 'id', key: 'id', render: (text, record) => (record.strategyTypeDesc),
      },
      {
        title: '通道类型', dataIndex: 'name', key: 'name', render: (text, record) => (record.notifyType),
      },
      {
        title: '模板类型', dataIndex: 'createdAt', key: 'createdAt', render: (text, record) => (record.messageTemplateContent?.templateType),
      },
      {
        title: '模板名称', dataIndex: 'id', key: 'id1', render: (text, record) => (record.messageTemplateContent?.templateName),
      },
      {
        title: '消息分类', dataIndex: 'name', key: 'name1', render: (text, record) => (record.messageTemplateContent?.msgTypeCodeName),
      },
      {
        title: '消息标题', dataIndex: 'createdAt', key: 'createdAt1', render: (text, record) => (record.messageTemplateContent?.msgTitle),
      },
      {
        title: '消息内容', dataIndex: 'id', key: 'id2', render: (text, record) => (record.messageTemplateContent?.msgContent),
      },
      {
        title: '跳转类型', dataIndex: 'name', key: 'name2', render: (text, record) => (record.messageTemplateContent?.skipType),
      },
      {
        title: '跳转链接', dataIndex: 'createdAt', key: 'createdAt2', render: (text, record) => (record.messageTemplateContent?.skipUrl),
      },
    ];
    const columnsSmart = [
      { title: '短信签名', dataIndex: 'signature', key: 'signature' },
      { title: '消息类型', dataIndex: 'type', key: 'type' },
      { title: '主标题', dataIndex: 'title', key: 'title' },
      { title: '子标题', dataIndex: 'subtitle', key: 'subtitle' },
      {
        title: '图片', dataIndex: 'img', key: 'img', render: (text, record) => (record.img ? <Image src={record.img} alt="" width={50} /> : null),
      },
      { title: '摘要', dataIndex: 'content', key: 'content' },
      { title: '跳转页面', dataIndex: 'taskStatus', key: 'taskStatus' },
      { title: '跳转链接', dataIndex: 'deepLink', key: 'deepLink' },
      { title: '预算项目', dataIndex: 'budgetProjectName', key: 'budgetProjectName' },
    ];
    // eslint-disable-next-line no-nested-ternary
    const columns = messageType === 'sms' ? columnsSms : messageType === 'push' ? columnsPush : columnsSmart;
    let pendingResult;
    if (messageType === 'sms') {
      pendingResult = getSMSTemplate({ smsTemplateCode: realId });
    } else if (messageType === 'push') {
      pendingResult = getNotifyScene({ sceneId: realId });
    } else {
      pendingResult = getSmartMessageTemplate({ templateCode: realId });
    }

    setTableColumnsMap((prev) => {
      const newColumns = [...prev];
      newColumns[index] = columns;
      return newColumns;
    });
    pendingResult.then((res) => {
      if (res?.code === '200') {
        setTableDataMap((prev) => {
          const newData = [...prev];
          newData[index] = messageType === 'sms' || messageType === 'smartSms' ? [res.data] : res.data;
          return newData;
        });
      }
    }).finally(() => {
      setLoadingMap((prev) => ({
        ...prev,
        [index]: false,
      }));
    });
  };
  const debouncedSearch = useMemo(
    () => debounce((value, type, index) => onQueryTemplate(value, type, index), 500),
    [onQueryTemplate], // 依赖项根据实际情况调整
  );
  const fetchQuotaList = (param) => {
    console.log(param, 'fetchQuotaList');
    // if (param) {
    return getQuotaId({
      type: 'cashCoupon',
      quotaIdOrName: param || '',
    }).then((res) => (res?.data
      ? res.data.map((item) => ({
        label: item.quotaIdWithName,
        value: item.id,
      }))
      : []));
    // }
    // return new Promise((resolve) => {
    //   resolve([]);
    // });
  };
  const getTemplateIdList = (param, addParam) => {
    console.log(param, addParam, 'getTemplateIdList');
    if (param) {
      return getTemplateBrief({
        id: param,
        rewardType: 4,
      }).then((res) => (res?.data
        ? [
          {
            label: res.data.templateName,
            value: res.data.id,
          },
        ]
        : []));
    }
    return new Promise((resolve) => {
      resolve([]);
    });
  };
  function getsmsallListWhencome() {
    setTimeout(() => {
      const messageList = form.getFieldValue('messageConfig');
      if (messageList?.length > 0) {
        messageList.forEach((item, index) => {
          if (item[idMap[item.messageType]]) {
            onQueryTemplate(idMap[item.messageType], item.messageType, index);
          }
        });
      }
    });
  }
  useEffect(() => {
    if (!isModalOpenSupply) {
      return;
    }
    getsmsallListWhencome();
    return () => {
    };
  }, [isModalOpenSupply]);

  const items = [
    {
      key: '1',
      label: '权益内容',
      children: <div style={{ position: 'relative' }}>
        {readonly ? null : (
          <Space style={{
            position: 'absolute', right: 0, fontSize: '25px', zIndex: 999,
          }}
          >
            <PlusSquareFilled onClick={() => {
              const tmpList = actionRef.current?.getList();
              if (tmpList?.length === 1) {
                notification.warning({
                  description: '权益组件最多配置一个',
                  message: '提示',
                });
                return;
              }
              actionRef.current?.add();
              setRewardConfigVersion((prev) => prev + 1);
            }}
            />
            <MinusSquareFilled
              onClick={() => {
                actionRef.current?.remove(0);
                setRewardConfigVersion((prev) => prev + 1);
              }}
            />
          </Space>
        )}
        <ProFormList
          name="rewardConfig"
          copyIconProps={false}
          deleteIconProps={false}
          creatorButtonProps={false}
          actionRef={actionRef}
        >
          {(f, index, action) => {
            const currentRowData = action.getCurrentRowData();
            const root2Data = getRoot2Ref().getProperties() || {};
            const root1Data = getRoot1Ref().getProperties() || {};
            const disabledCheck = (type) => {
              const disabledCheck1 = ['timedSingleTrigger', 'timedRecurringTrigger'].includes(root1Data?.type) && (root2Data?.type === 'dmpTagUser' && root2Data?.dmpTagType === 'imei');
              const disabledCheck2 = ['timedSingleTrigger', 'timedRecurringTrigger'].includes(root1Data?.type)
              && (['allUsers', 'userPackage', 'fundingChannel'].includes(root2Data?.type)
              || (root2Data?.type === 'dmpTagUser' && root2Data?.dmpTagType === 'open_id'));
              const disabledCheck3 = ['timedSingleTrigger', 'timedRecurringTrigger'].includes(root1Data?.type);
              if (type === 'canSendCouponCondition') {
                return disabledCheck1;
              }
              if (type === 'userCanCreditOrLoanCondition') {
                return disabledCheck1;
              }
              if (type === 'partnerCanLoanCondition') {
                return disabledCheck1 || disabledCheck2;
              }
              if (type === 'userNotLoanApplyCondition') {
                return disabledCheck3 || disabledCheck1;
              }
              if (type === 'partnerNotLoanApplyCondition') {
                return disabledCheck1 || disabledCheck2;
              }
            };
            return (
              <div>
                {/* <h3 style={{ marginBottom: 24 }}>权益内容</h3> */}
                <Space>
                  <div className="self-item-in-canvas-form-24">延时设置：满足条件则</div>
                  <ProFormSelect
                    name={['delayConfig', 'enable']}
                    options={[{ label: '延迟', value: 1 }, { label: '立即', value: 0 }]}
                    rules={[{ required: true, message: '请选择' }]}
                  />
                  <ProFormDependency name={['delayConfig', 'enable']}>
                    {({ delayConfig }) => {
                      if (delayConfig?.enable === 1) {
                        return (
                          <Space>
                            <ProFormDigit
                              placeholder="正整数"
                              name={['delayConfig', 'delayDays']}
                              min={1}
                              fieldProps={{ precision: 0 }}
                              rules={[{ required: true, message: '请选择触发时间' }]}
                            />
                            <div className="self-item-in-canvas-form-24">天</div>
                            <ProFormTimePicker
                              name={['delayConfig', 'executeTime']}
                              fieldProps={{
                                format: 'HH:mm:ss',
                                onChange: (value, timeString) => {
                                  form.setFieldValue(['rewardConfig', index, 'delayConfig', 'executeTime'], timeString);
                                },
                              }}
                              rules={[{ required: true, message: '请选择' }]}
                            />
                          </Space>
                        );
                      }
                      return null;
                    }}
                  </ProFormDependency>
                  <div className="self-item-in-canvas-form-24">发送</div>
                </Space>
                <ProFormCheckbox.Group
                  name={['businessConditions']}
                  label="执行前校验"
                  options={[
                    { label: '校验可发券', value: 'canSendCouponCondition', disabled: disabledCheck('canSendCouponCondition') },
                    { label: '用户维度可授信或可借款', value: 'userCanCreditOrLoanCondition', disabled: disabledCheck('userCanCreditOrLoanCondition') },
                    { label: '场景指定合作方维度可借款', value: 'partnerCanLoanCondition', disabled: disabledCheck('partnerCanLoanCondition') },
                    { label: '进入流程至今未申请借款（用户维度）', value: 'userNotLoanApplyCondition', disabled: disabledCheck('userNotLoanApplyCondition') },
                    { label: '进入流程至今未申请借款（场景指定合作方）', value: 'partnerNotLoanApplyCondition', disabled: disabledCheck('partnerNotLoanApplyCondition') }]}
                />
                <div style={{
                  marginTop: '-25px',
                  marginBottom: '18px',
                  fontSize: '12px',
                  color: '#706b6b',
                }}
                >
                  仅限openID维度生效，至今判断、指定合作方判断仅限单场景触发、序列行为触发可配
                </div>
                <ProFormSelect
                  label="权益类型"
                  name={['rewardType']}
                  options={[{ label: '利息优惠券', value: 'interestCoupon' }]}
                  rules={[{ required: true, message: '请选择' }]}
                />
                <ProForm.Item
                  name="quotaId"
                  label="红包库存ID"
                  rules={[{ required: true, message: '请输入关联红包库存ID' }]}
                >
                  <DebounceSelect
                    showSearch
                    clearOnAddParamChange
                    addParam={{
                      initialValue: currentRowData?.quotaId || '',
                    }}
                    fetchOptions={fetchQuotaList}
                    allowClear
                    disabled={readonly}
                  />
                </ProForm.Item>
                <ProForm.Item
                  name="templateId"
                  label="关联券规则ID"
                  rules={[{ required: true, message: '这是必填项' }]}
                >
                  <DebounceSelect
                    showSearch
                    clearOnAddParamChange
                    addParam={{
                      initialValue: currentRowData?.templateId || '',
                    }}
                    fetchOptions={getTemplateIdList}
                    setOptionByAddParam
                    allowClear
                    disabled={readonly}
                    onSelect={(value, option) => {
                      console.log(value, 'value', option);
                      form.setFieldValue(['rewardConfig', index, 'templateIdWithName'], `${option.value}-${option.label}`);
                    }}
                  />
                </ProForm.Item>
                <ProFormDigit
                  placeholder="填正整数"
                  name={['maxCount']}
                  label="上限发放总张数"
                  min={1}
                  fieldProps={{ precision: 0 }}
                  rules={[{ required: true, message: '请输入' }]}
                />
                <Space>
                  <div className="self-item-in-canvas-form-24">用户使用有效期：自发放后</div>
                  <ProFormDigit
                    placeholder="正整数"
                    name={['validDays']}
                    min={1}
                    fieldProps={{ precision: 0 }}
                    rules={[{ required: true, message: '请选择触发时间' }]}
                  />
                  <div className="self-item-in-canvas-form-24">天内有效</div>
                </Space>
              </div>
            );
          }}
        </ProFormList>
      </div>,
    },
    {
      key: '2',
      label: '触达消息',
      children: <ProForm.Group style={{ width: '100%', position: 'relative' }}>
        {readonly ? null : (
          <Space style={{
            position: 'absolute', right: 0, fontSize: '25px', zIndex: 999,
          }}
          >
            <PlusSquareFilled onClick={() => {
              actionRef2?.current?.add({
                delayConfig: {
                },
                businessConditions: [],
                messageType: 'sms',
                pushTemplateId: '',
                smsTemplateId: '',
              }, 1);
            }}
            />
            <MinusSquareFilled
              onClick={() => {
                actionRef2?.current?.remove(0);
                setTableDataMap((prev) => {
                  const newData = [...prev];
                  newData.splice(0, 1);
                  return newData;
                });
                setTableColumnsMap((prev) => {
                  const newColumns = [...prev];
                  newColumns.splice(0, 1);
                  return newColumns;
                });
              }}
            />
          </Space>
        )}
        <ProFormList
          name="messageConfig"
          key={`messageConfig-${rewardConfigVersion}`}
          copyIconProps={false}
          deleteIconProps={false}
          creatorButtonProps={false}
          className="supply-form-formlist-wrap"
          style={{ width: '100%' }}
          actionRef={actionRef2}
        >
          {(f, index, action) => {
            const root1Data = getRoot1Ref().getProperties() || {};
            const root2Data = getRoot2Ref().getProperties() || {};
            const curLoading = loadingMap[index] || false;
            const currentTableData = tableDataMap[index] || []; // 获取当前子项的表格数据
            const currentTableColumns = tableColumnsMap[index] || []; // 获取当前子项的表格列定义
            const rewList = actionRef?.current?.getList();
            const descFordealy = rewList?.length >= 1 ? '权益发放成功' : '满足条件';
            const disabledCheck = (type) => {
              const disabledCheck1 = ['timedSingleTrigger', 'timedRecurringTrigger'].includes(root1Data?.type) && (root2Data?.type === 'dmpTagUser' && root2Data?.dmpTagType === 'imei');
              const disabledCheck2 = ['timedSingleTrigger', 'timedRecurringTrigger'].includes(root1Data?.type)
              && (['allUsers', 'userPackage', 'fundingChannel'].includes(root2Data?.type)
              || (root2Data?.type === 'dmpTagUser' && root2Data?.dmpTagType === 'open_id'));
              const disabledCheck3 = ['timedSingleTrigger', 'timedRecurringTrigger'].includes(root1Data?.type);
              if (type === 'canSendCouponCondition') {
                return disabledCheck1;
              }
              if (type === 'userCanCreditOrLoanCondition') {
                return disabledCheck1;
              }
              if (type === 'partnerCanLoanCondition') {
                return disabledCheck1 || disabledCheck2;
              }
              if (type === 'userNotLoanApplyCondition') {
                return disabledCheck3 || disabledCheck1;
              }
              if (type === 'partnerNotLoanApplyCondition') {
                return disabledCheck1 || disabledCheck2;
              }
            };
            return (
              <div style={{ position: 'relative' }}>
                {index > 0 && !readonly && (
                <Space style={{
                  position: 'absolute', right: 0, fontSize: '25px', zIndex: 999,
                }}
                >
                  <PlusSquareFilled onClick={() => {
                    actionRef2?.current?.add({
                      delayConfig: {
                      },
                      businessConditions: [],
                      messageType: 'sms', // sms（普通文本短信）、push、smartSms（富媒体短信）
                      pushTemplateId: '',
                      smsTemplateId: '',
                    }, index + 1);
                  }}
                  />
                  <MinusSquareFilled
                    onClick={() => {
                      actionRef2?.current?.remove(index);
                      // 使用数组的 splice 方法删除元素
                      setTableDataMap((prev) => {
                        const newData = [...prev];
                        newData.splice(index, 1);
                        return newData;
                      });
                      setTableColumnsMap((prev) => {
                        const newColumns = [...prev];
                        newColumns.splice(index, 1);
                        return newColumns;
                      });
                    }}
                  />
                </Space>
                )}
                <h3 style={{ marginBottom: 24, marginTop: index === 0 ? 0 : 24 }}>{`触达消息${index + 1}`}</h3>
                <Space>
                  <div className="self-item-in-canvas-form-24">{`延时设置：${descFordealy}则`}</div>
                  <ProFormSelect
                    name={['delayConfig', 'enable']}
                    options={[{ label: '延迟', value: 1 }, { label: '立即', value: 0 }]}
                    rules={[{ required: true, message: '请选择' }]}
                  />
                  <ProFormDependency name={['delayConfig', 'enable']}>
                    {({ delayConfig }) => {
                      if (delayConfig?.enable === 1) {
                        return (
                          <Space>
                            <ProFormDigit
                              placeholder="正整数"
                              name={['delayConfig', 'delayDays']}
                              min={1}
                              fieldProps={{ precision: 0 }}
                              rules={[{ required: true, message: '请选择触发时间' }]}
                            />
                            <div className="self-item-in-canvas-form-24">天</div>
                            <ProFormTimePicker
                              name={['delayConfig', 'executeTime']}
                              fieldProps={{
                                format: 'HH:mm:ss',
                                onChange: (value, timeString) => {
                                  form.setFieldValue(['messageConfig', index, 'delayConfig', 'executeTime'], timeString);
                                },
                              }}
                              rules={[{ required: true, message: '请选择' }]}
                            />
                          </Space>
                        );
                      }
                      return null;
                    }}
                  </ProFormDependency>
                  <div className="self-item-in-canvas-form-24">发送</div>
                </Space>
                <ProForm.Item
                  name={['businessConditions']}
                  label="执行前校验"
                >
                  <Checkbox.Group>
                    <Flex style={{ marginTop: '5px' }} className="supply-form-checkbox-group-businesscondition">
                      <Row style={{ width: '130px' }}>金融贷款</Row>
                      <Row style={{ marginTop: '-4px' }}>
                        <Checkbox value="userCanCreditOrLoanCondition" disabled={disabledCheck('userCanCreditOrLoanCondition')}>用户维度可授信或可借款</Checkbox>
                        <Checkbox value="partnerCanLoanCondition" disabled={disabledCheck('partnerCanLoanCondition')}>场景指定合作方维度可借款</Checkbox>
                        <Checkbox value="userNotLoanApplyCondition" disabled={disabledCheck('userNotLoanApplyCondition')}>进入流程至今未申请借款（用户维度）</Checkbox>
                        <Checkbox value="partnerNotLoanApplyCondition" disabled={disabledCheck('partnerNotLoanApplyCondition')}>进入流程至今未申请借款（场景指定合作方）</Checkbox>
                        <div style={{
                          marginBottom: '18px',
                          fontSize: '12px',
                          color: '#706b6b',
                        }}
                        >
                          仅限openID维度生效，至今判断、指定合作方判断仅限单场景触发、序列行为触发可配
                        </div>
                      </Row>
                    </Flex>
                    <Flex style={{ marginTop: '5px' }} className="supply-form-checkbox-group-businesscondition">
                      <Row style={{ width: '62px' }}>保险</Row>
                      <Row style={{ marginTop: '-4px' }}>
                        <Checkbox value="userNotBuySpbCondition">校验可购买碎屏险</Checkbox>
                        <Checkbox value="userNotBuyVivoCareCondition">校验可购买vivoCare+</Checkbox>
                        <div style={{
                          width: '100%',
                          marginBottom: '18px',
                          fontSize: '12px',
                          color: '#706b6b',
                        }}
                        >
                          imei维度
                        </div>
                      </Row>
                    </Flex>
                  </Checkbox.Group>
                </ProForm.Item>
                <ProFormSelect
                  label="触达类型"
                  name={['messageType']}
                  options={[{ label: 'sms（普通文本短信）', value: 'sms' }, { label: 'push', value: 'push' }, { label: 'smartSms（富媒体短信）', value: 'smartSms' }]}
                  rules={[{ required: true, message: '请选择' }]}
                />
                <ProFormDependency name={['messageType']}>
                  {({ messageType }) => {
                    console.log(messageType, currentTableData, tableDataMap, index);
                    return (
                      <>
                        <Space>
                          <div className="self-item-in-canvas-form-24">模板ID：</div>
                          <ProFormText
                            placeholder={`请输入${placeholderMap[messageType]}ID`}
                            name={idMap[messageType]}
                            rules={[{ required: true, message: '请选择' }]}
                            fieldProps={{
                              onChange: (e) => {
                                debouncedSearch(idMap[messageType], messageType, index);
                              },
                            }}
                          />
                          {!readonly && (<div style={{ height: 56 }}><Button type="primary" onClick={() => onQueryTemplate(idMap[messageType], messageType, index)}>查询</Button></div>)}
                          {/* {messageType !== 'push' && <ProFormSwitch name="isSchedule" label="是否调度" />} */}
                        </Space>
                        <Spin spinning={curLoading}>
                          {messageType === 'push' && (
                            <Space>
                              <span>
                                应用id：
                                {currentTableData.sceneId}
                              </span>
                              <span>
                                场景名称：
                                {currentTableData.sceneName}
                              </span>
                              <span>
                                消息一级分类：
                                {currentTableData.primaryTypeCodeName}
                              </span>
                              <span>
                                消息二级分类：
                                {currentTableData.secondTypeCodeName}
                              </span>
                              <span>
                                卡片标识：
                                <Image
                                  alt="icon"
                                  src={currentTableData.iconUrl}
                                  width={30}
                                  style={{
                                    display: 'inline-block',
                                    verticalAlign: 'middle',
                                  }}
                                />
                              </span>
                            </Space>
                          )}
                          <Table
                            columns={currentTableColumns} // 使用当前子项的列定义
                            dataSource={messageType === 'push' ? currentTableData.strategyList || [] : currentTableData} // 使用当前子项的数据源
                            pagination={false}
                            scroll={{ x: 'max-content' }}
                          />
                        </Spin>
                      </>

                    );
                  }}
                </ProFormDependency>
              </div>
            );
          }}
        </ProFormList>
      </ProForm.Group>,
    },
  ];
  return (
    <>
      <DrawerForm
        className="supply-form-drawer"
        layout="horizontal"
        formRef={formRef}
        open={isModalOpenSupply}
        title="供给组件"
        resize={{
          maxWidth: window.innerWidth * 0.8,
          minWidth: 900,
        }}
        form={form}
        readonly={readonly}
        drawerProps={{
          destroyOnClose: true,
          onClose: () => closeSupplyForm(),
          afterOpenChange: (open) => {
            if (!open) {
              setTableDataMap([]); // 改为空数组
              setTableColumnsMap([]); // 改为空数组
            }
          },
        }}
        submitTimeout={500}
        initialValues={
          {
            name: '',
            rewardConfig: [{
              delayConfig: {
              },
              businessConditions: [],
              rewardType: 'interestCoupon',
              quotaId: '',
              templateId: '',
              maxCount: 100,
              validType: '',
              validDays: '',
              pushTemplateId: '',
              smsTemplateId: '',
            }],
            messageConfig: [{
              delayConfig: {
              },
              businessConditions: [],
              messageType: 'sms', // sms（普通文本短信）、push、smartSms（富媒体短信）
              pushTemplateId: '',
              smsTemplateId: '',
              smsFunctionType: '',
              // isSchedule: 0,
            }],
          }
        }
        onFinish={async (oval) => {
          const values = { ...oval };
          if (values.rewardConfig.length > 0) {
            values.rewardConfig = values.rewardConfig[0];
            // if (values.rewardConfig) {
            //   values.rewardConfig.componentDetailId = nanoid(8);
            // }
          } else {
            values.rewardConfig = {};
          }
          values.messageConfig.forEach((element, index) => {
            // element.componentDetailId = nanoid(8);
            if (tableDataMap[index]?.[0]) {
              if (element.messageType === 'sms') {
                element.smsFunctionType = tableDataMap[index][0].functionType;
                element.tplTitle = tableDataMap[index][0].title;
              }
              if (element.messageType === 'push') {
                element.tplTitle = tableDataMap[index][0].sceneName;
                delete element.smsFunctionType;
              }
              if (element.messageType === 'smartSms') {
                element.taskId = tableDataMap[index][0].taskId;
                element.tplTitle = tableDataMap[index][0].title;
                delete element.smsFunctionType;
              }
            }
          });
          getSupplyFormData({ ...values });
          closeSupplyForm();
        }}
        submitter={!readonly}
      >
        <ProFormText
          name="name"
          label="供给组件名称"
          placeholder="请输入名称，不超过30个字"
          fieldProps={{ maxLength: 30 }}
          rules={[{ required: true, message: '请填写供给组件名称' }]}
        />
        <Collapse items={items} defaultActiveKey={['1', '2']} forceRender />
      </DrawerForm>
    </>

  );
};

export default SupplyForm;
