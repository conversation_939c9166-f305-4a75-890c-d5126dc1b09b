import React, { useState, useEffect } from 'react';
import {
  getFundChannel,
} from '@/services/canvas';
import { audienceTypeMap } from '../constant';

const App = ({ node }) => {
  const properties = node.getProperties();
  const [partnerNames, setPartnerNames] = useState([]);
  useEffect(() => {
    if (properties.type === 'fundingChannel') {
      const tempList = [];
      getFundChannel({ productCode: '10000' }).then((res) => {
        res.data.forEach((element) => {
          if (properties?.partnerCodes?.[10000]?.includes(element.partnerCode)) {
            tempList.push(element.displayName);
          }
        });
        setPartnerNames(tempList);
      });
    }
  }, [properties.type]);
  return (
    <div style={{ width: 240 }} className="audience-wrap">
      <div className="audience-title">受众用户</div>
      <div className="audience-content">
        <div className="audience-content-inner">
          <p>
            <span className="titlebold">
              {audienceTypeMap[properties.type]}
              {!properties.type ? '待设置' : ''}
              {properties.type && properties.type !== 'allUsers' ? ': ' : ''}
            </span>
            <span>
              {(properties.type === 'dmpTagUser' || properties.type === 'userPackage') && properties.fileName}
            </span>
            <span>
              {properties.type === 'fundingChannel' && partnerNames.join('、')}
            </span>
          </p>
        </div>
      </div>
    </div>
  );
};

export default App;
