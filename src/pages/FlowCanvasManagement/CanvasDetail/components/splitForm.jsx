import React, {
  useState, useEffect, useMemo,
} from 'react';
import {
  ProFormText,
  DrawerForm,
  ProForm,
} from '@ant-design/pro-components';
import {
  Space, Form, Spin, Button, Modal, Col, Row, notification,
} from 'antd';
import { experimentView } from '@/services/canvas';
import debounce from 'lodash/debounce';

import { strategySelectMap, strategySubSelectMap } from '../constant';

const SplitForm = ({
  formRef, isModalOpenSplit, closeSplitForm, getSplitFormData, readonly,
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [open, setOpen] = useState(false);
  const [experimentData, setExperimentData] = useState({});
  const [initialCode, setInitialCode] = useState('');
  const onSearch = async (value) => {
    setLoading(true);
    try {
      const res = await experimentView({
        experimentCode: value,
      });
      setExperimentData(res?.data || {});
    } catch (e) {
      setExperimentData({});
    } finally {
      setLoading(false);
    }
  };

  const debouncedSearch = useMemo(
    () => debounce((value) => onSearch(value), 500),
    [onSearch], // 依赖项根据实际情况调整
  );

  useEffect(() => {
    setTimeout(() => {
      const experimentCode = form.getFieldValue('experimentCode');
      if (experimentCode) {
      // 如果有 id，则发起接口请求
        debouncedSearch(experimentCode);
        setInitialCode(experimentCode);
      } else {
        setExperimentData({}); // 如果没有 id，则清空状态
        setInitialCode('');
      }
    });
  }, [isModalOpenSplit]);

  return (
    <>
      <DrawerForm
        formRef={formRef}
        open={isModalOpenSplit}
        title="分流组件"
        resize={{
          maxWidth: window.innerWidth * 0.8,
          minWidth: 600,
        }}
        form={form}
        readonly={readonly}
        drawerProps={{
          destroyOnClose: true,
          onClose: () => closeSplitForm(),
        }}
        submitTimeout={500}
        onFinish={async (values) => {
          if (readonly) {
            closeSplitForm();
            return;
          }
          if (!experimentData.code) {
            notification.error({
              description: '请选择正确的实验',
              message: '操作异常',
            });
            return false;
          }
          if (experimentData.code && experimentData.status !== 4) {
            notification.error({
              description: '请选择已上线的实验',
              message: '操作异常',
            });
            return false;
          }

          // 判断code是否变化
          if (initialCode && initialCode !== values.experimentCode) {
            notification.error({
              description: '分流组件不支持重新选择实验，请在画布中删除分流节点后重新添加配置新实验，请注意，删除操作会删除分流节点下的所有子节点',
              message: '操作异常',
            });
            return false;
          }

          console.log(experimentData, 'experimentData');
          getSplitFormData({
            experimentCode: experimentData.code, strategySelect: experimentData?.strategySelect, strategySubSelect: experimentData?.strategySubSelect, strategyParam: experimentData?.strategyParam,
          });
          closeSplitForm();
        }}
        // submitter={experimentData?.status === 4}
      >
        <Spin spinning={loading}>
          <ProForm.Group>
            <ProFormText
              name="experimentCode"
              placeholder="填实验平台code，自动带出分组信息"
              rules={[
                {
                  required: true,
                  message: '请输入实验code',
                },
              ]}
              fieldProps={{
                onChange: (e) => debouncedSearch(e.target.value),
                style: {
                  width: 300,
                },
              }}
            />
            <Button onClick={() => setOpen(true)}>查看详情</Button>
          </ProForm.Group>
          <ProForm.Group>
            <Space>
              <span>
                实验名称：
                {experimentData.name}
              </span>
              <span>
                实验时间：
                {experimentData.onlineTime}
              </span>
            </Space>
          </ProForm.Group>
          <ProForm.Group>
            <Space>
              <span>
                分流策略：
                {strategySelectMap[experimentData?.strategySelect]?.text}
              </span>
              <span>
                分组key：
                {JSON.parse(experimentData?.strategyParam || '{}').groupingKey}
              </span>
            </Space>
          </ProForm.Group>
          <h3 style={{ marginTop: 20 }}>分组信息</h3>
          <div>
            <span>默认分组：</span>
            <span>{experimentData?.expireDefaultGroup}</span>
          </div>
          {experimentData?.strategySubSelect && (
            <div>
              <span>条件设置：</span>
              <span>{strategySubSelectMap[experimentData?.strategySubSelect]}</span>
            </div>
          )}
          {JSON.parse(experimentData?.strategyParam || '{}').groupConfigItems?.map((item) => (
            <div>
              <span>{item.group === 0 ? '对照组：' : `实验组${item.group}：`}</span>
              <span>{item.basis}</span>
            </div>
          ))}
        </Spin>
      </DrawerForm>
      <Modal
        open={open}
        onCancel={() => setOpen(false)}
        footer={[
          <Button key="submit" type="primary" onClick={() => setOpen(false)}>
            好的
          </Button>,
        ]}
        width={1000}
      >
        <>
          <h3 style={{ marginTop: 20 }}>配置详情</h3>
          <Row gutter={16}>
            <Col span={8}>
              实验id：
              {experimentData.id}
            </Col>
            <Col span={8}>
              实验code：
              {experimentData.code}
            </Col>
            <Col span={8}>
              实验名：
              {experimentData.name}
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              实验描述：
              {experimentData.experimentDesc}
            </Col>
            <Col span={8}>
              业务方：
              {experimentData.businessCode}
            </Col>
            <Col span={8}>
              产品类型：
              {experimentData.productCode}
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              实验策略：
              {strategySelectMap[experimentData.strategySelect]?.text}
            </Col>
            <Col span={8}>
              是否需要确认：
              {experimentData.needConfirm}
            </Col>
            <Col span={8}>
              分组key：
              {JSON.parse(experimentData?.strategyParam || '{}').groupingKey}
            </Col>
          </Row>
          <Row gutter={16}>
            {JSON.parse(experimentData?.strategyParam || '{}').groupConfigItems?.map((item) => (
              <Col span={8}>
                <span>{item.group === 0 ? '对照组：' : `实验组${item.group}：`}</span>
                <span>{item.basis}</span>
              </Col>
            ))}
          </Row>
          <Row gutter={16}>
            <Col span={8}>
              上线时间：
              {experimentData.onlineTime}
            </Col>
            <Col span={8}>
              下线时间：
              {experimentData.offlineTime}
            </Col>
            <Col span={8}>
              版本号：
              {experimentData.version}
            </Col>
          </Row>
          {experimentData?.strategySubSelect && (
          <Row gutter={16}>
            <Col span={8}>
              条件设置：
              {strategySubSelectMap[experimentData?.strategySubSelect]}
            </Col>
          </Row>
          )}
          {experimentData?.effectIndexIdWithName && <h3 style={{ marginTop: 20 }}>实验指标</h3>}
          {/* <Row gutter={16}>
            <Col span={20}>
              添加维度：
              {experimentData.id}
            </Col>
          </Row> */}
          {/* <Row gutter={16}>
            <Col span={20}>
              添加效果指标：
              {experimentData.effectIndex}
            </Col>
          </Row> */}
          {experimentData?.effectIndexIdWithName && experimentData?.effectIndexIdWithName.map((item, index) => (
            <Row gutter={16}>
              <Col span={20}>
                指标
                {index + 1}
                :
                {item}
              </Col>
            </Row>
          ))}
        </>
      </Modal>
    </>

  );
};

export default SplitForm;
