.canvas-wrap {
    .custom-node-wrap {
        // width: 100%;
        padding: 30px;
        text-align: center;
        position: relative;

        .custom-node-content {
            font-family: -apple-system, BlinkMacSystemFont, Segoe UI, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Helvetica Neue, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", Segoe UI Symbol;
            font-variant: tabular-nums;
            font-feature-settings: "tnum";
            background: #FFFFFF;
            box-shadow: 0 1px 10px 3px rgba(36, 61, 89, .18);
            border-radius: 10px;
            overflow: hidden;
            .trigger-wrap {
                border-radius: 10px;
                .trigger-title {
                    background: #007AFF;
                    height: 40px;
                    line-height: 40px;
                    color: #fff;
                    padding: 0 10px;
                    font-size: 16px;
                    margin: 0;
                    text-align: left;
                }
                .trigger-content {
                    padding: 8px;
                    .trigger-content-inner {
                        padding: 9px 11px;
                        background: #e2f0ff;
                        border-radius: 4px;
                        text-align: left;
                        font-size: 13px;
                        line-height: 17px;
                        color: #475669;
                        min-height: 84px;
                        overflow: hidden;
                        p {
                            word-break: break-all;
                            .titlebold {
                                font-weight: 600;
                            }
                        }
                    }
                }
            }

            .audience-wrap {
                border-radius: 10px;
                .audience-title {
                    background: #007AFF;
                    height: 40px;
                    line-height: 40px;
                    color: #fff;
                    padding: 0 10px;
                    font-size: 16px;
                    margin: 0;
                    text-align: left;
                }
                .audience-content {
                    padding: 8px;
                    .audience-content-inner {
                        padding: 9px 11px;
                        background: #e2f0ff;
                        border-radius: 4px;
                        text-align: left;
                        font-size: 13px;
                        line-height: 17px;
                        color: #475669;
                        min-height: 84px;
                        overflow: hidden;
                        p {
                            word-break: 'break-all';
                            .titlebold {
                                font-weight: 600;
                            }
                        }
                    }
                }
            }
            .supply-wrap {
                font-size: 14px;
                color: #2f333c;
                background-color: #fff;
                .top-desc {
                    height: 40px;
                    background: #F3F0FF;
                    line-height: 40px;
                    padding: 0 12px;
                    text-align: left;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                .top-content {
                    padding: 12px;
                    .top-title {
                        height: 22px;
                        line-height: 22px;
                        white-space: nowrap;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        font-size: 16px;
                        font-weight: 500;
                        color: #1f2d3d;
                        padding-bottom: 2px;
                        text-align: center;
                    }
                    .item {
                        margin-top: 10px;
                        border-radius: 6px;
                        box-sizing: border-box;
                        overflow: hidden;
                        overflow-y: auto;
                        display: flex;
                        .leftBorder {
                            display: block;
                            width: 4px;
                            height: 100%;
                            flex-shrink: 0;
                        }
                    }
                    .reward-content {
                        .leftBorder {
                            background: #8DCD58;
                        }
                        background: #F2FAEE;
                        min-height: 80px;
                        .reward-content-inner {
                            text-align: left;
                            padding: 10px 5px 10px 10px;
                            &-title {
                                font-weight: 500;
                            }
                            &-list {
                                font-size: 13px;
                                &-item {
                                    &-top {
                                        font-weight: 450;
                                    }
                                    &-bottom {

                                    }
                                }
                            }
                        }
                    }
                    .message-content {
                        .leftBorder {
                            background: #FDB635;
                        }
                        background: #FEF8ED;
                        min-height: 80px;
                        .message-content-inner {
                            text-align: left;
                            padding: 10px 5px 10px 10px;
                             &-title {
                                font-weight: 500;
                            }
                            &-list {
                                font-size: 13px;
                                &-item {
                                    &-top {
                                        font-weight: 450;
                                    }
                                    &-bottom {
                                        
                                    }
                                }
                            }
                        }
                    }
                }
            }
            .empty-wrap {
                font-size: 18px;
                line-height: 30px;
                color: #083fc0;
                background-color: #fff;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                .empty-icon {
                    width: 60px;
                    height: 60px;
                }
                .empty-title {
                    width: 100%;
                    display: flex;
                    justify-content: center;
                    cursor: pointer;
                    text-align: left;
                    span {
                        margin-left: 4px;
                    }
                }
            }
        }
    }
    .hidenode {
        opacity: 0;
    }
    .custom-node-icon-plus {
        position: absolute;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        cursor: pointer;
        font-size: 30px;
        color: #ccc;
        background-color: #fff;
        line-height: 1;
        border: 1px solid #ccc;
        border-radius: 5px;
    }
    .custom-node-op-wrap {
        position: absolute;
        display: flex;
        flex-direction: column;
        top: 30px;
        right: -2px;
        font-size: 30px;
        // color: #e81111;
        line-height: 1;
        .custom-node-icon-del {
            margin-bottom: 10px;
        }
        .custom-node-icon-copy {
            color: #ed8e09;
        }
    }
    .custom-node-can-copy {
        display: flex;
        justify-content: center;
        .paste-btn {
            margin-top: -16px;
            border: 1px solid #E4E4E4;
            border-radius: 8px;
            background: #FFFFFF;
            text-align: center;
            height: 34px;
            line-height: 32px;
            font-size: 12px;
            font-weight: 500;
            padding: 0 6px;
        }
    }
}
.tool-node-type-inner {
    border: 1px solid #D6DAE0;
    border-radius: 3px;
    padding: 14px 16px;
    margin-bottom: 8px;
    cursor: pointer;
    &-title {
        font-size: 14px;
        font-weight: 500;
        line-height: 20px;
    }
}
.tool-node-type-inner.active {
    border: 1px solid #8fb2f5;
    background-color: #e2f0ff;
}
.canvas-root-wrap {
    .canvas-wrap-form {
        padding: 10px;
        background-color: #fff;
        .ant-form-item {
            margin-top: 10px;
        }
    }
}
.steps-form-drawer .ant-pro-steps-form-container  {
     width: 100%;
}
.self-item-in-canvas-form-24 {
    margin-top: -24px;
}
.self-item-error-not-blank::before {
    display: inline-block;
    margin-inline-end: 4px;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: "*";
}
.supply-form-formlist-wrap .ant-pro-form-list-container {
    width: 100%;
}
.supply-form-drawer .supply-form-formlist-wrap .ant-pro-form-list-item.ant-pro-form-list-item-default:not(:first-child) div:first-of-type .ant-form-item .ant-form-item-label{
    display: block;
}
.supply-form-checkbox-group-businesscondition .ant-form-item {
    margin-bottom: 0;
}
