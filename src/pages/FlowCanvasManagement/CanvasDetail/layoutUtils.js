// 布局工具函数
import nanoid from 'nano-id';

/**
 * 计算节点布局信息
 * @param {Object} lf - LogicFlow实例
 * @param {string} nodeId - 当前节点ID
 * @param {Map} treeMetaMap - 存储节点元数据的Map
 * @param {Map} parentChildrenMap - 父节点到子节点的映射关系
 * @param {number} MIN_NODE_WIDTH - 节点最小宽度
 * @param {number} HORIZONTAL_GAP - 水平间距
 */
export function calculateLayout(lf, nodeId, treeMetaMap, parentChildrenMap, MIN_NODE_WIDTH, HORIZONTAL_GAP) {
  const node = lf.getNodeModelById(nodeId);
  const children = parentChildrenMap.get(nodeId) || [];

  // 先递归计算子节点
  children.forEach((childId) => {
    calculateLayout(lf, childId, treeMetaMap, parentChildrenMap, MIN_NODE_WIDTH, HORIZONTAL_GAP);
  });

  // 计算当前节点宽度
  let totalWidth = 0;
  children.forEach((childId) => {
    const childMeta = treeMetaMap.get(childId);
    if (childMeta) {
      totalWidth += childMeta.width + HORIZONTAL_GAP;
    }
  });

  treeMetaMap.set(nodeId, {
    width: Math.max(totalWidth - HORIZONTAL_GAP, MIN_NODE_WIDTH), // 去除最后一个间距
    height: node.properties.height || 48, // 默认高度48
    offset: 0,
  });
}

/**
 * 布局子节点
 * @param {Object} lf - LogicFlow实例
 * @param {string} nodeId - 当前节点ID
 * @param {number} x - 当前节点x坐标
 * @param {number} y - 当前节点y坐标
 * @param {Map} treeMetaMap - 节点元数据Map
 * @param {Map} parentChildrenMap - 父子节点关系Map
 * @param {number} VERTICAL_GAP - 垂直间距
 * @param {number} HORIZONTAL_GAP - 水平间距
 */
export function layoutChildren(lf, nodeId, x, y, treeMetaMap, parentChildrenMap, childParentMap, VERTICAL_GAP, HORIZONTAL_GAP) {
  const node = lf.getNodeModelById(nodeId);
  const nodeMeta = treeMetaMap.get(nodeId);

  // 设置当前节点位置
  node.x = x;
  node.y = y;

  const children = parentChildrenMap.get(nodeId) || [];
  children.forEach((childId) => {
    // 确保父子关系正确建立
    childParentMap.set(childId, nodeId);
  });
  let currentX = x - nodeMeta.width / 2; // 子节点排列起点

  children.forEach((childId) => {
    const childMeta = treeMetaMap.get(childId);
    const childNode = lf.getNodeModelById(childId);

    // 计算子节点坐标
    const childX = currentX + childMeta.width / 2;
    const childY = y + nodeMeta.height / 2 + VERTICAL_GAP + childMeta.height / 2;

    // 递归布局子节点
    layoutChildren(
      lf,
      childId,
      childX,
      childY,
      treeMetaMap,
      parentChildrenMap,
      childParentMap,
      VERTICAL_GAP,
      HORIZONTAL_GAP,
    );

    // 绘制连接线
    lf.addEdge({
      id: nanoid(10),
      sourceNodeId: nodeId,
      targetNodeId: childId,
      type: 'bezier',
      startPoint: {
        x: node.x,
        y: node.y + (node.properties.height || 48) / 2 - 30,
      },
      endPoint: {
        x: childNode.x,
        y: childNode.y - (childNode.properties.height || 48) / 2 + 30,
      },
    });

    currentX += childMeta.width + HORIZONTAL_GAP; // 应用水平间距
  });
}

/**
 * 自动布局入口函数
 * @param {Object} lf - LogicFlow实例
 * @param {Object} rootNode - 根节点引用
 * @param {Map} parentChildrenMap - 父子节点关系Map
 * @param {number} VERTICAL_GAP - 垂直间距
 * @param {number} HORIZONTAL_GAP - 水平间距
 * @param {number} MIN_NODE_WIDTH - 节点最小宽度
 */
export function autoLayout(lf, rootNode, parentChildrenMap, childParentMap, VERTICAL_GAP, HORIZONTAL_GAP, MIN_NODE_WIDTH) {
  const treeMetaMap = new Map();

  // 清空现有边
  const edges = lf.getGraphData().edges || [];
  edges.forEach((edge) => lf.deleteEdge(edge.id));

  // 计算布局
  calculateLayout(
    lf,
    rootNode.id,
    treeMetaMap,
    parentChildrenMap,
    MIN_NODE_WIDTH,
    HORIZONTAL_GAP,
  );

  // 应用布局
  layoutChildren(
    lf,
    rootNode.id,
    lf.container.clientWidth / 2, // 画布居中
    100, // 顶部起始位置
    treeMetaMap,
    parentChildrenMap,
    childParentMap,
    VERTICAL_GAP,
    HORIZONTAL_GAP,
  );
}
