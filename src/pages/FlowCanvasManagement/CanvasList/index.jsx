import React, {
  useRef, useState, useEffect, useCallback,
} from 'react';
import {
  Space, Button, Spin, Drawer, Popconfirm,
} from 'antd';
import { ProTable } from '@ant-design/pro-components';
import { useLocation } from 'umi';
import {
  getCanvasList, deleteCanvas, cancelAuditCanvas, operateLineCanvas, terminateDelay,
} from '@/services/canvas';
import CanvasDetail from '../CanvasDetail/index';
import OpreateList from '../CanvasDetail/opreateList';

const CanvasList = () => {
  const [loading, setLoading] = useState(false);
  const [detailOpen, setDetailOpen] = useState(false);
  const [detailOpenOplist, setDetailOpenOplist] = useState(false);
  const [canvasId, setCanvasId] = useState('');
  const [canvasIdOplist, setCanvasIdOplist] = useState('');
  const [canvasContentOplist, setCanvasContentOplist] = useState({});
  const [optType, setOptType] = useState('view');
  const location = useLocation();
  const searchParams = new URLSearchParams(location.query);
  const actionRef = useRef();
  const urlType = searchParams.get('type');
  const urlId = searchParams.get('id');

  const handleReload = () => {
    actionRef.current?.reload();
  };
  const getCanvasListData = useCallback(async (params) => {
    params.page = params.current;
    if (params.dateRange) {
      params.validaStartTime = params.dateRange[0];
      params.validaEndTime = params.dateRange[1];
    }
    Object.keys(params).forEach((key) => {
      if (params[key] === '') {
        delete params[key];
      }
    });
    try {
      const res = await getCanvasList(params);
      if (res.code === '200') {
        const { data } = res;
        return {
          data: data.list || [],
          total: data.total || 0,
        };
      }
      return {
        data: [],
        total: 0,
      };
    } catch (error) {
      return {
        data: [],
        total: 0,
      };
    }
  }, []);
  const handleDetailView = (record, id) => {
    console.log(record, id);
    setDetailOpenOplist(true);
    setCanvasIdOplist(id);
    setCanvasContentOplist(record.contentBefore);
  };
  async function handleAction(record, actionType) {
    console.log(record, actionType);
    setLoading(true);
    try {
      let res;
      switch (actionType) {
        case 'online':
        case 'offline':
          res = await operateLineCanvas({ id: record.id, onlineOperation: actionType === 'online' ? 2 : 11 }); // 上线状态 1：禁用、2:启用
          if (res?.code === '200') {
            handleReload();
          }
          break;
        case 'view':
          // history.push(`/flowCanvasManagement/canvasDetail/view/${record.id}`);
          setCanvasId(record.id);
          setOptType('view');
          setDetailOpen(true);
          break;
        case 'edit':
          // history.push(`/flowCanvasManagement/canvasDetail/edit/${record.id}`);
          setCanvasId(record.id);
          setOptType('edit');
          setDetailOpen(true);
          break;
        case 'delete':
          res = await deleteCanvas({ id: record.id });
          if (res?.code === '200') {
            handleReload();
          }
          break;
        case 'cancelAudit':
          res = await cancelAuditCanvas({ auditNo: record.configAuditDTO.auditNo });
          if (res?.code === '200') {
            handleReload();
          }
          break;
        case 'copy':
          // history.push(`/flowCanvasManagement/canvasDetail/copy/${record.id}`);
          setCanvasId(record.id);
          setOptType('copy');
          setDetailOpen(true);
          break;
        case 'terminateDelay':
          res = await terminateDelay({ id: record.id });
          if (res?.code === '200') {
            handleReload();
          }
          break;
        default:
          break;
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  }

  const columns = [
    {
      title: '流程ID',
      dataIndex: 'id',
      key: 'id',
      valueType: 'text',
      align: 'center',
      fixed: 'left',
      width: 100,
    },
    {
      title: '业务类型',
      dataIndex: 'businessType',
      key: 'businessType',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '细分产品供给',
      dataIndex: 'productType',
      key: 'productType',
      align: 'center',
      hideInSearch: true,
    },
    {
      title: '流程名称',
      dataIndex: 'name',
      key: 'name',
      align: 'center',
      valueType: 'text',
    },
    {
      title: '权益类型',
      dataIndex: 'rightType',
      key: 'rightType',
      valueType: 'select',
      valueEnum: {
        interestCoupon: {
          text: '利息优惠券',
        },
      },
      align: 'center',
      fieldProps: {
        options: [
          {
            label: '利息优惠券',
            value: 'interestCoupon',
          },
        ],
      },
    },
    {
      title: '触达类型',
      dataIndex: 'pushType',
      key: 'pushType',
      valueType: 'select',
      align: 'center',
      fieldProps: {
        options: [
          {
            label: '文本短信',
            value: 'sms',
          },
          {
            label: 'push',
            value: 'push',
          },
          {
            label: '富媒体短信',
            value: 'smartSms',
          },
        ],
      },
      render: (text, record) => {
        const tempType = record.pushType || '';
        const tempList = tempType.split(',');
        const desclist = tempList.map((item) => {
          if (item === 'push') {
            return 'push';
          }
          if (item === 'smartSms') {
            return '富媒体短信';
          }
          return '文本短信';
        });
        return desclist.join(',');
      },
    },
    {
      title: '起止时间',
      dataIndex: 'startandendtime',
      key: 'startandendtime',
      align: 'center',
      hideInSearch: true,
      render: (text, record) => `${record.validaStartTime ?? '-'}-${record.validaEndTime ?? '-'}`,
    },
    {
      title: '创建人',
      dataIndex: 'operatorName',
      key: 'operatorName',
      align: 'center',
      valueType: 'text',
    },
    {
      title: '审核人',
      dataIndex: 'auditorName',
      key: 'auditorName',
      align: 'center',
      valueType: 'text',
      hideInSearch: true,
    },
    {
      title: '生效日期',
      dataIndex: 'dateRange',
      key: 'dateRange',
      align: 'center',
      valueType: 'dateTimeRange',
      hideInTable: true,
    },
    {
      title: '状态',
      dataIndex: 'onlineStatus',
      key: 'onlineStatus',
      align: 'center',
      valueType: 'select',
      valueEnum: {
        1: { text: '禁用', status: 'Processing' }, // 蓝色
        2: { text: '启用', status: 'Success' }, // 绿色
        3: { text: '已失效', status: 'Error' }, // 红色
        4: { text: '数据准备中', status: 'Error' }, // 红色
        5: { text: '已完成', status: 'Error' }, // 红色
        6: { text: '已冻结', status: 'Error' }, // 红色
        11: { text: '已下线', status: 'Error' }, // 红色
      },
      fieldProps: {
        options: [
          {
            label: '禁用',
            value: 1,
          },
          {
            label: '启用',
            value: 2,
          },
          {
            label: '已失效',
            value: 3,
          },
          {
            label: '已下线',
            value: 11,
          },
        ],
      },
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      key: 'auditStatus',
      align: 'center',
      valueType: 'select',
      valueEnum: {
        '-1': { text: '-' }, // 蓝色
        0: { text: '-' }, // 蓝色
        1: { text: '待审核', status: 'Processing' }, // 蓝色
        2: { text: '审核通过', status: 'Success' }, // 绿色
        3: { text: '审核驳回', status: 'Error' }, // 红色
        4: { text: '审核通过但执行失败', status: 'Error' }, // 红色
        5: { text: '审核驳回但执行失败', status: 'Error' }, // 红色
      },
      fieldProps: {
        options: [
          {
            label: '待审核',
            value: 1,
          },
          {
            label: '审核通过',
            value: 2,
          },
          {
            label: '审核驳回',
            value: 3,
          },
          {
            label: '审核通过但执行失败',
            value: 4,
          },
          {
            label: '审核驳回但执行失败',
            value: 5,
          },
        ],
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      hideInSearch: true,
      align: 'center',
      fixed: 'right',
      width: 300,
      render: (text, record) => (
        <Space size="small" key={record.id}>
          {record.supportOnline === 1 && (
          <Button
            key="online"
            type="link"
            size="small"
            onClick={() => handleAction(record, 'online')}
          >
            上线
          </Button>
          )}
          {record.supportOffline === 1 && (
          <Button
            key="offline"
            type="link"
            size="small"
            onClick={() => handleAction(record, 'offline')}
          >
            下线
          </Button>
          )}
          {(record.supportApprove === 1) && (
          <Button
            key="audit"
            type="link"
            size="small"
            className="ant-btn css-var-r68e ant-btn-link ant-btn-color-link ant-btn-variant-link"
            href={`${record.configAuditDTO.flowUrl}`}
            target="_blank"
            rel="noreferrer"
          >
            审核
          </Button>
          )}
          {(record.auditStatus === 1) && (
          <Button
            key="reaudit"
            type="link"
            size="small"
            onClick={() => {
              handleAction(record, 'cancelAudit');
            }}
          >
            撤销审核
          </Button>
          )}
          {record.supportEdit === 1 && (
          <Button
            key="edit"
            type="link"
            size="small"
            onClick={() => {
              handleAction(record, 'edit');
            }}
          >
            编辑
          </Button>
          )}
          {record.supportDelete === 1 && (
            <Popconfirm
              title="确认删除吗？"
              key="delete"
              onConfirm={() => {
                handleAction(record, 'delete');
              }}
              onCancel={() => {}}
              okText="确定"
              cancelText="取消"
            >
              <Button
                key="del"
                type="link"
                size="small"
              >
                删除
              </Button>
            </Popconfirm>

          )}
          {record.supportView === 1 && (
          <Button
            key="view"
            type="link"
            size="small"
            onClick={() => {
              handleAction(record, 'view');
            }}
          >
            详情
          </Button>
          )}
          {record.supportCopy === 1 && (
          <Button
            key="copy"
            type="link"
            size="small"
            onClick={() => {
              handleAction(record, 'copy');
            }}
          >
            复制
          </Button>
          )}
          {record.supportTerminateDelay === 1 && (
            <Popconfirm
              title="此画布中含有延迟内容未执行完成，终止延迟将不执行剩余的延迟内容，请确认是否扔终止延迟"
              key="terminateDelaypopover"
              onConfirm={() => {
                handleAction(record, 'terminateDelay');
              }}
              onCancel={() => {}}
              okText="确定"
              cancelText="取消"
            >
              <Button
                key="terminateDelay"
                type="link"
                size="small"
              >
                终止延迟
              </Button>
            </Popconfirm>
          )}
        </Space>
      ),
    },
  ];
  useEffect(() => {
    if (urlType === 'view' && urlId) {
      setCanvasId(urlId);
      setOptType('view');
      setDetailOpen(true);
    }
  }, []);
  return (
    <>
      <Spin spinning={loading}>
        <ProTable
          columns={columns}
          scroll={{ x: 'max-content' }}
          search={{
            defaultCollapsed: false,
          }}
          toolbar={
            {
              title: (<Button
                type="primary"
                onClick={() => {
                  // history.push('/FlowCanvasManagement/CanvasDetail/add');
                  setCanvasId('');
                  setOptType('add');
                  setDetailOpen(true);
                }}
              >
                新建画布
              </Button>),
            }
}
          request={getCanvasListData}
          actionRef={actionRef}
        />
      </Spin>
      <Drawer
        destroyOnHidden
        open={detailOpen}
        width="100%"
        onClose={() => {
          if (urlType && urlId) {
            return;
          }
          setDetailOpen(false);
        }}
        title="画布"
        maskClosable={false}
      >
        {detailOpen && (
        <CanvasDetail
          key={`detail-${canvasId}-${optType}`}
          canvasId={canvasId}
          optType={optType}
          closeCanvas={() => {
            setDetailOpen(false); handleReload();
          }}
        />
        )}
        {detailOpen && optType === 'view' && canvasId && !detailOpenOplist && <OpreateList key={`opreateList-${canvasId}`} id={canvasId} handleDetailView={handleDetailView} />}
      </Drawer>
      <Drawer
        destroyOnHidden
        open={detailOpenOplist}
        width="100%"
        onClose={() => {
          setDetailOpenOplist(false);
        }}
        title="画布快照"
        maskClosable={false}
      >
        {detailOpenOplist && (
        <CanvasDetail
          key={`detailOplist-${canvasIdOplist}-view`}
          canvasId={canvasIdOplist}
          content={canvasContentOplist}
          optType="view"
          closeCanvas={() => { setDetailOpenOplist(false); }}
        />
        )}
      </Drawer>
    </>

  );
};

export default CanvasList;
