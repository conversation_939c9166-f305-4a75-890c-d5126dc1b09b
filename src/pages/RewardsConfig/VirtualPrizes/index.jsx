import React, { useRef, useState } from 'react';
import CommonTableList from '@/components/CommonTableList';
import {
  getVirtualList,
  settingOnline,
  getVirtualDetails,
  deleteVirtual,
} from '@/services/virtualPrizes.js';
import {
  Button, Space, Divider, Popconfirm, Spin, Tag,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import DetailModal from './components/DetailModal/index';

const VirtualPrizes = () => {
  const actionRef = useRef();
  const [modalVisible, setModalVisible] = useState(false); // 弹窗可见性
  const [actionType, setActionType] = useState('');
  const [initialConfig, setInitialConfig] = useState({});
  const [loading, setLoading] = useState(false);

  /**
   * 列表数据请求
   */
  const gainActivitySettingList = async (params) => {
    const param = {
      page: params.current,
      ...params,
    };
    const res = await getVirtualList(param);
    const { data } = res;
    return {
      data: data.list,
      total: data.total,
    };
  };

  /**
 * 表格刷新
 */
  const handleReload = () => {
    actionRef.current?.reload();
  };

  /**
   * 活动上下线
   * onlineStatus 1 上线 2 下线
   */
  const changeOnlineStatus = async (record) => {
    const params = {
      id: record.id,
      onlineOperation: record.onlineStatus === 1 ? 2 : 1,
    };
    const res = await settingOnline(params);
    if (res?.code === '200') {
      handleReload();
    }
  };

  /**
   * 列表操作
   * type: view edit delete audit add
   */
  const handleAction = async (record, type) => {
    // 删除
    if (type === 'delete') {
      const res = await deleteVirtual({ id: record.id });
      if (res?.code === '200') {
        handleReload();
      }
      return;
    }
    // 编辑详情
    if (record.id) {
      // 请求详情信息
      setLoading(true);
      const res = await getVirtualDetails({ id: record.id });
      if (res?.code === '200' && res?.data) {
        const {
          startTime,
          endTime,
          totalCount,
        } = res.data;
        const tempRecord = {
          ...res.data,
          effectiveTime: [startTime, endTime],
          countType: totalCount === -1 ? '0' : '1',
        };
        setInitialConfig(tempRecord);
        setLoading(false);
        setModalVisible(true);
      }
    } else {
      // 设置初始值
      setLoading(false);
      setModalVisible(true);
    }
    setActionType(type);
  };

  const tableColumns = [
    {
      title: '库存id',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      fixed: 'left',
      align: 'center',
    },
    {
      title: '库存名称',
      dataIndex: 'quotaName',
      key: 'quotaName',
      width: 150,
      align: 'center',
    },
    {
      title: '库存控制（奖品名称-奖品库存）',
      dataIndex: 'rewardName',
      key: 'rewardName',
      hideInSearch: true,
      width: 150,
      align: 'center',
      render: (text, record) => (
        <span>
          {record.rewardName}
          -
          {record.totalCount === -1 ? '无上限' : record.totalCount}
        </span>
      ),
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      valueType: 'dateTime',
      hideInSearch: true,
      width: 200,
      align: 'center',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      valueType: 'dateTime',
      hideInSearch: true,
      width: 200,
      align: 'center',
    },
    {
      title: '操作员',
      dataIndex: 'operatorId',
      key: 'operatorId',
      width: 150,
      align: 'center',
      render: (text, record) => (
        <span>
          {record.operatorName}
        </span>
      ),
    },
    {
      title: '审核员',
      dataIndex: 'auditorName',
      key: 'auditorName',
      hideInSearch: true,
      width: 150,
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'onlineStatus',
      key: 'onlineStatus',
      valueType: 'select',
      valueEnum: {
        1: {
          text: '禁用',
          status: 'Error',
        },
        2: {
          text: '启用',
          status: 'Success',
        },
        3: {
          text: '已失效',
          status: 'Default',
        },
      },
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
      width: 120,
      align: 'center',
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      key: 'auditStatus',
      valueType: 'select',
      valueEnum: {
        1: '待审核',
        2: '审核通过',
        3: '审核驳回',
      },
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
      render: (_, record) => {
        const auditStatusColorMap = {
          1: { text: '待审核', color: '#faad14' },
          2: { text: '审核通过', color: '#52c41a' },
          3: { text: '审核驳回', color: '#ff4d4f' },
        };
        return (
          <Space>
            <Tag
              color={auditStatusColorMap[record.auditStatus]?.color}
              key={_}
            >
              {auditStatusColorMap[record.auditStatus]?.text}
            </Tag>
          </Space>
        );
      },
      width: 120,
      align: 'center',
    },
    {
      title: '审核备注',
      dataIndex: 'remark',
      key: 'remark',
      hideInSearch: true,
      width: 150,
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      hideInSearch: true,
      width: 400,
      align: 'center',
      fixed: 'right',
      render: (text, record) => (
        <Space split={<Divider type="vertical" />} key={record.id}>
          {record.supportEdit === 1 && (
          <Button
            key="edit"
            type="link"
            size="small"
            onClick={() => {
              handleAction(record, 'edit');
            }}
          >
            编辑
          </Button>
          )}
          {record.supportDelete === 1 && (
          <Popconfirm
            title="删除后无法恢复，确认删除吗？"
            onConfirm={() => {
              handleAction(record, 'delete');
            }}
            key="del"
            onCancel={() => {}}
            okText="确定"
            cancelText="取消"
          >
            <Button key="delete" type="link" size="small">
              删除
            </Button>
          </Popconfirm>
          )}
          {record.supportApprove === 1 && (
          <Button
            key="audit"
            type="link"
            size="small"
            onClick={() => {
              handleAction(record, 'audit');
            }}
          >
            审核
          </Button>
          )}
          {record.supportView === 1 && (
          <Button
            key="view"
            type="link"
            size="small"
            onClick={() => {
              handleAction(record, 'view');
            }}
          >
            详情
          </Button>
          )}
          {record.supportOffline === 1 && (
          <Button
            key="offline"
            type="link"
            size="small"
            onClick={() => {
              changeOnlineStatus(record);
            }}
          >
            下线
          </Button>
          )}
          {record.supportOnline === 1 && (
          <Button
            key="online"
            type="link"
            size="small"
            onClick={() => {
              changeOnlineStatus(record);
            }}
          >
            上线
          </Button>
          )}
          {record.productType !== 2 && record.productType !== 6 && (
          <Button
            key="copy"
            type="link"
            size="small"
            onClick={() => {
              handleAction(record, 'copy');
            }}
          >
            复制
          </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <>
      <Spin spinning={loading}>
        <CommonTableList
          search={{ defaultCollapsed: false }}
          toolBarRender={() => [
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                handleAction({}, 'add');
              }}
            >
              新增
            </Button>,
          ]}
          columns={tableColumns}
          request={gainActivitySettingList}
          actionRef={actionRef}
        />
        {/* 详情弹窗 */}
        <DetailModal
          modalVisible={modalVisible}
          actionType={actionType}
          setActionType={setActionType}
          setModalVisible={setModalVisible}
          handleReload={handleReload}
          setInitialConfig={setInitialConfig}
          initialConfig={initialConfig}
        />
      </Spin>
    </>
  );
};

export default VirtualPrizes;
