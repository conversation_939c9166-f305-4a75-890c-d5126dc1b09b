import React, { memo } from 'react';
import { ProDescriptions, ProCard } from '@ant-design/pro-components';

const InventoryRecord = ({ initialConfig }) => {
  const columns = [
    {
      title: '发放上限数',
      dataIndex: 'totalCount',
      key: 'totalCount',
      align: 'center',
      hideInSearch: true,
      width: 120,
      render: (text, record) => (
        <span>
          {record.totalCount === -1 ? '无上限' : record.totalCount}
        </span>
      ),
    },
    {
      title: '已发送',
      dataIndex: 'sendCount',
      key: 'sendCount',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '剩余',
      dataIndex: 'residualCount',
      key: 'residualCount',
      hideInSearch: true,
      width: 120,
    },
  ];

  return (
    <ProCard>
      <ProDescriptions
        title="库存使用详情"
        dataSource={initialConfig}
        columns={columns}
      />
    </ProCard>
  );
};

export default memo(InventoryRecord);
