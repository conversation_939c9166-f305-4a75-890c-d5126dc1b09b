/* eslint-disable */
import React, { useState, useRef, useEffect } from 'react';
import {
  BetaSchemaForm,
  ProForm,
  EditableProTable,
  ModalForm,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { ConfigProvider } from 'finance-busi-components-toB';
import { auditColumns } from '@/constant/config.js';
import { getDmpTag } from '@/services/activityCommon.js';
import {
  auditVirtual,
  saveVirtual,
} from '@/services/virtualPrizes.js';
import {
  getUsePackageView,
} from '@/services/common.js';
import InventoryRecord from '../InventoryRecord';
import OperateHistory from '../OperateHistory';

import dayjs from 'dayjs';
import {
  message,
  notification,
  Button,
  Drawer,
  Space,
} from 'antd';

const DetailModal = ({
  modalVisible,
  setModalVisible,
  actionType,
  setActionType,
  width = '60%',
  handleReload,
  setInitialConfig,
  initialConfig,
  noHistoryTable = false,
}) => {
  const formRef = useRef();
  const formDisabled = actionType === 'view' || actionType === 'audit';
  const [params, setParams] = useState(initialConfig || {});

  useEffect(() => {
    setParams(initialConfig);
  }, [initialConfig]);

  /**
   * 重置数据 关闭弹窗
   */
  const resetDialog = () => {
    setModalVisible(false);
    setActionType('');
    setInitialConfig({});
    // 刷新列表
    handleReload && handleReload();
  };

  // 表单提交
  const handleSubmit = async () => {
    const values = await formRef.current?.validateFields();
    const { effectiveTime = [], countType } = values;
    const { id, version } = initialConfig
    let params = {
      id,
      version,
      ...values,
    };
    // 参数处理
    if (effectiveTime) {
      params.startTime = dayjs(effectiveTime[0]).format(
        'YYYY-MM-DD HH:mm:ss',
      );
      params.endTime = dayjs(effectiveTime[1]).format('YYYY-MM-DD HH:mm:ss');
      delete params.effectiveTime;
    }
    if (countType === '0') {
      params.totalCount = -1;
    }
    delete params.countType;

    if (['add', 'copy'].includes(actionType)) {
      delete params.id;
    }
    // 提交
    const res = await saveVirtual(params);
    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
      });
      resetDialog();
    }
  };

  // 修改审核状态
  const handleAudit = async (params) => {
    const res = await auditVirtual(params);

    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
      });
      resetDialog();
    }
  };

  const basicColumns = [
    {
      title: '库存名称',
      dataIndex: 'quotaName',
      key: 'quotaName',
      width: 'lg',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          { max: 20, message: '限制20个字符' },
        ],
      },
      fieldProps: () => ({
        maxLength: 20,
      }),
    },
    {
      title: '产品类型',
      dataIndex: 'rewardType',
      key: 'rewardType',
      valueType: 'select',
      width: 'lg',
      initialValue: 1,
      fieldProps: {
        options: [
          { label: '活动货币', value: 1 },
          { label: '虚拟物', value: 2 },
        ],
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '奖品名称',
      dataIndex: 'rewardName',
      key: 'rewardName',
      width: 'lg',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          { max: 20, message: '限制20个字符' },
          {
            validator: (_, value) => {
              const reg = /(^\s+)|(\s+$)/;
              if (reg.test(value)) {
                return Promise.reject(new Error("前后不能有空格"));
              }
              return Promise.resolve();
            },
          },
        ],
      },
      fieldProps: () => ({
        maxLength: 20,
      }),
    },
    {
      title: '奖品描述',
      dataIndex: 'rewardDesc',
      key: 'rewardDesc',
      width: 'lg',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          { max: 20, message: '限制20个字符' },
        ],
      },
      fieldProps: () => ({
        maxLength: 20,
      }),
    },
    {
      title: '奖品图片',
      dataIndex: 'rewardPicture',
      key: 'rewardPicture',
      width: 'md',
      valueType: 'inputUpload',
      fieldProps: {
        limit: {
          type: ['png', 'jpg', 'jpeg'],
        },
        appKey: 'finance-operation-admin.vmic.xyz',
        appName: 'finance-operation-admin',
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
        extra:
          '无规格限制，格式：jpg、png，jpeg',
      },
    },
    {
      title: '奖品发放',
      dataIndex: 'countType',
      key: 'countType',
      width: 'lg',
      valueType: 'radio',
      fieldProps: (form) => {
        return {
          options: [
            { label: '有上限', value: '1' },
            { label: '无上限', value: '0' },
          ],
          onChange: (e) => {
            form.setFieldValue('totalCount', '');
          },
        };
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      valueType: 'dependency',
      name: ['countType'],
      columns: ({ countType }) => {
        return countType === '1'
          ? [
            {
              title: '上限',
              dataIndex: 'totalCount',
              key: 'totalCount',
              width: 'lg',
              valueType: 'digit',
              fieldProps: {
                // prefix: '上限',
                suffix: '个',
              },
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: '此项为必填项',
                  },
                ],
              },
            },
          ]
          : [];
      },
    },
    {
      title: '有效期',
      valueType: 'dateTimeRange',
      dataIndex: 'effectiveTime',
      key: 'effectiveTime',
      width: 'lg',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          () => ({
            validator(_, value) {
              if (value && dayjs(value[1]) > dayjs()) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('结束时间必须晚于当前时间'));
            },
          }),
        ],
      },
    },
  ];
  return (
    <Drawer
      title={"库存管理配置页"}
      width={'1000'}
      onClose={() => {
        resetDialog();
      }}
      destroyOnClose={true}
      open={modalVisible}
      styles={{
        body: {
          paddingBottom: 80,
        },
      }}
      footer={
        <Space>
          <Button
            onClick={() => {
              resetDialog();
            }}
          >
            {actionType === 'view' ? '关闭' : '取消'}
          </Button>
          {['edit', 'add', 'copy'].includes(actionType) && (
            <>
              <Button
                type="primary"
                onClick={() => {
                  handleSubmit();
                }}
              >
                提交
              </Button>
            </>
          )}
          {actionType === 'audit' && (
            <>
              <ModalForm
                trigger={
                  <Button type="primary" disabled={false}>
                    审核驳回
                  </Button>
                }
                title="请填写审核驳回意见"
                modalProps={{
                  destroyOnClose: true,
                  centered: true,
                }}
                onFinish={async (auditParams) => {
                  const tempAuditParams = {
                    ...auditParams,
                    id: initialConfig?.id,
                    auditOperation: 3,
                  };

                  const res = await auditVirtual(tempAuditParams);

                  if (res?.code === '200') {
                    notification.success({
                      message: '操作成功',
                    });
                    resetDialog();
                    return true;
                  } else {
                    notification.error({
                      message: '操作失败',
                      description: res?.msg,
                    });
                  }
                }}
              >
                <BetaSchemaForm layoutType="Embed" columns={auditColumns} />
              </ModalForm>
              <Button
                type="primary"
                onClick={() => {
                  handleAudit({
                    id: initialConfig?.id,
                    auditOperation: 2,
                  });
                }}
              >
                审核通过
              </Button>
            </>
          )}
        </Space>
      }
    >
      <ConfigProvider>
        <ProForm
          formRef={formRef}
          layout="horizontal"
          submitter={false}
          onValuesChange={(changedValues, allValues) => {
            setParams({ ...params, ...changedValues });
          }}
          initialValues={initialConfig}
          disabled={formDisabled}
        >
          <BetaSchemaForm layoutType="Embed" columns={basicColumns} />
        </ProForm>
      </ConfigProvider>
      {/* 库存使用详情 */}
      {!noHistoryTable && (actionType === 'audit' || actionType === 'view') && (
        <InventoryRecord initialConfig={initialConfig} />
      )}
      {/* 操作记录 */}
      {!noHistoryTable && (actionType === 'audit' || actionType === 'view') && (
        <OperateHistory
          key="operateHistory"
          id={initialConfig?.id}
          bizCode="virtualRewardQuota"
          disabled={false}
        />
      )}
    </Drawer>
  );
};

export default DetailModal;
