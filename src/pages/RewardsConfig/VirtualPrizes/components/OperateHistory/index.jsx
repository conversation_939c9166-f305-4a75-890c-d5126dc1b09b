/* eslint-disable */
import React, { useState } from 'react';
import CommonTableList from '@/components/CommonTableList';
import { queryLogList } from '@/services/common.js';
import { Button } from 'antd';
import DetailModal from '../DetailModal';
import { operateBasicColumn } from '@/constant/config.js';
import { padArray } from '@/utils/util.js';

const OperateHistory = ({ id, bizCode }) => {
  const [modalVisible, setModalVisible] = useState(false); // 弹窗可见性
  const [actionType, setActionType] = useState('');
  const [initialConfig, setInitialConfig] = useState({});
  // 操作记录列表数据请求
  const getOperationHistory = async (params) => {
    const param = {
      page: params.current,
      pageSize: params.pageSize,
      operationId: id,
      bizCode,
    };
    const res = await queryLogList(param);

    if (res?.code === '200' && res.data) {
      return {
        data: res.data.list,
        total: res.data.total,
      };
    }
  };

  const columns = [
    ...operateBasicColumn,
    {
      title: '操作详情',
      dataIndex: 'actionType',
      key: 'actionType',
      align: 'center',
      fixed: 'right',
      hideInSearch: true,
      render: (text, record) => (
        <Button
          key="edit"
          type="link"
          size="small"
          onClick={() => {
            const {
              startTime,
              endTime,
              totalCount
            } = record.contentBefore;
            const tempRecord = {
              ...record.contentBefore,
              effectiveTime: [startTime, endTime],
              countType: totalCount === -1 ? '0' : '1',
            };
            setInitialConfig(tempRecord);
            setActionType('view');
            setModalVisible(true);
          }}
          disabled={false}
        >
          查看详情
        </Button>
      ),
    },
  ];
  return (
    <>
      <CommonTableList
        key="OperateHistory"
        columns={columns}
        search={false}
        bordered
        request={getOperationHistory}
        pagination={{
          pageSize: 10,
          showSizeChanger: false,
          showTotal: (total) => {
            return `共${total}项`;
          },
        }}
        headerTitle="操作记录"
      />
      <DetailModal
        key="historyDetailModal"
        modalVisible={modalVisible}
        actionType={actionType}
        setActionType={setActionType}
        setModalVisible={setModalVisible}
        setInitialConfig={setInitialConfig}
        initialConfig={initialConfig}
        noHistoryTable={true}
      />
    </>
  );
};

export default OperateHistory;
