/* eslint-disable */
import React, { useState, useEffect } from 'react';
import CommonTableList from '@/components/CommonTableList';
import './custom.css';
import {
  Drawer,
  Row,
  Col,
} from 'antd';
import {
  loanDetail
} from '@/services/InterestCoupon/InterestDetailsQuery.js';
const DetailModal = ({
  modalVisible,
  setModalVisible,
  initialConfig,
}) => {
  const [params, setParams] = useState(initialConfig || {});
  const [loanDTO, setLoanDTO] = useState({});
  const tableListDataSource = [params]

  useEffect(() => {
    setParams(initialConfig);
  }, [initialConfig]);
  useEffect(() => {
    if (modalVisible && initialConfig?.openId) {
      queryLoanDetail();
    }
  }, [modalVisible]);

  // 获取还款计划
  const queryLoanDetail = async () => {
    const res = await loanDetail({
      vendor:initialConfig.settleVendor,
      loanNo:initialConfig.orderSerial,
      openId:initialConfig.openId
    });
    if (res?.code === '200') {
      setLoanDTO({
        loanDate: res.data?.loanDate,
        totalLoanAmt: res.data?.totalLoanAmt,
        totalLoanPeriod: res.data?.totalLoanPeriod,
        loanList: res.data?.loanRepayDetailDTOList || [],
        settleTypeName: initialConfig?.settleTypeName || '-'

      });
    }
  };


  /**
   * 重置数据 关闭弹窗
   */
  const resetDialog = () => {
    setModalVisible(false);
    setLoanDTO({}); // 重置数据
    setParams({}); // 重置参数
  };


  const tableColumns = [
    {
      title: '活动类型',
      dataIndex: 'campaignType',
      key: 'campaignType',
      valueType: 'select',
      width: 120,
      fixed: 'left',
      align: 'center',
      hideInSearch: true,
      fieldProps: {
        options: [{
          label: '定向人群发返现券 ',
          value: '6',
        }, {
          label: '优惠券自动发放 ',
          value: '4',
        }, {
          label: '用户领券 ',
          value: '5',
        }, {
          label: '固定活动模板 ',
          value: '3',
        }, {
          label: '提额活动 ',
          value: '7',
        }],
      }
    },

    {
      title: '活动名称',
      dataIndex: 'campaignName',
      key: 'campaignName',
      align: 'center',
      width: 95,
      hideInSearch: true,
    },


    {
      title: '券名称',
      dataIndex: 'templateName',
      key: 'templateName',
      align: 'center',
      width: 90,
      hideInSearch: true,
    },

    {
      title: '券码Code',
      dataIndex: 'couponCode',
      key: 'couponCode',
      align: 'center',
      width: 100,
      hideInSearch: true,
    },

    {
      title: '券码领取时间',
      dataIndex: 'receiveTime',
      key: 'receiveTime',
      align: 'center',
      width: 90,
      hideInSearch: true,
      render: (text, record) => (
        <span>
          {record.receiveTime ? record.receiveTime : '-'}
        </span>
      )
    },


    {
      title: '核销合作方',
      dataIndex: 'settlePartnerName',
      key: 'settlePartnerName',
      align: 'center',
      width: 90,
      hideInSearch: true,
    },
    {
      title: '借据编号',
      dataIndex: 'orderSerial',
      key: 'orderSerial',
      align: 'center',
      width: 100,
      hideInSearch: true,
    },
    {
      title: '券码状态',
      dataIndex: 'couponStatusName',
      key: 'couponStatusName',
      valueType: "select",
      align: 'center',
      width: 100,
    },
    {
      title: '券码应优惠金额(元)',
      dataIndex: 'expectedSettlementAmount',
      key: 'expectedSettlementAmount',
      align: 'center',
      width: 130,
      hideInSearch: true,
    },
    {
      title: '券码累计已核销金额(元)',
      dataIndex: 'settlementAmount',
      key: 'settlementAmount',
      align: 'center',
      width: 130,
      hideInSearch: true,
    },

    {
      title: '最近一次核销时间',
      dataIndex: 'lastSettleTime',
      key: 'lastSettleTime',
      align: 'center',
      width: 130,
      hideInSearch: true,
      render: (text, record) => (
        <span>
          {record.lastSettleTime ? record.lastSettleTime : '-'}
        </span>
      )
    },
  ];

  const columns2 = [
    {
      title: '期数',
      align: 'center',
      dataIndex: 'currentLoanPeriod',
      key: 'currentLoanPeriod',
      width: 100,
      fixed: 'left'
    },
    {
      title: '应还金额',
      align: 'center',
      dataIndex: 'totalAmt',
      key: 'totalAmt',
      width: 100
    },
    {
      title: '应还本金',
      align: 'center',
      dataIndex: 'payPrincipalAmt',
      key: 'payPrincipalAmt',
      width: 100
    },
    {
      title: '应还利息',
      align: 'center',
      dataIndex: 'payInterAmt',
      key: 'payInterAmt',
      width: 100
    },
    {
      title: '应还其他费用',
      align: 'center',
      dataIndex: 'payOtherAmt',
      key: 'payOtherAmt',
      width: 130
    },
    {
      title: '应还款日期',
      align: 'center',
      dataIndex: 'payDate',
      key: 'payDate',
      width: 130,

    },
    {
      title: '应优惠金额',
      align: 'center',
      dataIndex: 'reductionInterAmt',
      key: 'reductionInterAmt',
      width: 100
    },
    {
      title: '实还金额',
      align: 'center',
      dataIndex: 'actualTotalAmt',
      key: 'actualTotalAmt',
      width: 100
    },
    {
      title: '实还利息',
      align: 'center',
      dataIndex: 'actualPayInterAmt',
      key: 'actualPayInterAmt',
      width: 100
    },
    {
      title: '实还其他费用',
      align: 'center',
      dataIndex: 'actualPayOtherAmt',
      key: 'actualPayOtherAmt',
      width: 130
    },
    {
      title: '实还罚息',
      align: 'center',
      dataIndex: 'actualPayFineIntelAmt',
      key: 'actualPayFineIntelAmt',
      width: 100
    },
    {
      title: '提前还款手续费',
      align: 'center',
      dataIndex: 'advancedCharge',
      key: 'advancedCharge',
      width: 120,
      render: (text, record) => (
        <span>{record.advancedCharge === 'null' ? 0 : record.advancedCharge}</span>
      )
    },
    {
      title: '还款日期',
      align: 'center',
      dataIndex: 'actualPayDate',
      key: 'actualPayDate',
      width: 100,
    },
    {
      title: '还款状态',
      align: 'center',
      dataIndex: 'payStatus',
      key: 'payStatus',
      width: 100
    },
    {
      title: '逾期天数',
      align: 'center',
      dataIndex: 'overdueDays',
      key: 'overdueDays',
      width: 100
    },
    {
      title: '实际优惠金额',
      align: 'center',
      dataIndex: 'actualReductionInterAmt',
      key: 'actualReductionInterAmt',
      width: 100,
      fixed: 'right'
    }
  ];
  return (
    <Drawer
      title={'利息优惠券核销明细'}
      width={'70%'}
      onClose={() => {
        resetDialog();
      }}
      destroyOnClose={true}
      open={modalVisible}
      styles={{
        body: {
          paddingBottom: 80,
        },
      }}
    >
      <CommonTableList
        search={false}
        pagination={false}
        columns={tableColumns}
        dataSource={tableListDataSource}
      />
      <Row justify="start" style={{ margin: '20px 0', }}>
        <Col span={6}>放款日期:{loanDTO.loanDate}</Col>
        <Col span={4}>借款金额:{loanDTO.totalLoanAmt}</Col>
        <Col span={4}>借款期限:{loanDTO.totalLoanPeriod}</Col>
        <Col span={4}>核销方式:{loanDTO.settleTypeName}</Col>
      </Row>
      <CommonTableList
        search={false}
        pagination={false}
        columns={columns2}
        dataSource={loanDTO.loanList || []}
        className="custom-table"
      ></CommonTableList>
    </Drawer >
  );
};

export default DetailModal;
