import React, { useRef, useState, useEffect } from 'react';
import CommonTableList from '@/components/CommonTableList';
import { exportExcel } from '@/utils/util';
import {
  multiQuery,
} from '@/services/InterestCoupon/InterestDetailsQuery.js';
import {
  getPartnerList,
} from '@/services/InterestCoupon/InterestCouponAllocation.js';
import {
  Button, Space, Divider, Spin, message,
} from 'antd';
import { DownOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import DetailModal from './DetailModal/index';
// import {
//   settleArr,
// } from '../constant.js';

const InterestDetailsQuery = () => {
  const actionRef = useRef();
  const ref = useRef();
  const [modalVisible, setModalVisible] = useState(false); // 弹窗可见性
  // const [actionType, setActionType] = useState('');
  const [initialConfig, setInitialConfig] = useState({});
  const [loading, setLoading] = useState(false);
  const [partnerList, setPartnerList] = useState([]);
  const [pageData, setPageData] = useState({ page: 1, pageSize: 10 });
  // 添加新的 state
  const [exportLoading, setExportLoading] = useState(false);
  /**
   * 列表数据请求
   */
  const queryInterestDetailsQueryList = async (params) => {
    // 添加提交验证
    if (params.campaignId && !/^\d+$/.test(params.campaignId)) {
      message.warning('活动ID只能输入数字');
      return;
    }
    if (params.quotaId && !/^\d+$/.test(params.quotaId)) {
      message.warning('红包库存ID只能输入数字');
      return;
    }
    if (params.templateId && !/^\d+$/.test(params.templateId)) {
      message.warning('券规则ID只能输入数字');
      return;
    }
    setPageData({
      page: params.current,
      pageSize: params.pageSize,
    });
    const param = {
      page: params.current,
      ...params,
    };
    const res = await multiQuery(param);
    const { data } = res;
    setLoading(false);
    return {
      data: data.list,
      total: data.total,
    };
  };

  const queryPartnerList = async () => {
    const res = await getPartnerList({ productCode: 10000 });
    const transformedArray = (res?.data || []).map((item) => ({
      label: item.displayName, // 显示名称作为展示标签
      value: item.partnerCode, // 合作方代码作为实际值
    }));
    console.log('🚀 ~ queryPartnerList ~ transformedArray:', transformedArray);
    setPartnerList(transformedArray || []);
  };
  useEffect(() => {
    queryPartnerList();
  }, []);
  const handleParams = (params) => {
    const tempParams = {
      ...params,
      page: params.current,
      pageSize: params.pageSize,
    };
    if (params.time) {
      tempParams.startValidity = dayjs(params.time[0]).format('YYYY-MM-DD');
      tempParams.endValidity = dayjs(params.time[1]).format('YYYY-MM-DD');
      delete tempParams.time;
    }
    return tempParams;
  };
  /**
   * 导出内容
   */
  const handleDownload = async () => {
    const searchParams = ref.current?.getFieldsValue();
    const tempParams = handleParams(searchParams);
    tempParams.pageSize = pageData.pageSize;
    tempParams.page = pageData.page;
    tempParams.fileExportType = 1;

    try {
      setExportLoading(true);
      // 使用 Blob 处理文件流
      // eslint-disable-next-line no-undef
      exportExcel(tempParams, '/interestCoupon/exportList');
    } catch (error) {
      message.error('导出失败');
    } finally {
      setExportLoading(false);
    }
  };
  // 审核详情
  const handleAction = async (record) => {
    setInitialConfig(record);
    setModalVisible(true);
  };

  const settlementStatusArr = [
    {
      label: '已领取',
      value: '1',
    },
    {
      label: '已绑定',
      value: '2',
    }, {
      label: '核销中',
      value: '3',
    }, {
      label: '核销完成',
      value: '4',
    },
    {
      label: '已过期',
      value: '5',
    },
    {
      label: '已失效',
      value: '6',
    },
  ];
  const tableColumns = [
    {
      title: '活动类型',
      dataIndex: 'campaignTypeName',
      key: 'campaignType',
      valueType: 'select',
      width: 120,
      fixed: 'left',
      align: 'center',
      fieldProps: {
        options: [{
          label: '定向人群发返现券',
          value: 6,
        }, {
          label: '优惠券自动发放 ',
          value: 4,
        }, {
          label: '用户领券 ',
          value: 5,
        }, {
          label: '固定活动模板 ',
          value: 3,
        }, {
          label: '提额活动 ',
          value: 7,
        }],
      },
    },
    {
      title: '活动ID',
      dataIndex: 'campaignId',
      key: 'campaignId',
      align: 'center',
      width: 150,
      valueType: 'text',
    },
    {
      title: '活动名称',
      dataIndex: 'campaignName',
      key: 'campaignName',
      align: 'center',
      width: 95,
    },
    {
      title: '红包库存ID',
      dataIndex: 'quotaId',
      key: 'quotaId',
      align: 'center',
      width: 90,
      valueType: 'text',
    },

    {
      title: '券规则ID',
      dataIndex: 'templateId',
      key: 'templateId',
      align: 'center',
      width: 95,
      valueType: 'text',
    },
    {
      title: '券名称',
      dataIndex: 'templateName',
      key: 'templateName',
      align: 'center',
      width: 90,
    },
    {
      title: '可用合作方',
      dataIndex: 'availablePartnerList',
      key: 'availablePartnerList',
      align: 'center',
      width: 90,
      hideInSearch: true,
    },
    {
      title: '可用合作方',
      dataIndex: 'partnerCode',
      key: 'partnerCode',
      align: 'center',
      valueType: 'select',
      width: 90,
      hideInTable: true,
      // request: queryPartnerList,
      fieldProps: {
        options: partnerList,
      },

    },
    {
      title: '券码Code',
      dataIndex: 'couponCode',
      key: 'couponCode',
      align: 'center',
      width: 120,
    },
    {
      title: '用户openid',
      dataIndex: 'openId',
      key: 'openId',
      align: 'center',
      width: 120,
    },
    {
      title: '账户手机号',
      dataIndex: 'mobilePhone',
      key: 'mobilePhone',
      align: 'center',
      width: 120,
    },
    {
      title: '券码领取时间',
      dataIndex: 'receiveTime',
      key: 'receiveTime',
      align: 'center',
      width: 120,
      hideInSearch: true,
      render: (text, record) => (
        <span>
          {record.receiveTime ? record.receiveTime : '-'}
        </span>
      ),
    },

    {
      title: '券码过期时间',
      dataIndex: 'endValidity',
      key: 'endValidity',
      align: 'center',
      width: 120,
      hideInSearch: true,

      // renderForItem: () => {},
      render: (text, record) => (
        <span>
          {record.endValidity ? record.endValidity : '-'}
        </span>
      ),
    },
    {
      title: '券码绑定时间',
      dataIndex: 'finishTime',
      key: 'finishTime',
      align: 'center',
      width: 120,
      hideInSearch: true,
      render: (text, record) => (
        <span>{record.finishTime ? record.finishTime : '-'}</span>
      ),
    },
    {
      title: '用户使用失效时间',
      dataIndex: 'useEndValidity',
      key: 'useEndValidity',
      align: 'center',
      width: 120,
      hideInSearch: true,
      render: (text, record) => (
        <span>
          {record.useEndValidity ? record.useEndValidity : '-'}
        </span>
      ),
    },
    {
      title: '核销合作方',
      dataIndex: 'settlePartnerName',
      key: 'settlePartnerName',
      align: 'center',
      width: 90,
      hideInSearch: true,
    },
    // 搜索框用
    {
      title: '核销合作方',
      dataIndex: 'settleFundingChannel',
      key: 'settleFundingChannel',
      align: 'center',
      width: 90,
      hideInTable: true,
      valueType: 'select',
      fieldProps: {
        options: partnerList,
      },
    },
    {
      title: '核销方式',
      dataIndex: 'settleTypeName',
      key: 'settleTypeName',
      hideInSearch: true,
      width: 120,
      align: 'center',
    },
    {
      title: '借据编号',
      dataIndex: 'orderSerial',
      key: 'orderSerial',
      align: 'center',
      width: 120,
    },
    {
      title: '借款金额',
      dataIndex: 'loanAmount',
      key: 'loanAmount',
      align: 'center',
      width: 90,
      hideInSearch: true,
    },
    {
      title: '券码状态',
      dataIndex: 'couponStatusName',
      key: 'couponStatusName',
      valueType: 'select',
      align: 'center',
      width: 90,
      hideInSearch: true,
    },
    {
      title: '券码状态',
      dataIndex: 'couponStatusList',
      key: 'couponStatusList',
      valueType: 'select',
      align: 'center',
      width: 90,
      hideInTable: true,
      fieldProps: {
        options: settlementStatusArr,
        mode: 'multiple',
      },
    },
    {
      title: '券码领取时间',
      dataIndex: 'time',
      key: 'time',
      valueType: 'dateRange',
      align: 'center',
      width: 130,
      hideInTable: true,
      fieldProps: {
        allowEmpty: [true, true],
      },
      search: {
        transform: (value) => ({
          startValidity: value[0],
          endValidity: value[1],
        }),
      },
    },
    {
      title: '券码应优惠金额(元)',
      dataIndex: 'expectedSettlementAmount',
      key: 'expectedSettlementAmount',
      align: 'center',
      width: 90,
      hideInSearch: true,
    },
    {
      title: '券码累计已核销金额(元)',
      dataIndex: 'settlementAmount',
      key: 'settlementAmount',
      align: 'center',
      width: 90,
      hideInSearch: true,
    },

    {
      title: '最近一次核销时间',
      dataIndex: 'lastSettleTime',
      key: 'lastSettleTime',
      align: 'center',
      width: 120,
      hideInSearch: true,
      render: (text, record) => (
        <span>
          {record.lastSettleTime ? record.lastSettleTime : '-'}
        </span>
      ),
    },
    {
      title: '核销详情',
      dataIndex: 'action',
      key: 'action',
      width: 110,
      align: 'center',
      fixed: 'right',
      hideInSearch: true,
      render: (text, record) => (
        <Space split={<Divider type="vertical" />} key={record.id}>
          <Button
            key="view"
            type="link"
            size="small"
            onClick={() => {
              handleAction(record);
            }}
          >
            查看详情
          </Button>
        </Space>
      ),
    },
  ];

  return (
    <>
      <Spin spinning={loading}>
        <CommonTableList
          search={{ defaultCollapsed: false, labelWidth: 'auto' }}
          toolBarRender={() => [
            <Button key="out" onClick={handleDownload} loading={exportLoading}>
              导出数据
              {!exportLoading && <DownOutlined />}
            </Button>,
          ]}
          columns={tableColumns}
          request={queryInterestDetailsQueryList}
          actionRef={actionRef}
          formRef={ref}
          pagination={{
            pageSize: 10,
            showSizeChanger: false,
            showTotal: (total) => `共${total}项`,
          }}
        />
        {/* 审核详情弹窗 */}
        <DetailModal
          modalVisible={modalVisible}
          setModalVisible={setModalVisible}
          initialConfig={initialConfig}
        />
      </Spin>
    </>
  );
};

export default InterestDetailsQuery;
