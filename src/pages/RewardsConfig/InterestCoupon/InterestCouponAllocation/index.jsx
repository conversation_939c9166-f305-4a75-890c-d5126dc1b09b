import React, { useRef, useState } from 'react';
import CommonTableList from '@/components/CommonTableList';
import {
  getInterestCouponList,
  settingOnline,
  getInterestCouponDetails,
  deleteInterestCoupon,
} from '@/services/InterestCoupon/InterestCouponAllocation.js';
import {
  Button, Space, Divider, Popconfirm, Spin, Tag,
  message,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import DetailModal from './components/DetailModal/index';

const InterestCouponAliiocation = () => {
  const actionRef = useRef();
  const [modalVisible, setModalVisible] = useState(false); // 弹窗可见性
  const [actionType, setActionType] = useState('');
  const [initialConfig, setInitialConfig] = useState({});
  const [loading, setLoading] = useState(false);

  /**
   * 列表数据请求
   */
  const gainInterestCouponList = async (params) => {
    // 添加提交验证
    if (params.id && !/^\d+$/.test(params.id)) {
      message.warning('券规则ID只能输入数字');
      return;
    }
    const param = {
      page: params.current,
      couponType: 4,
      ...params,
    };
    const res = await getInterestCouponList(param);
    const { data } = res;
    if (data.list.length === 0) {
      message.warning('未查询到结果，请检查查询条件是否输入正确');
    }
    return {
      data: data.list,
      total: data.total,
    };
  };

  /**
 * 表格刷新
 */
  const handleReload = () => {
    actionRef.current?.reload();
  };

  /**
   * 活动上下线
   * onlineStatus 1 上线 2 下线
   */
  const changeOnlineStatus = async (record) => {
    const params = {
      id: record.id,
      onlineOperation: record.onlineStatus === 1 ? 2 : 1,
    };
    const res = await settingOnline(params);
    if (res?.code === '200') {
      handleReload();
    }
  };

  /**
   * 列表操作
   * type: view edit delete audit add
   */
  const handleAction = async (record, type) => {
    // 删除
    if (type === 'delete') {
      const res = await deleteInterestCoupon({ id: record.id });
      if (res?.code === '200') {
        handleReload();
      }
      return;
    }
    // 编辑详情
    if (record.id) {
      // 请求详情信息
      setLoading(true);
      const res = await getInterestCouponDetails({ id: record.id });
      if (res?.code === '200' && res?.data) {
        const tempRecord = {
          ...res.data,
          countType: 4,
        };
        setInitialConfig(tempRecord);
        setLoading(false);
        setModalVisible(true);
      }
    } else {
      // 设置初始值
      setLoading(false);
      setModalVisible(true);
    }
    setActionType(type);
  };

  const tableColumns = [
    {
      title: '券规则ID',
      dataIndex: 'id',
      key: 'id',
      width: 120,
      fixed: 'left',
      align: 'center',
      valueType: 'text',
    },
    {
      title: '券名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      align: 'center',
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      valueType: 'dateTime',
      hideInSearch: true,
      width: 200,
      align: 'center',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      valueType: 'dateTime',
      hideInSearch: true,
      width: 200,
      align: 'center',
    },
    {
      title: '操作员',
      dataIndex: 'operatorName',
      key: 'operatorName',
      width: 150,
      align: 'center',
      render: (text, record) => (
        <span>
          {record.operatorName}
        </span>
      ),
    },
    {
      title: '审核员',
      dataIndex: 'auditorName',
      key: 'auditorName',
      hideInSearch: true,
      width: 150,
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'onlineStatus',
      key: 'onlineStatus',
      valueType: 'select',
      valueEnum: {
        1: {
          text: '禁用',
          status: 'Error',
        },
        2: {
          text: '启用',
          status: 'Success',
        },
        3: {
          text: '已失效',
          status: 'Default',
        },
      },
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
      width: 120,
      align: 'center',
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      key: 'auditStatus',
      valueType: 'select',
      valueEnum: {
        1: '待审核',
        2: '审核通过',
        3: '审核驳回',
      },
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
      render: (_, record) => {
        const auditStatusColorMap = {
          1: { text: '待审核', color: '#faad14' },
          2: { text: '审核通过', color: '#52c41a' },
          3: { text: '审核驳回', color: '#ff4d4f' },
        };
        return (
          <Space>
            <Tag
              color={auditStatusColorMap[record.auditStatus]?.color}
              key={_}
            >
              {auditStatusColorMap[record.auditStatus]?.text}
            </Tag>
          </Space>
        );
      },
      width: 120,
      align: 'center',
    },
    // {
    //   title: '审核备注',
    //   dataIndex: 'remark',
    //   key: 'remark',
    //   hideInSearch: true,
    //   width: 150,
    //   align: 'center',
    // },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      hideInSearch: true,
      width: 400,
      align: 'center',
      fixed: 'right',
      render: (text, record) => (
        <Space split={<Divider type="vertical" />} key={record.id}>
          {record.supportEdit === 1 && (
            <Button
              key="edit"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'edit');
              }}
            >
              编辑
            </Button>
          )}
          {record.supportDelete === 1 && (
            <Popconfirm
              title="删除后无法恢复，确认删除吗？"
              onConfirm={() => {
                handleAction(record, 'delete');
              }}
              key="del"
              onCancel={() => { }}
              okText="确定"
              cancelText="取消"
            >
              <Button key="delete" type="link" size="small">
                删除
              </Button>
            </Popconfirm>
          )}
          {record.supportApprove === 1 && (
            <Button
              key="audit"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'audit');
              }}
            >
              审核
            </Button>
          )}
          {record.supportView === 1 && (
            <Button
              key="view"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'view');
              }}
            >
              详情
            </Button>
          )}
          {record.supportOffline === 1 && (
          <Popconfirm
            title="您确定下线？"
            onConfirm={() => {
              changeOnlineStatus(record);
            }}
            key="offline"
            onCancel={() => {}}
            okText="确定"
            cancelText="取消"
          >
            <Button
              key="offline"
              type="link"
              size="small"
            >
              下线
            </Button>
          </Popconfirm>
          )}
          {record.supportOnline === 1 && (

          <Popconfirm
            title="上线后优惠券立即生效，且只能进行下线操作，无法进行编辑和删除，确定上线吗？"
            onConfirm={() => {
              changeOnlineStatus(record);
            }}
            key="online"
            onCancel={() => {}}
            okText="确定"
            cancelText="取消"
          >
            <Button
              key="online"
              type="link"
              size="small"
            >
              上线
            </Button>
          </Popconfirm>
          )}
          {record.supportCopy === 1 && (
            <Button
              key="copy"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'copy');
              }}
            >
              复制
            </Button>
          )}
        </Space>
      ),
    },
  ];

  return (
    <>
      <Spin spinning={loading}>
        <CommonTableList
          search={{
            defaultCollapsed: false,
          }}
          toolBarRender={() => [
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                handleAction({}, 'add');
              }}
            >
              新增
            </Button>,
          ]}
          columns={tableColumns}
          request={gainInterestCouponList}
          actionRef={actionRef}
          pagination={{
            pageSize: 10,
            showSizeChanger: false,
            showTotal: (total) => `共${total}项`,
          }}
        />
        {/* 详情弹窗 */}
        <DetailModal
          modalVisible={modalVisible}
          actionType={actionType}
          setActionType={setActionType}
          setModalVisible={setModalVisible}
          handleReload={handleReload}
          setInitialConfig={setInitialConfig}
          initialConfig={initialConfig}
        />
      </Spin>
    </>
  );
};

export default InterestCouponAliiocation;
