/* eslint-disable */
import React, { useState, useRef, useEffect } from 'react';
import RichTextEditor from "@/components/RichTextEditor";
import {
  BetaSchemaForm,
  ProForm,
  EditableProTable,
  ModalForm,
  ProFormTextArea,
} from '@ant-design/pro-components';
import {
  PERIOD_LIST,
} from '../../../constant.js';
import { ConfigProvider } from 'finance-busi-components-toB';
import { auditColumns } from '@/constant/config.js';
import CouponTemplate from '../../../CouponTemplate';
import {
  getPartnerList,
  auditInterestCoupon,
  saveInterestCoupon
} from '@/services/InterestCoupon/InterestCouponAllocation.js';

import OperateHistory from '../OperateHistory';

import {
  notification,
  Button,
  Drawer,
  Space,
} from 'antd';

const DetailModal = ({
  modalVisible,
  setModalVisible,
  actionType,
  setActionType,
  width = '60%',
  handleReload,
  setInitialConfig,
  initialConfig,
  noHistoryTable = false,
}) => {
  const titleMap = {
    edit: '编辑',
    audit: '审核',
    view: '查看详情',
    add: '新增',
    copy: '复制',
  };
  const formRef = useRef();
  const formDisabled = actionType === 'view' || actionType === 'audit';
  const [partnerList, setPartnerList] = useState([]);
  console.log('initialConfig', initialConfig)
  const { discountEffect = {}, periodEffect = {}, partnerCodes = {}, loanStrategy = {}, repayStrategy = {} } = initialConfig
  const { periodRestriction, amountRestriction, loanMembershipRestriction } = loanStrategy
  const { repaymentRestriction, repayMembershipRestriction } = repayStrategy
  /**
   *处理借款规则start
   */
  let loanType = []
  if (periodRestriction?.enable) {
    loanType.push('isPeriodChecked')
  }
  if (amountRestriction?.enable && amountRestriction?.minAmountInclude !== 0) {
    loanType.push('isPeriodMoneyChecked')
  }
  if (loanMembershipRestriction?.enable) {
    loanType.push('isLoanMemberChecked')
  }
  /**
 *处理借款规则end
 */
  let repayTypeArr = []
  repayTypeArr.push('settleInAdvance', 'overdue', '') //默认选中、 提前结清失效、还款逾期失效
  if (repayMembershipRestriction?.enable) {
    repayTypeArr.push('repayMembershipRestriction')
  }
  let partnerCodesArr = partnerCodes['10000']
  initialConfig = {
    ...initialConfig,
    periodType: periodEffect?.periodType,
    firstNDays: periodEffect?.firstNDays,
    specifiedPeriods: periodEffect?.specifiedPeriods,
    discountInterestRate: discountEffect.discountInterestRate ?? undefined,
    maxDiscountInterestAmount: discountEffect?.maxDiscountInterestAmount ? discountEffect?.maxDiscountInterestAmount / 100 : undefined,
    partnerCodeArr: partnerCodesArr || [],
    loanType,
    repayTypeArr,
    minPeriod: periodRestriction?.minPeriod || undefined,
    minAmountInclude: amountRestriction?.minAmountInclude ? amountRestriction?.minAmountInclude / 100 : undefined,
    personalized: amountRestriction?.personalized || false,
    percentage: amountRestriction?.percentage || undefined,
    limitPartner: actionType === 'add' ? undefined : partnerCodesArr?.length > 0 ? 1 : 2,
  }
  // 可用合作方列表
  const queryPartnerList = async () => {
    const res = await getPartnerList({ productCode: 10000 });
    const transformedArray = (res?.data || []).map((item) => ({
      label: item.displayName, // 显示名称作为展示标签
      value: item.partnerCode, // 合作方代码作为实际值
    }));
    console.log('🚀 ~ queryPartnerList ~ transformedArray:', transformedArray);
    setPartnerList(transformedArray || []);
  };
  useEffect(() => {
    queryPartnerList();
  }, []);
  //处理优惠券展示字段
  const handleCoupon = (form) => {
    const loanType = form.getFieldValue('loanType');
    if (loanType?.includes('isPeriodMoneyChecked')) {
      const limitPartner = form.getFieldValue('limitPartner');
      const minAmountInclude = form.getFieldValue('minAmountInclude');
      const personalized = form.getFieldValue('personalized');
      const minPeriod = form.getFieldValue('minPeriod');
      const detailDesc =
        `<p>1.${limitPartner === 1 ? '本券仅限${limitPartner}借款使用，' : ''}借款金额需满${personalized ? '${limitAmount}' : minAmountInclude || ''}元${minPeriod ? `，借${minPeriod || ''}期以上可用` : ''}</p>`
      form.setFieldValue('detailDesc', detailDesc);
      const briefDesc = `借款满${personalized ? '${limitAmount}' : minAmountInclude || ''}元可用${minPeriod ? `\n借${minPeriod || ''}期以上可用` : ''}`

      form.setFieldValue('briefDesc', briefDesc);
    }
  }
  //处理优惠券标题
  const handleCouponTitle = (form) => {
    const periodType = form.getFieldValue('periodType');
    const firstNDays = form.getFieldValue('firstNDays');
    const specifiedPeriods = form.getFieldValue('specifiedPeriods');
    const discountInterestRate = form.getFieldValue('discountInterestRate');
    const displayTitle = `${discountInterestRate ? `${discountInterestRate / 10}折` : ''}${periodType === 'firstNDaysEffect' ? `${firstNDays || ''}天` : `${specifiedPeriods?.length || ''}期`}利息优惠券`
    form.setFieldValue('displayTitle', displayTitle);
  }
  /**
   * 重置数据 关闭弹窗
   */
  const resetDialog = (updata) => {
    setModalVisible(false);
    setActionType('');
    setInitialConfig({});
    // 刷新列表
    if (updata) {
      handleReload && handleReload();
    }
  };

  // 表单提交
  const handleSubmit = async () => {

    const values = await formRef.current?.validateFields();
    const { name, periodType, firstNDays, displayTitle, personalized,
      briefDesc,
      detailDesc, specifiedPeriods, limitPartner, discountInterestRate, loanType, repayTypeArr, maxDiscountInterestAmount, periodRestriction, amountRestriction, minAmountInclude, minPeriod, percentage, partnerCodeArr } = values;
    let params = {
      couponType: 4,
      name,
      displayTitle,
      briefDesc,
      detailDesc
    };
    // 参数处理start

    //优惠期限
    params.periodEffect = {
      periodType,
      firstNDays,
      specifiedPeriods,
    }
    //优惠力度
    params.discountEffect = {
      discountInterestRate,
      discountType: 'rateEffect', //默认利息折扣
      maxDiscountInterestAmount: maxDiscountInterestAmount ? maxDiscountInterestAmount * 100 : null,

    };

    // 处理合作方数据
    if (limitPartner === 1) {
      params.partnerCodes = {
        '10000': partnerCodeArr
      };
    }
    // 借款校验
    // periodRestriction(借款期限限制)
    // amountRestriction(借款金额限制)
    // loanMembershipRestriction(借款时仅钱包会员生效可用)

    params.loanStrategy = {
      periodRestriction: {
        enable: loanType.includes('isPeriodChecked'),
        minPeriod,
      },
      amountRestriction: {
        enable: loanType.includes('isPeriodMoneyChecked'),
        minAmountInclude: minAmountInclude ? minAmountInclude * 100 : undefined,
        personalized,
        percentage: percentage ? Number(percentage) : undefined,
      },
      loanMembershipRestriction: {
        enable: loanType.includes('isLoanMemberChecked'),
      },
    };
    //还款校验：
    // repaymentRestriction.settleInAdvance(提前结清失效)
    // repaymentRestriction.overdue(还款逾期失效)
    // repayMembershipRestriction(还款时仅钱包会员生效可享优惠)
    params.repayStrategy = {
      repaymentRestriction: {
        settleInAdvance: repayTypeArr.includes('settleInAdvance'),
        overdue: repayTypeArr.includes('overdue'),
      },
      repayMembershipRestriction: {
        enable: repayTypeArr.includes('repayMembershipRestriction'),
      }
    }
    if (!['add', 'copy'].includes(actionType)) {
      params.id = initialConfig.id;
      params.version = initialConfig.version;
    }
    console.log("🚀 ~ handleSubmit ~ params:", params)
    // 提交
    const res = await saveInterestCoupon(params);
    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
      });
      resetDialog(true);
    }

  }
  // 修改审核状态
  const handleAudit = async (params) => {
    const res = await auditInterestCoupon(params);

    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
      });
      resetDialog(true);
    }
  };


  const basicColumns = [
    {
      title: '券名称',
      dataIndex: 'name',
      key: 'name',
      width: 'lg',
      formItemProps: {

        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          {
            validator: (_, value) => {
              if (!value) return Promise.resolve();
              if (value.length > 30) {
                return Promise.reject('请输入不超过30个字的券名称');
              }
              return Promise.resolve();
            }
          }
        ],
      },
      fieldProps: () => ({
        placeholder: '请输入券名称，内部命名，不超过30个字',
      }),
    },
    {
      title: '限定合作方',
      dataIndex: 'limitPartner',
      key: 'limitPartner',
      valueType: 'radio',
      width: 'lg',
      fieldProps: (form) => {
        return {
          options: [
            { label: '限制可用合作方', value: 1 },
            { label: '不限合作方', value: 2 },
          ],
          onChange: () => {
            form.setFieldValue('partnerCodeArr', []);
            handleCoupon(form);
          },
        };
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      valueType: 'dependency',
      name: ['limitPartner'],
      columns: ({ limitPartner }) => {
        return limitPartner === 1
          ? [
            {
              title: '限制可用合作方',
              dataIndex: 'partnerCodeArr',
              key: 'partnerCodeArr',
              width: '100%',
              valueType: 'checkbox',
              fieldProps: (form) => {
                return {
                  options: partnerList,
                  onChange: () => {
                    handleCoupon(form);
                  },
                };
              },
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: '此项为必填项',
                  },
                ],
              },
            },
          ]
          : [];
      },
    },
    {
      title: '优惠期限',
      dataIndex: ['periodType'],
      // key: 'periodType',
      valueType: 'radio',
      width: 'lg',
      fieldProps: (form) => {
        return {
          options: [
            { label: '按天优惠', value: 'firstNDaysEffect' },
            { label: '按期优惠', value: 'specificPeriodEffect' },
          ],
          onChange: () => {
            form.setFieldValue('firstNDays', undefined);
            form.setFieldValue('specifiedPeriods', []);
            handleCouponTitle(form);

          },
        };
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      valueType: 'dependency',
      name: ['periodType', 'limitPartner'],
      columns: ({ periodType, limitPartner }) => {
        if (periodType === 'firstNDaysEffect') {
          return [
            {
              title: '优惠前',
              dataIndex: ['firstNDays'],
              width: 'lg',
              valueType: 'digit',
              fieldProps: (form) => ({
                precision: 0,
                placeholder: "请输入填1-365的整数",
                suffix: '天',
                onBlur: () => {
                  handleCouponTitle(form);
                }
              }),
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: '此项为必填项',
                  },
                  {
                    validator: (_, value) => {
                      if (value !== undefined) {
                        if (value < 1) {
                          return Promise.reject('请输入大于等于1的整数');
                        }
                        if (value > 365) {
                          return Promise.reject('请输入小于等于365的整数');
                        }
                      }
                      return Promise.resolve();
                    }
                  }
                ],
                extra: "填1-365的整数",
              },
            },
          ]
        }
        if (periodType === 'specificPeriodEffect') {
          return [
            {
              title: '优惠期数',
              dataIndex: ['specifiedPeriods'],
              width: '100%',
              valueType: 'checkbox',
              fieldProps: (form) => {
                return {
                  options: PERIOD_LIST,
                  onChange: () => {
                    handleCouponTitle(form);
                  },
                };
              },
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: '此项为必填项',
                  },
                ],

              },
            },
          ]
        }
        return [];
      },
    },
    {
      title: '',
      renderFormItem: () => <h4>优惠力度</h4>,
    },
    {
      title: '利息折扣',
      dataIndex: ['discountInterestRate'],
      width: 'lg',
      valueType: 'digit',
      fieldProps: (form) => ({
        precision: 0,
        placeholder: "填1-99正整数，如填70则为7折，填0则为免息",
        suffix: '%',
        onBlur: () => {
          handleCouponTitle(form);
        }
      }),
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          {
            validator: (_, value) => {
              if (value !== undefined) {
                if (value < 0) {
                  return Promise.reject('请输入大于等于0的整数');
                }
                if (value > 99) {
                  return Promise.reject('请输入小于等于99的整数');
                }
              }
              return Promise.resolve();
            }
          }
        ],
        extra: '填1-99正整数，如填70则为7折，填0则为免息'
      },
    },
    {
      title: '最高减免',
      dataIndex: ['maxDiscountInterestAmount'],
      width: 'lg',
      valueType: 'digit',
      fieldProps: (form) => ({
        precision: 0,
        placeholder: "选填，请输入大于0的整数",
        suffix: '元',
        onBlur: () => {
          handleCouponTitle(form);
        }
      }),
      formItemProps: {
        rules: [
          {
            required: false,
            message: '选填',
          },
          {
            validator: (_, value) => {
              if (value !== undefined && value <= 0) {
                return Promise.reject('请输入大于0的整数');
              }
              return Promise.resolve();
            }
          }
        ],
      },
    },
    {
      title: '',
      renderFormItem: () => <h4>借款校验</h4>,
    },
    {
      title: '借款校验',
      dataIndex: ['loanType'],
      valueType: 'checkbox',
      width: '100%',
      fieldProps: (form) => {
        return {
          options: [
            { label: '借款期限限制', value: 'isPeriodChecked' },
            { label: '借款金额限制', value: 'isPeriodMoneyChecked' },
            { label: '校验会员身份', value: 'isLoanMemberChecked' },
          ],
          onChange: (value) => {
            if (!value.includes('isPeriodChecked')) {
              form.setFieldValue('minPeriod', '');
            }
            if (!value.includes('isPeriodMoneyChecked')) {
              form.setFieldValue('percentage', '');
              form.setFieldValue('minAmountInclude', '');
              form.setFieldValue('detailDesc', '');
              form.setFieldValue('briefDesc', '');
            }
            handleCoupon(form)
          },
        };
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      valueType: 'dependency',
      name: ['loanType'],
      columns: ({ loanType }) => {
        let minPeriodDto = {
          title: '期数限制',
          dataIndex: ['minPeriod'],
          // key: 'firstNDays',
          width: 'lg',
          valueType: 'digit',
          fieldProps: (form) => ({
            precision: 0,
            placeholder: "请输入期数",
            suffix: '期',
            onChange: () => {
              handleCoupon(form);
            }
          }),
          formItemProps: {
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
              {
                validator: (_, value) => {
                  if (value !== undefined) {
                    if (value < 1) {
                      return Promise.reject('请输入大于等于1的整数');
                    }
                    if (value > 12) {
                      return Promise.reject('请输入小于等于12的整数');
                    }
                  }
                  return Promise.resolve();
                }
              }
            ],
            extra: 'x期以上可用，填1-12期'
          },
        }
        let amountRestrictionDto = {
          title: '券门槛个性化',
          dataIndex: ['personalized'],
          width: '100%',
          valueType: 'switch',
          initialValue: false,
          fieldProps: (form) => ({
            onChange: (e) => {
              if (!e) {
                form.setFieldValue('percentage', '');
              }
              handleCoupon(form)

            },
          }),
          formItemProps: {
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
            ],
          },
        }
        let minAmountIncludeDto = {
          valueType: 'dependency',
          title: '若无额度默认券门槛',
          dataIndex: ['minAmountInclude'],
          width: 'lg',
          valueType: 'digit',
          fieldProps: (form) => ({
            precision: 0,
            placeholder: "借款金额",
            suffix: '元',
            onBlur: () => {
              handleCoupon(form);
            }
          }),
          formItemProps: {
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
              {
                validator: (_, value) => {
                  if (value !== undefined) {
                    if (value < 1) {
                      return Promise.reject('请输入大于等于1的整数');
                    }
                  }
                  return Promise.resolve();
                }
              }
            ],
            extra: '填正整数，此金额以上可用'
          },
        }
        let percentageFDTO = {
          valueType: 'dependency',
          name: ['personalized'],
          columns: ({ personalized }) => {
            let percentageDto = {
              //valueType: 'dependency',
              title: '剩余额度的',
              dataIndex: ['percentage'],
              width: 'lg',
              valueType: 'digit',
              fieldProps: (form) => ({
                precision: 0,
                placeholder: "借款金额",
                suffix: '%',
                onBlur: () => {
                  handleCoupon(form);
                }
              }),
              formItemProps: {
                rules: [
                  {
                    required: true,
                    message: '此项为必填项',
                  },
                  {
                    validator: (_, value) => {
                      if (value !== undefined) {
                        if (value < 1) {
                          return Promise.reject('请输入大于等于1的整数');
                        }
                        if (value > 100) {
                          return Promise.reject('请输入小于等于100的整数');
                        }
                      }
                      return Promise.resolve();
                    }
                  }
                ],
              },
            }
            let arr = []
            if (personalized) {
              arr.push(percentageDto)
            }
            return arr;
          },
        }
        let loanArr = []
        if (loanType?.includes('isPeriodChecked')) {
          loanArr.push(minPeriodDto)
        }
        if (loanType?.includes('isPeriodMoneyChecked')) {
          loanArr.push(amountRestrictionDto, percentageFDTO, minAmountIncludeDto)
        }
        return loanArr;
      },
    },
    {
      title: '',
      renderFormItem: () => <h4>还款校验</h4>,
    },
    {
      title: '校验规则',
      dataIndex: ['repayTypeArr'],
      valueType: 'checkbox',
      width: '100%',
      fieldProps: () => {
        return {
          options: [
            { label: '提前结清失效', value: 'settleInAdvance', disabled: true },
            { label: '还款逾期失效', value: 'overdue', disabled: true },
            { label: '还款时仅钱包会员生效可享优惠', value: 'repayMembershipRestriction' },
          ],
        };
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '',
      renderFormItem: () => <h4>券面展示</h4>,
    },
    {
      title: '券面名称',
      dataIndex: 'displayTitle',
      key: 'displayTitle',
      width: 'lg',
      placeholder: '对客名称，不超过12个字',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          // { max: 12, message: '对客名称，不超过12个字' },
          {
            validator: (_, value) => {
              if (!value) return Promise.resolve();
              const actualLength = value.replace(/\./g, '').length;
              if (actualLength > 12) {
                return Promise.reject('不超过12个字符');
              }
              return Promise.resolve();
            }
          }
        ],
      },
    },
    {
      valueType: 'dependency',
      name: ['loanType'],
      columns: ({ loanType }) => [
        {
          title: '规则简介',
          dataIndex: 'briefDesc',
          key: 'briefDesc',
          valueType: 'textarea',
          width: 'lg',
          placeholder: '请输入规则简介，每行不超过24个字符',
          formItemProps: {
            rules: [
              {
                required: true,
                message: '此项为必填项',
              },
              {
                validator: (_, value) => {
                  if (!value) return Promise.resolve();
                  const lines = value.split('\n');
                  if (lines.length > 2) {
                    return Promise.reject('最多配置两行');
                  }
                  return Promise.resolve();
                }
              }
            ],
            extra: "一行不超过24个字符,可配置两行"
          },
          fieldProps: {
            disabled: (loanType?.includes('isPeriodMoneyChecked') || actionType === 'audit' || actionType === 'view')
          },
        }
      ]
    },
    {
      valueType: 'dependency',
      name: ['loanType'],
      columns: ({ loanType }) => [
        {
          title: "展开详情",
          dataIndex: "detailDesc",
          key: "detailDesc",
          formItemProps: {
            rules: [
              {
                required: true,
                message: "此项为必填项",
              },
              {
                validator(_, value) {
                  if (!value || value === '<p><br></p>' || value === '<p></p>') {
                    return Promise.reject(new Error('请输入展开详情'));
                  }
                  return Promise.resolve();
                },
              },
            ],
            extra: loanType?.includes('isPeriodMoneyChecked') ? (
              <span style={{ color: 'red' }}>第1条禁止修改</span>
            ) : null
          },
          fieldProps: {
            placeholder: "请输入展开详情内容",
          },
          renderFormItem: () => {
            return <RichTextEditor disabled={(actionType === 'audit' || actionType === 'view')} />;
          },
        }
      ]
    },
    {
      title: '券面预览',
      valueType: 'dependency',
      name: ['maxDiscountInterestAmount', 'periodType', 'specifiedPeriods', 'firstNDays', 'displayTitle', 'briefDesc', 'detailDesc', 'discountInterestRate'],
      columns: ({ maxDiscountInterestAmount, periodType, specifiedPeriods, firstNDays, displayTitle, briefDesc, detailDesc, discountInterestRate }) => {
        let couponDTO = {
          discountInterestRate,
          displayTitle,
          briefDesc,
          detailDesc,
          maxDiscountInterestAmount,
          firstNDays,
          specifiedPeriods,
          periodType
        }
        return [
          {
            title: '券面预览',
            dataIndex: '2',
            renderFormItem: () => {
              return <CouponTemplate data={couponDTO} />
            },
          }
        ]

      },

    },


  ];

  return (
    <Drawer
      title={titleMap[actionType]}
      width={'1000'}
      onClose={() => {
        resetDialog();
      }}
      destroyOnClose={true}
      open={modalVisible}
      styles={{
        body: {
          paddingBottom: 80,
        },
      }}
      footer={
        <Space>
          <Button
            onClick={() => {
              resetDialog();
            }}
          >
            {actionType === 'view' ? '关闭' : '取消'}
          </Button>
          {['edit', 'add', 'copy'].includes(actionType) && (
            <>
              <Button
                type="primary"
                onClick={() => {
                  handleSubmit();
                }}
              >
                提交
              </Button>
            </>
          )}
          {actionType === 'audit' && (
            <>
              <ModalForm
                trigger={
                  <Button type="primary" disabled={false}>
                    审核驳回
                  </Button>
                }
                title="请填写审核驳回意见"
                modalProps={{
                  destroyOnClose: true,
                  centered: true,
                }}
                onFinish={async (auditParams) => {
                  const tempAuditParams = {
                    ...auditParams,
                    id: initialConfig?.id,
                    auditOperation: 3,
                  };

                  const res = await auditInterestCoupon(tempAuditParams);

                  if (res?.code === '200') {
                    notification.success({
                      message: '操作成功',
                    });
                    resetDialog(true);
                    return true;
                  } else {
                    notification.error({
                      message: '操作失败',
                      description: res?.msg,
                    });
                  }
                }}
              >
                <BetaSchemaForm layoutType="Embed" columns={auditColumns} />
              </ModalForm>
              <Button
                type="primary"
                onClick={() => {
                  handleAudit({
                    id: initialConfig?.id,
                    auditOperation: 2,
                  });
                }}
              >
                审核通过
              </Button>
            </>
          )}
        </Space>
      }
    >
      <ConfigProvider>
        <ProForm
          formRef={formRef}
          layout="horizontal"
          submitter={false}
          // onValuesChange={(changedValues, allValues) => {
          //   setParams({ ...params, ...changedValues });
          // }}
          initialValues={initialConfig}
          disabled={formDisabled}
        >
          <BetaSchemaForm layoutType="Embed" columns={basicColumns} />
        </ProForm>
      </ConfigProvider>
      {/* 操作记录 */}
      {!noHistoryTable && (actionType === 'audit' || actionType === 'view') && (
        <OperateHistory
          key="operateHistory"
          id={initialConfig?.id}
          bizCode="template"
          disabled={false}
        />
      )}
    </Drawer>
  );
};

export default DetailModal;
