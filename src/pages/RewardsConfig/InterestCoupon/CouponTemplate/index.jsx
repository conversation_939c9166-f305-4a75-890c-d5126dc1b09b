import React, { useState, Fragment } from 'react';
import PropTypes from 'prop-types';
import styles from './style.less';

function CouponTemp({ data = {} }) {
  const [showDetail, setShowDetail] = useState(false);

  const amount = function () {
      return (
        <Fragment>
        {
          Number(data.maxDiscountInterestAmount) 
            ? <Fragment>
                <span className={styles['coupon-temp-symbol']}>¥</span> 
                {(data.maxDiscountInterestAmount)}
              
              </Fragment>
            : <Fragment>
                 {data?.periodType && (
                  data.periodType === 'firstNDaysEffect' 
                    ? <Fragment>
                        {data.firstNDays}
                        <span className={styles['coupon-temp-symbol']}>天</span>
                      </Fragment>
                    : <Fragment>
                        {data.specifiedPeriods?.length}
                        <span className={styles['coupon-temp-symbol']}>期</span>
                      </Fragment>
                )}
              </Fragment>
        }
      </Fragment>
      );
  };

  const amountAppend = function () {
      return (
        <Fragment>
          {
            data.maxDiscountInterestAmount ? '最高优惠': '优惠'
          }
        </Fragment>
      );
  };

  const renderBriefDesc = (briefDesc) => {
    return briefDesc?.split('\n').map((item) => <p key={`brief-${item}`}>{item}</p>);
  };

  return (
    <Fragment>
      <div className={styles['coupon-temp']}>
        <div className={styles['coupon-temp-left']}>
          <div className={styles['coupon-temp-left_tag']}>利息优惠券</div>
          <div className={styles['coupon-temp-left_title']}>
            {amount()}
          </div>
          <div className={styles['coupon-temp-left_append']}>
            {amountAppend()}
          </div>
          <img src="https://gaia-vivofs.vivo.com.cn/qEiqKoT2I4DBQpyI/9de077c0-3085-11ec-b74c-5f44ad75402e.png" alt=""  className={styles['coupon-temp-img']}/>
        </div>
        <div className={styles['coupon-temp-right']}>
          <div className={styles['coupon-temp-right_desc-title']}>{data.displayTitle}</div>
          <div className={styles['coupon-temp-right_desc-1']}>
            {renderBriefDesc(data.briefDesc)}
          </div>
          <div className={styles['coupon-temp-right_btn']}>去使用</div>
          <div className={styles['coupon-temp-right_date']}>
            <div className={styles['coupon-temp-right_date_start']}>
              2025-01-01
            </div>
            <div className={styles['coupon-temp-right_date_unit']}>至</div>
            <div className={styles['coupon-temp-right_date_end']}>
              2026-06-01
            </div>
          </div>
          <div className={`${styles['coupon-temp-right_arrow']} ${showDetail ? styles['coupon-temp-right_arrow-down'] : ''}`} onClick={() => setShowDetail(!showDetail)} />
        </div>
      </div>
      {
        showDetail && (
          <div 
            className={styles['coupon-temp-detail']} 
            dangerouslySetInnerHTML={{ 
              __html: `<p>使用说明：</p>\n${(data.detailDesc && data.detailDesc.toString('html')) || ''}`
            }} 
          />
        )
      }
    </Fragment>
  );
}

CouponTemp.propTypes = {
  data: PropTypes.shape({
    maxDiscountInterestAmount: PropTypes.number,
    periodType: PropTypes.string,
    firstNDays: PropTypes.number,
    specifiedPeriods: PropTypes.array,
    displayTitle: PropTypes.string,
    briefDesc: PropTypes.string,
    detailDesc: PropTypes.string,
  }),
};

export default CouponTemp;