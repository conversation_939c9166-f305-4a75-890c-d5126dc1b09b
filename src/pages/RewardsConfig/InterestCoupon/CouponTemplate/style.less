.coupon-temp {
  display: flex;
  position: relative;
  z-index: 2;

  width: 330px;
  height: 105px;
  display: flex;
  box-shadow: 0px 0.06rem 0.18rem 0px rgba(0, 0, 0, 0.05);
  position: relative;
  z-index: 3;
  border-radius: 5px;
  &-symbol {
    font-size: 12px;
  }
  &-img {
    height: 50px;
    position: absolute;
    right: 5px;
    bottom: 3px;
  }
  &-left {
    width: 111px;
    height: 105px;
    background: linear-gradient(146.84deg, #FF9549 17.34%, #FF9D61 96%);
    padding: 20px 0 0 10px;
    border-radius: 5px;
    position: relative;
    &_tag {
      position: absolute;
      top: 0;
      left: 0;
      padding: 0 5px;
      background: rgba(0, 0, 0, 0.15);
      border-radius: 5px 0 5px 0;
      color: #ffffff;
      text-indent: 0;
      font-size: 10px;
    }
    &_title {
      height: 44px;
      font-weight: 500;
      font-size: 32px;
      line-height: 44px;

      color: #FFFFFF;
    }
    &_title-icon {
      font-size: 12px;
    }
    &_title-yen {
      display: flex;
      &::before {
        content: '￥';
        font-size: 12px;
        width: 8px;
        margin-right: 2px;
        transform: translateY(5px);
      }
    }
    &_title-little {
      font-size: 26px;
    }
    &_append {
      font-size: 11px;
      color: #FFFFFF;
      line-height: 15px;
    }
  }

  &-right {
    position: relative;
    width: 250px;
    height: 105px;
    &_btn {
      position: absolute;
      background: #415fff;
      color: #fff;
      width: 55px;
      border-radius: 36px;
      right: 10px;
      top: 70px;
      text-align: center;
      font-weight: 500;
      top: 35px;
    }
    &_desc-title {
      position: absolute;
      left: 10px;
      top: 15px;
      height: 18px;

      font-weight: 500;
      font-size: 13px;
      line-height: 18px;
    }

    &_desc-1 {
      position: absolute;
      left: 10px;
      top: 38px;
      height: 15px;
      color: #999999;

      font-size: 11px;
      line-height: 15px;

      p {
        margin: 0 0;
      }
    }
    &_date {
      position: absolute;
      bottom: 5px;
      color: #999999;
      font-size: 12px;
      width: 205px;
      left: 10px;
      &_unit {
        position: absolute;
        left: 70px;
        bottom: 0px;
      }
      &_end {
        position: absolute;
        right: 50px;
        bottom: 0px;
      }
    }
    &_date::after{
      position: absolute;
      left: 0;
      top: -5px;
      width: 100%;
      height: 1px;
      content: '';
      background-image: -webkit-gradient(linear, left top, right top, from(#E0E0E0), color-stop(50%, #E0E0E0), color-stop(50%, transparent));
      background-image: -webkit-linear-gradient(left, #E0E0E0 0%, #E0E0E0 50%, transparent 50%);
      background-image: linear-gradient(to right, #E0E0E0 0%, #E0E0E0 50%, transparent 50%);
      background-size: 5px 1px;
      background-repeat: repeat-x;
    }
    &_arrow {
      position: absolute;
      width: 16px;
      height: 16px;
      right: 10px;
      bottom: 5px;

      background-image: url('https://gaia-vivofs.vivo.com.cn/qEiqKoT2I4DBQpyI/557939a0-308e-11ec-b74c-5f44ad75402e.png');
      background-repeat: no-repeat;
      background-size: 100% 100%;
      transition: all 0.3s;

      &-down {
        transform: rotate(180deg);
      }
    }
  }

  &-detail {
    position: relative;
    z-index: 1;

    width: 330px;
    border-radius: 6px;
    padding-top: 25px;
    padding-left: 16px;
    padding-bottom: 15px;
    margin-top: -13px;
    background-color: #fff;
    box-shadow: 0px 0.06rem 0.18rem 0px rgba(0, 0, 0, 0.05);
    font-weight: 300;
    font-size: 11px;
    line-height: 15px;

    color: #999999;
    
    p {
      margin: 0 0;

      &:first-child {
        margin-bottom: 6px;
      }
    }
  }
}