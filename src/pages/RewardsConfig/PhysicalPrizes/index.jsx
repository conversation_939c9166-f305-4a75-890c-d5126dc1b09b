import React, { useRef, useState, useEffect } from 'react';
import CommonTableList from '@/components/CommonTableList';
import { getInKindList, updateInKindData } from '@/services/physicalPrizes.js';
import {
  Button,
  Dropdown,
  Space,
  Tag,
  Divider,
  Popconfirm,
  Spin,
  notification,
} from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import DetailModal from './components/DetailModal/index.jsx';
import { padArray } from '@/utils/util.js';
import { Submitter } from '@ant-design/pro-components';

const PhysicalPrizes = () => {
  const actionRef = useRef();
  const [modalVisible, setModalVisible] = useState(false); // 弹窗可见性
  const [actionType, setActionType] = useState('');
  const [initialConfig, setInitialConfig] = useState({});
  const [loading, setLoading] = useState(false);

  /**
   * 列表数据请求
   */
  const getList = async (params) => {
    console.log('实物奖励列表返回数据', params);
    const param = {
      page: params.current,
      ...params,
    };

    const res = await getInKindList(param);
    const { data } = res;
    return {
      data: data.list,
      total: data.total,
    };
  };

  /**
   * 活动上下线
   * onlineStatus 1 上线 2 下线
   */
  const changeOnlineStatus = async (record) => {
    const params = {
      ...record,
      status: record.status === 1 || record.status === 3 ? 0 : 1,
      operationType: 3,
    };
    const res = await updateInKindData(params);
    if (res?.code === '200') {
      notification.success({
        message: record.status === 1 ? '下线成功' : '上线成功',
      });
      handleReload();
    }
  };

  /**
   * 列表操作
   * actionType: edit add
   */
  const handleAction = async (record, actionType) => {
    record.effectiveTime = [record.effectiveBeginTime, record.effectiveEndTime];
    setInitialConfig(record);
    setModalVisible(true);
    setActionType(actionType);
  };

  /**
   * 表格刷新
   */
  const handleReload = () => {
    actionRef.current?.reload();
  };

  const submit = async (params) => {
    const res = await updateInKindData(params);
    if (res?.code === '200' && res?.data === 1) {
      notification.success({
        message: '操作成功',
      });
      setModalVisible(false);
      handleReload();
    } else {
      setModalVisible(false);
      notification.error({
        message: res.errorMessage || '操作失败',
      });
    }
  };

  const tableColumns = [
    {
      title: '库存id',
      dataIndex: 'quotaId',
      key: 'quotaId',
      width: 120,
      fixed: 'left',
      align: 'center',
    },
    {
      title: '礼品中台活动id',
      dataIndex: 'relActiveId',
      key: 'relActiveId',
      hideInSearch: true,
      width: 150,
      align: 'center',
    },
    {
      title: '礼品id',
      dataIndex: 'relGiftIds',
      key: 'relGiftIds',
      width: 150,
      align: 'center',
    },
    {
      title: '库存名称',
      dataIndex: 'productName',
      key: 'productName',
      width: 150,
      align: 'center',
    },
    {
      title: '剩余数量',
      dataIndex: 'remainNum',
      key: 'remainNum',
      hideInSearch: true,
      width: 120,
      align: 'center',
    },
    {
      title: '库存数量',
      dataIndex: 'initTotalNum',
      key: 'initTotalNum',
      hideInSearch: true,
      width: 120,
      align: 'center',
    },
    {
      title: '已冻结数量',
      dataIndex: 'occupyNum',
      key: 'occupyNum',
      hideInSearch: true,
      width: 120,
      align: 'center',
    },
    {
      title: '已发放数量',
      dataIndex: 'occupyNum',
      key: 'occupyNum',
      hideInSearch: true,
      width: 120,
      align: 'center',
    },
    {
      title: '库存有效期',
      dataIndex: 'effectiveTime',
      key: 'effectiveTime',
      hideInSearch: true,
      valueType: 'dateTime',
      width: 200,
      align: 'center',
      render: (text, record) => {
        return (
          <span>
            {record.effectiveBeginTime} - {record.effectiveEndTime}
          </span>
        );
      },
    },
    {
      title: '操作时间',
      dataIndex: 'operationTime',
      key: 'operationTime',
      valueType: 'dateTime',
      hideInSearch: true,
      width: 200,
      align: 'center',
    },
    {
      title: '操作人',
      dataIndex: 'operationUser',
      key: 'operationUser',
      hideInSearch: true,
      width: 150,
      align: 'center',
    },
    {
      title: '库存状态',
      dataIndex: 'status',
      key: 'status',
      hideInSearch: true,
      valueType: 'select',
      valueEnum: {
        0: {
          text: '未生效',
          status: 'Error',
        },
        1: {
          text: '生效中',
          status: 'Success',
        },
        2: {
          text: '已失效',
          status: 'Default',
        },
        3: {
          text: '待生效',
          status: 'Default',
        },
      },
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
      width: 120,
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      hideInSearch: true,
      width: 200,
      fixed: 'right',
      render: (text, record) => {
        return (
          <Space split={<Divider type="vertical" />} key={record.quotaId}>
            <Button
              key="edit"
              type="link"
              size="small"
              onClick={() => {
                handleAction(record, 'edit');
              }}
            >
              更新库存
            </Button>
            {(record.status === 1 || record.status === 3) && (
              <Button
                key="offline"
                type="link"
                size="small"
                onClick={() => {
                  changeOnlineStatus(record);
                }}
              >
                下线
              </Button>
            )}
            {(record.status === 0 || record.status === 2) && (
              <Button
                key="online"
                type="link"
                size="small"
                onClick={() => {
                  changeOnlineStatus(record);
                }}
              >
                上线
              </Button>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <Spin spinning={loading}>
        <CommonTableList
          search={{ defaultCollapsed: false }}
          toolBarRender={() => [
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                handleAction({}, 'add');
              }}
            >
              导入
            </Button>,
          ]}
          columns={tableColumns}
          request={getList}
          actionRef={actionRef}
          pagination={{
            pageSize: 10,
          }}
        />
        {/* 详情弹窗 */}
        <DetailModal
          modalVisible={modalVisible}
          actionType={actionType}
          setActionType={setActionType}
          setModalVisible={setModalVisible}
          handleReload={handleReload}
          setInitialConfig={setInitialConfig}
          initialConfig={initialConfig}
          submit={submit}
        />
      </Spin>
    </>
  );
};

export default PhysicalPrizes;
