import React, { useState, useRef, useCallback, useEffect } from 'react';
import { BetaSchemaForm } from '@ant-design/pro-components';
import CommonDrawer from '@/components/CommonDrawer';
import { Tabs, message, notification, Button } from 'antd';
import dayjs from 'dayjs';
import { debounce } from '@/utils/util.js';

const DetailModal = ({
  modalVisible,
  actionType,
  setActionType,
  setModalVisible,
  handleReload,
  setInitialConfig,
  initialConfig,
  submit,
}) => {
  const formRef = useRef();
  const [tagOptions, setTagOptions] = useState([]);

  /**
   * 提交
   */
  const handleSubmit = (values) => {
    values.effectiveBeginTime = values.effectiveTime[0];
    values.effectiveEndTime = values.effectiveTime[1];
    values.operationType = actionType === 'edit' ? 2 : 1;
    values.quotaId =
      actionType === 'edit' && initialConfig ? initialConfig.quotaId : '';
    delete values.effectiveTime;
    submit(values);
  };
  /**
   * 重置数据 关闭弹窗
   */
  const resetDialog = () => {
    setModalVisible(false);
    setActionType('');
  };
  const columns = [
    {
      title: '礼品中台活动id',
      dataIndex: 'relActiveId',
      key: 'relActiveId',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      fieldProps: (form, config) => {
        return {
          maxLength: 20,
          disabled: actionType === 'edit',
        };
      },
    },
    {
      title: '礼品中台礼品id',
      dataIndex: 'relGiftIds',
      key: 'relGiftIds',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          { max: 20, message: '限制20个字符' },
        ],
      },
      fieldProps: (form, config) => {
        return {
          maxLength: 20,
          disabled: actionType === 'edit',
          placeholder: '多个skuid以英文逗号分隔',
        };
      },
    },
    {
      title: '库存名称',
      dataIndex: 'productName',
      key: 'productName',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          { max: 20, message: '限制20个字符' },
        ],
      },
      fieldProps: (form, config) => {
        return {
          maxLength: 20,
        };
      },
    },
    {
      title: '库存数量',
      dataIndex: 'initTotalNum',
      key: 'initTotalNum',
      valueType: 'digit',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      title: '有效期',
      valueType: 'dateTimeRange',
      dataIndex: 'effectiveTime',
      key: 'effectiveTime',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (value && dayjs(value[1]) > dayjs()) {
                return Promise.resolve();
              }
              return Promise.reject(new Error('结束时间必须晚于当前时间'));
            },
          }),
        ],
      },
    },
    {
      title: '库存预警',
      dataIndex: 'vchatNotifyAccount',
      key: 'vchatNotifyAccount',
      fieldProps: (form) => {
        return {
          placeholder: '请输入工号搜索',
        };
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
  ];
  return (
    <CommonDrawer
      formRef={formRef}
      title={'导入实物奖品'}
      open={modalVisible}
      onOpenChange={(value) => {
        console.log('onOpenChange的value', value);
        setModalVisible(value);
        !value && resetDialog();
      }}
      onFinish={(values) => {
        handleSubmit(values);
      }}
      layout={'horizontal'}
      formLayoutType={{
        labelCol: { span: 4 },
        wrapperCol: { span: 20 },
      }}
      initialValues={initialConfig}
      formRender={<BetaSchemaForm layoutType="Embed" columns={columns} />}
    ></CommonDrawer>
  );
};

export default DetailModal;
