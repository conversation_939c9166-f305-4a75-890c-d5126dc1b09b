export const actProductTypeMap = {
  loan: '消费贷',
  vivoCard: 'vivoCard',
  '5G': '5G',
  installmentVivoShop: '线上分期-商城',
};

export const audienceTypeMap = {
  firstTime: '全周期首次(在活动期间内，此前从未进行该场景的用户)',
  firstTimeInCampaignPeriod: '活动期间首次(在活动期间，第一次满足该场景的用户)',
  onlyAllowedCompleteOnce: '活动期间首次满足发放活动奖励条件',
};

// 选择额度升级场景时，全周期首次不可选
export const audienceFilterTypeMap = {
  firstTimeInCampaignPeriod: '活动期间首次(在活动期间，第一次满足该场景的用户)',
  onlyAllowedCompleteOnce: '活动期间首次满足发放活动奖励条件',
};

// 奖励类型
export const rewardTypeOptions = [
  {
    value: 'payCoupon',
    label: '支付券',
  },
  {
    value: 'points',
    label: '积分',
  },
  {
    value: 'coupon',
    label: '贷款优惠券',
  },
  {
    value: 'interestCoupon',
    label: '利息优惠券',
  },
  {
    value: 'mallCoupon',
    label: '商城优惠券',
  },
  {
    value: 'cashCoupon',
    label: '现金红包',
  },
  {
    value: 'gameMember',
    label: '游戏会员',
  },
  {
    value: 'videoMember',
    label: '视频会员',
  },
  {
    value: 'redPacketCoupon',
    label: '返现券',
  },
  {
    value: 'musicRedeem',
    label: '音乐会员兑换码',
  },
  {
    value: 'themeRedeem',
    label: '主题会员兑换码',
  },
  {
    value: 'outerRedeem',
    label: '兑换码（外部）',
  },
  {
    value: 'orderDeduction',
    label: '订单立减',
  },
];
