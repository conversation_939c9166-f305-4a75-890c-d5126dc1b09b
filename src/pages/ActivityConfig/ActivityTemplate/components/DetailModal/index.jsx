/* eslint-disable */
import React, { useState, useRef, useCallback, useEffect } from 'react';
import {
  BetaSchemaForm,
  ProCard,
  ProFormList,
  ProFormText,
  ProForm,
  ProFormDependency,
  ProFormSelect,
  ProFormGroup,
  ProFormDigit,
  ProFormRadio,
  ProFormCheckbox,
  EditableProTable,
  ModalForm,
  ProFormTextArea,
} from '@ant-design/pro-components';
import { PlusOutlined, UploadOutlined } from '@ant-design/icons';
import {
  actProductTypeMap,
  audienceTypeMap,
  audienceFilterTypeMap,
  rewardTypeOptions,
} from '../../../constant';
import { auditColumns } from '@/constant/config.js';
import { debounce, downloadFile } from '@/utils/util.js';
import { getDmpTag } from '@/services/activityCommon.js';
import {
  getActivityChannel,
  auditActivity,
  saveActivityDetails,
} from '@/services/activityConfig/activityTemplate.js';
import { getFundChannel } from '@/services/activityConfig/fundChannelMaintain.js';
import {
  getQuotaId,
  getRewards,
  getTemplateBrief,
  getUsePackageView,
} from '@/services/common.js';
import { titleMap } from '@/constant/config.js';
import DebounceSelect from '@/components/DebounceSelect';
import OperateHistory from '../OperateHistory';
import ActivityRecord from '../ActivityRecord';

import dayjs from 'dayjs';
import {
  Upload,
  Tabs,
  message,
  notification,
  Button,
  Row,
  Checkbox,
  Radio,
  Select,
  Col,
  InputNumber,
  DatePicker,
  Drawer,
  Form,
  Input,
  Space,
  Table,
} from 'antd';

const scenesMap = {
  loan: [
    'VivoCardApplySubmitSuccess',
    'VivoCardApplySuccess',
    'VivoCardFirstCardApplySuccess',
    '5GHandleSuccess',
    'InstallmentVivoShopOrderDeduction',
  ], // 贷款场景特殊 为取反 不包含的场景
  vivoCard: [
    'VivoCardApplySubmitSuccess',
    'VivoCardApplySuccess',
    'VivoCardFirstCardApplySuccess',
  ],
  '5G': ['5GHandleSuccess'],
  installmentVivoShop: ['InstallmentVivoShopOrderDeduction'],
};

const cashTypeOptions = [
  {
    label: '固定面额发放',
    value: 'fixed',
  },
  {
    label: '梯度面额发放',
    value: 'gradient',
  },
  {
    label: '借款金额梯度返现',
    value: 'condition',
  },
];

const strategyInit = {
  equipmentRestriction: {
    //设备限制
    enable: true,
    equipmentParam: 'unique',
  },
  amountRestriction: {
    //金额限制
    enable: false,
    minAmount: undefined,
    maxAmount: undefined,
  },
  channelRestriction: {
    enable: false,
    channels: [], //渠道限制
  },
  algorithmPredictScoreRestriction: {
    enable: false,
    algorithmPredictsName: undefined,
    minScore: undefined,
    maxScore: undefined,
  },
};

const DetailModal = ({
  modalVisible,
  setModalVisible,
  actionType,
  setActionType,
  width = '60%',
  handleReload,
  setInitialConfig,
  initialConfig,
  sceneOptions = [],
  noHistoryTable = false,
}) => {
  const formRef = useRef();
  const formDisabled = actionType === 'view' || actionType === 'audit';
  const [tagList, setTagList] = useState([]);
  const [tagOptions, setTagOptions] = useState([]);
  const [channelOptions, setChannelOptions] = useState([]);
  const [fileList, setFileList] = useState([]); // 人群包文件
  const [rewardRuleType, setRewardRuleType] = useState('partnerUnite');
  const [cashFundList, setCashFundList] = useState([]); //小额现金贷
  const [loanFundList, setLoanFundList] = useState([]); // 以贷办贷
  const [InstallFundList, setInstallFundList] = useState([]); // 分期
  const [params, setParams] = useState(initialConfig || {});
  const [userPackageData, setUserPackageData] = useState([]);
  const [supportDelete, setSupportDelete] = useState(false);

  useEffect(() => {
    getActivityChannelList();
    getFundList('10000');
    getFundList('50000');
    getFundList('60000');
  }, []);

  useEffect(() => {
    initialConfig.audienceTag &&
      getDmpTagData({ tagId: Number(initialConfig.audienceTag) });
    setParams(initialConfig);

    // 控制部分字段不可编辑
    setSupportDelete(initialConfig.supportDelete);

    // 默认奖励类型
    setRewardRuleType(initialConfig.rewardRuleType || 'partnerUnite');

    // 人群包
    if (initialConfig.fileList) {
      setFileList(initialConfig.fileList || []);
    }
    initialConfig.userType === 'userPackage' && handleUserPackage();
  }, [initialConfig]);

  /**
   * 重置数据 关闭弹窗
   */
  const resetDialog = () => {
    setModalVisible(false);
    setActionType('');
    setInitialConfig({});
    // 刷新列表
    handleReload && handleReload();
  };

  /**
   * 获取dmp详情
   */
  const getDmpTagData = async (params) => {
    if (!params.tagName && !params.tagId) return;
    const res = await getDmpTag(params);
    if (res?.code === '200') {
      setTagList(res.data);
      const options = res.data.map((item) => ({
        value: item.id + '',
        label: item.tagIdWithName,
      }));
      setTagOptions(options);
    }
  };

  /**
   * 获取人群包数据
   */

  const handleUserPackage = async () => {
    const param = {
      fileId: initialConfig.audienceTag,
    };
    const res = await getUsePackageView(param);
    if (res.code === '200' && res.data) {
      setUserPackageData(res.data?.split(','));
    }
  };

  /**
   * 获取活动限制渠道
   */

  const getActivityChannelList = async () => {
    const res = await getActivityChannel(3);
    if (res?.code === '200' && res?.data) {
      const tempList = res.data.map((channelItem) => {
        return {
          value: channelItem.channel,
          label: channelItem.channelName,
        };
      });
      setChannelOptions(tempList);
    }
  };

  //获取资金方数据
  const getFundList = async (id) => {
    const params = {
      productCode: id,
      bizCode: 'campaign',
    };
    const res = await getFundChannel(params);
    const fundOptions = ((res && res.data) || []).map((i) => ({
      label: i.displayName,
      value: i.partnerCode,
    }));
    if (id === '10000') {
      setCashFundList(fundOptions);
    } else if (id === '50000') {
      setLoanFundList(fundOptions);
    }else if (id === '60000') {
      setInstallFundList(fundOptions);
    }
  };

  // 获取库存id
  const fetchQuotaList = (param, addParam) => {
    if (param && addParam?.voucherType) {
      return getQuotaId({
        type: addParam.voucherType,
        quotaIdOrName: param,
      }).then((res) => {
        return res?.data
          ? res.data.map((item) => {
            return {
              label: item.quotaIdWithName,
              value: item.id,
            };
          })
          : [];
      });
    } else {
      return new Promise((resolve) => {
        resolve([]);
      });
    }
  };

  //获取关联奖品ID
  const getRewardList = (param, addParam) => {
    if (addParam?.quotaId) {
      return getRewards({
        quotaId: addParam.quotaId,
      }).then((res) => {
        return res?.data
          ? res.data.map((item) => {
            return {
              label: item.displayName,
              value: item.quotaDetailId,
            };
          })
          : [];
      });
    } else {
      return new Promise((resolve) => {
        resolve([]);
      });
    }
  };

  //获取返现规则id
  const getTemplateIdList = (param, addParam, form) => {
    if (param && addParam?.voucherType) {
      return getTemplateBrief({
        id: param,
        rewardType: addParam?.voucherType === 'redPacketCoupon' ? 3 : 4,
      }).then((res) => {
        return res?.data
          ? [
            {
              label: res.data.templateName,
              value: res.data.id,
            },
          ]
          : [];
      });
    } else {
      return new Promise((resolve) => {
        resolve([]);
      });
    }
  };

  // 表单提交
  const handleSubmit = async () => {
    const values = await formRef.current?.validateFields();
    const { activityTime = [], rewardRules, strategy } = values;

    // 手动校验

    // 活动限制
    // 算法预测分值
    if (strategy.algorithmPredictScoreRestriction?.enable) {
      const { minScore, maxScore } = strategy.algorithmPredictScoreRestriction;
      if (minScore !== 0 && maxScore !== 0 && !minScore && !maxScore) {
        notification.error({
          message: '提交失败',
          description: '算法预测分值最小值和最大值至少填写一个',
        });
        return;
      }

      if (minScore > maxScore) {
        notification.error({
          message: '提交失败',
          description: '算法预测分值最小值不能超过最大值',
        });
        return;
      }

      values.strategy.algorithmPredictScoreRestriction.algorithmPredictsName =
        'firstTimeBorrowPredict';
    }
    // 金额限制
    if (strategy.amountRestriction?.enable) {
      const { maxAmount, minAmount } = strategy.amountRestriction;
      if (!minAmount && !maxAmount) {
        notification.error({
          message: '提交失败',
          description: '金额限制最小值和最大值至少填写一个',
        });
        return false;
      }

      if (minAmount > maxAmount) {
        notification.error({
          message: '提交失败',
          description: '金额限制最小值和最大值至少填写一个',
        });
        return false;
      }
    }

    // 奖励配置
    // 单用户发放张数不得大于总数
    const perPersonCountExceed = rewardRules.filter((rule) => {
      if (!['cashCoupon', 'orderDeduction'].includes(rule.voucherType)) {
        return rule.perPersonCount > rule.maxCount;
      }
    });
    if (perPersonCountExceed.length > 0) {
      return notification.error({
        message: '提交失败',
        description: '单用户发放张数不得大于总数',
      });
    }

    const gradientCash = []; // 梯度面额发放校验 两者都需要填写
    let orderDeductionRuleStatus0 = false;
    let orderDeductionRuleStatus1 = false;
    let orderDeductionRuleStatus2 = false;
    let orderDeductionRuleStatus3 = false;
    rewardRules.forEach((item) => {
      // 现金红包相关配置
      if (item.voucherType === 'cashCoupon') {
        if (item.cashType === 'gradient') {
          item.cashParamList.forEach((i, index) => {
            if ((i.quota && !i.count) || (!i.quota && i.count)) {
              gradientCash.push(i);
            }
          });
        }
      }
      // 订单立减相关配置
      if (item.voucherType === 'orderDeduction') {
        item.orderDeductionRuleVOS.forEach((order) => {
          if (
            (!order.minOrderAmount && !order.maxOrderAmount) ||
            order.deductionProbabilityVOList.length < 1
          ) {
            orderDeductionRuleStatus0 = true;
            notification.error({
              message: '提交失败',
              description: '订单立减最大值、最小值至少填写一个',
            });
            return;
          }
          if (
            order.minOrderAmount &&
            order.maxOrderAmount &&
            order.minOrderAmount > order.maxOrderAmount
          ) {
            orderDeductionRuleStatus1 = true;
          }
          const probabilityLimit = order.deductionProbabilityVOList.find(
            (probabilityItem) => {
              return (
                !probabilityItem.deductionAmount || !probabilityItem.probability
              );
            },
          );
          if (probabilityLimit) {
            orderDeductionRuleStatus2 = true;
          }
          const totalProbability = order.deductionProbabilityVOList.reduce(
            (pre, probabilityItem) => {
              return probabilityItem.probability
                ? pre + probabilityItem.probability * 100
                : pre;
            },
            0,
          );

          if (totalProbability !== 10000) {
            orderDeductionRuleStatus3 = true;
          }
        });
      }
    });

    // 现金红包配置校验
    let cashExam = false;
    for (let i = 0; i < rewardRules.length; i++) {
      let {
        loanAmountList = [],
        cashParamList = [],
        cashType,
      } = rewardRules[i];
      if (cashType === 'condition') {
        if (!loanAmountList[0].loanAmount) {
          notification.error({
            message: '提交失败',
            description: '奖励配置，借款金额梯度梯度1必填',
          });
          cashExam = true;
          return false;
        }
        let mark = false;
        const tempLoanAmountList = loanAmountList.map(
          (item) => item.loanAmount,
        );
        const [
          gradient0,
          gradient1,
          gradient2,
          gradient3,
          gradient4,
          gradient5,
        ] = tempLoanAmountList;
        if (
          !gradient5 &&
          !gradient4 &&
          !gradient3 &&
          !gradient2 &&
          !gradient1 &&
          gradient0
        ) {
          mark = true;
        } else if (
          !gradient5 &&
          !gradient4 &&
          !gradient3 &&
          !gradient2 &&
          gradient0 &&
          gradient1
        ) {
          mark = true;
        } else if (
          !gradient5 &&
          !gradient4 &&
          !gradient3 &&
          gradient0 &&
          gradient1 &&
          gradient2
        ) {
          mark = true;
        } else if (
          !gradient5 &&
          !gradient4 &&
          gradient0 &&
          gradient1 &&
          gradient2 &&
          gradient3
        ) {
          mark = true;
        } else if (
          !gradient5 &&
          gradient0 &&
          gradient1 &&
          gradient2 &&
          gradient3 &&
          gradient4
        ) {
          mark = true;
        } else if (
          gradient0 &&
          gradient1 &&
          gradient2 &&
          gradient3 &&
          gradient4 &&
          gradient5
        ) {
          mark = true;
        }
        if (!mark) {
          notification.error({
            message: '提交失败',
            description: '梯度金额不能跨格填写',
          });
          cashExam = true;
          return false;
        }

        for (let i = 0; i < tempLoanAmountList.length - 1; i++) {
          if (tempLoanAmountList[i] > tempLoanAmountList[i + 1]) {
            notification.error({
              message: '提交失败',
              description: '梯度必须从小到大的顺序排列',
            });
            cashExam = true;
            return false;
          }
        }

        const loanAmountListLength = tempLoanAmountList?.filter(
          (num) => num,
        )?.length;
        const cashParamListLength = cashParamList?.filter((cashParam) => {
          return (
            cashParam.quota ||
            cashParam.count ||
            [true, false].includes(cashParam.limit)
          );
        })?.length;

        if (loanAmountListLength !== cashParamListLength) {
          notification.error({
            message: '提交失败',
            description: '梯度配置和返现配置必须数量一致',
          });
          cashExam = true;
          return false;
        }
      }
    }

    if (cashExam) {
      return;
    }

    if (gradientCash.length > 0) {
      return notification.error({
        message: '提交失败',
        description: '填写面额或数量另一项必填',
      });
    }

    if (orderDeductionRuleStatus0) {
      return notification.error({
        message: '提交失败',
        description: '订单立减最大值、最小值至少填写一个',
      });
    }

    if (orderDeductionRuleStatus1) {
      return notification.error({
        message: '提交失败',
        description: '订单最小金额不能超过订单最大金额',
      });
    }

    if (orderDeductionRuleStatus2) {
      return notification.error({
        message: '提交失败',
        description: '订单最小值、最大值 至少必填一个',
      });
    }

    if (orderDeductionRuleStatus3) {
      return notification.error({
        message: '提交失败',
        description: '一组订单金额的中奖概率必须100',
      });
    }

    let params = {
      ...initialConfig,
      ...values,
    };
    // 参数处理
    if (activityTime) {
      params.startValidity = dayjs(activityTime[0]).format(
        'YYYY-MM-DD HH:mm:ss',
      );
      params.endValidity = dayjs(activityTime[1]).format('YYYY-MM-DD HH:mm:ss');
      delete params.activityTime;
    }

    if (params.userType === 'allUsers') {
      params.fileName = '';
      params.audienceTag = '';
    } else if (params.userType === 'userPackage') {
      const fileInfo = fileList?.[0]?.response?.data || {};
      params.fileName = fileInfo.fileName;
      params.audienceTag = fileInfo.fileId;
    } else if (params.userType === 'dmpTag') {
      params.fileName = '';
    }
    if (strategy?.equipmentRestriction?.equipmentParam) {
      params.strategy.equipmentRestriction.enable = true;
    }
    const tempRewardRules = rewardRules.map((rewardRule) => {
      const { cashParamList, loanAmountList, maxCount } = rewardRule;
      if (
        rewardRule.voucherType === 'cashCoupon' &&
        rewardRule?.cashType === 'condition' &&
        rewardRule.loanAmountList
      ) {
        const tempCashParamList = cashParamList.map((cashParamItem, index) => {
          return {
            ...cashParamItem,
            down: loanAmountList[index]?.loanAmount,
            up: loanAmountList[index + 1]?.loanAmount
              ? loanAmountList[index + 1]?.loanAmount
              : undefined,
          };
        });
        rewardRule.cashParamList = tempCashParamList;
        rewardRule.maxCount = maxCount * 100;
      }
      delete rewardRule.loanAmountList;

      if (['fixed', 'gradient'].includes(rewardRule?.cashType)) {
        rewardRule.perPersonCount = 1;
      }

      if (rewardRule.voucherType === 'orderDeduction') {
        rewardRule.maxCount = maxCount * 100;
      }

      return rewardRule;
    });
    params.rewardRules = tempRewardRules;

    if (['add', 'copy'].includes(actionType)) {
      delete params.id;
    }
    // 提交
    const res = await saveActivityDetails(params);
    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
      });
      resetDialog();
    }
  };

  // 修改审核状态
  const handleAudit = async (params) => {
    const res = await auditActivity(params);

    if (res?.code === '200') {
      notification.success({
        message: '操作成功',
      });
      resetDialog();
    }
  };

  const handleChange = (info) => {
    let newFileList = [...info.fileList];

    // console.log('=====', info.fileList);

    // 1. Limit the number of uploaded files
    // Only to show two recent uploaded files, and old ones will be replaced by the new
    newFileList = newFileList.slice(-2);
    // console.log('=====', newFileList);

    // 2. Read from response and show file link
    newFileList = newFileList.map((file) => {
      if (file.response) {
        // Component will show  file.url as link
        file.url = file.response.url;
      }
      return file;
    });
    setFileList(newFileList);
    formRef.current.setFieldValue('fileList', newFileList);
  };

  const strategyColumns = [
    {
      title: '设备限制',
      valueType: 'radio',
      dataIndex: ['strategy', 'equipmentRestriction', 'equipmentParam'],
      fieldProps: (form) => {
        return {
          maxLength: 4,
          showCount: true,
          options: [
            { label: '限制单个设备只允许参加一次', value: 'unique' },
            { label: '不限制', value: 'repeatable' },
          ],
        };
      },
    },
    {
      valueType: 'group',
      dataIndex: ['strategy', 'channelRestriction'],
      columns: [
        {
          dataIndex: ['strategy', 'channelRestriction', 'enable'],
          title: '渠道限制',
          width: 'xs',
          valueType: 'radio',
          fieldProps: {
            options: [
              { label: '限制', value: true },
              { label: '不限制', value: false },
            ],
          },
        },
        {
          valueType: 'dependency',
          name: [['strategy', 'channelRestriction', 'enable']],
          columns: ({ strategy }) => {
            return strategy?.channelRestriction?.enable
              ? [
                {
                  dataIndex: ['strategy', 'channelRestriction', 'channels'],
                  valueType: 'select',
                  title: '选择渠道',
                  width: 'md',
                  fieldProps: (form) => ({
                    allowClear: true,
                    showSearch: true,
                    options: channelOptions,
                    mode: 'multiple',
                    maxCount: 1,
                    onChange: (value) => {
                      if (value.length) {
                        form.setFieldValue(
                          ['strategy', 'channelRestriction', 'enable'],
                          true,
                        );
                      } else {
                        form.setFieldValue(
                          ['strategy', 'channelRestriction', 'enable'],
                          false,
                        );
                      }
                    },
                  }),
                  formItemProps: {
                    // labelCol: { span: 4 },
                    wrapperCol: { span: 10 },
                    rules: [
                      {
                        required: true,
                        message: '此项为必填项',
                      },
                    ],
                  },
                },
              ]
              : [];
          },
        },
      ],
    },
    {
      valueType: 'group',
      dataIndex: ['strategy', 'amountRestriction'],
      columns: [
        {
          dataIndex: ['strategy', 'amountRestriction', 'enable'],
          title: '金额限制',
          width: 'xs',
          valueType: 'radio',
          fieldProps: {
            disabled:
              !['LoanSuccess', 'LoanRefused'].includes(params.scene) ||
              formDisabled,
            options: [
              { label: '限制', value: true },
              { label: '不限制', value: false },
            ],
          },
        },
        {
          valueType: 'dependency',
          name: [['strategy', 'amountRestriction', 'enable']],
          columns: ({ strategy }) => {
            return strategy?.amountRestriction?.enable
              ? [
                {
                  title: '最小值',
                  dataIndex: ['strategy', 'amountRestriction', 'minAmount'],
                  valueType: 'digit',
                  width: 'xs',
                  fieldProps: {
                    min: 1,
                    precision: 0,
                  },
                },
                {
                  title: '最大值',
                  valueType: 'digit',
                  dataIndex: ['strategy', 'amountRestriction', 'maxAmount'],
                  width: 'xs',
                  fieldProps: {
                    min: 1,
                    precision: 0,
                  },
                },
              ]
              : [];
          },
        },
      ],
    },
    {
      valueType: 'group',
      dataIndex: ['strategy', 'algorithmPredictScoreRestriction'],
      columns: [
        {
          dataIndex: ['strategy', 'algorithmPredictScoreRestriction', 'enable'],
          title: '算法预测分值限制',
          width: 'xs',
          valueType: 'radio',
          fieldProps: (form) => {
            return {
              disabled: params.scene !== 'CreditSuccess' || formDisabled,
              options: [
                { label: '限制', value: true },
                { label: '不限制', value: false },
              ],
            };
          },
        },
        {
          valueType: 'dependency',
          name: [['strategy', 'algorithmPredictScoreRestriction', 'enable']],
          columns: ({ strategy }) => {
            return strategy?.algorithmPredictScoreRestriction?.enable
              ? [
                {
                  title: '首借预测-最小值',
                  dataIndex: [
                    'strategy',
                    'algorithmPredictScoreRestriction',
                    'minScore',
                  ],
                  valueType: 'digit',
                  width: 'xs',
                  fieldProps: {
                    min: 0,
                    max: 1,
                    maxLength: 6,
                  },
                  formItemProps: {
                    extra: '填0-1区间的值,最多4位小数，包含0-1可填',
                  },
                },
                {
                  title: '首借预测-最大值',
                  valueType: 'digit',
                  dataIndex: [
                    'strategy',
                    'algorithmPredictScoreRestriction',
                    'maxScore',
                  ],
                  width: 'xs',
                  fieldProps: {
                    min: 0,
                    max: 1,
                    maxLength: 6,
                  },
                  formItemProps: {
                    extra: '填0-1区间的值,最多4位小数，包含0-1可填',
                  },
                },
              ]
              : [];
          },
        },
      ],
    },
  ];

  const columns = [
    {
      title: '活动名称',
      dataIndex: 'name',
      key: 'name',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          { max: 20, message: '限制20个字符' },
        ],
        extra: '内部命名，限制20个字符, 不对外显示',
      },
      fieldProps: (form, config) => {
        return {
          maxLength: 20,
          showCount: true,
        };
      },
    },
    {
      title: '产品类型',
      dataIndex: 'productType',
      key: 'productType',
      valueType: 'select',
      valueEnum: actProductTypeMap,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      fieldProps: (form) => {
        return {
          onChange: (e) => {
            form.setFieldValue('scene', '');
            form.setFieldValue('rewardRuleType', 'partnerUnite');
            setRewardRuleType('partnerUnite');
            // 切换清空活动规则
            formRef.current.setFieldValue('rewardRules', [
              { rewardAlarmConfigVO: { threshold: 20 } },
            ]);
            formRef.current.setFieldValue('strategy', strategyInit);
          },
        };
      },
    },
    {
      valueType: 'dependency',
      name: ['productType'],
      columns: ({ productType }) => {
        return [
          {
            title: '活动场景',
            dataIndex: 'scene',
            key: 'scene',
            valueType: 'select',
            fieldProps: (form) => {
              return {
                options: sceneOptions.filter((item) => {
                  if (!productType) return true;
                  if (productType === 'loan') {
                    return !scenesMap[productType]?.includes(item.value);
                  } else {
                    return scenesMap[productType]?.includes(item.value);
                  }
                }),
                onChange: (value) => {
                  // 活动用户设置
                  if (
                    [
                      'CreditSuccess',
                      'LoanSuccess',
                      'FaceIdentification',
                    ].includes()
                  ) {
                    form.setFieldValue('userType', 'dmpTag');
                  } else {
                    form.setFieldValue('userType', 'allUsers');
                  }
                  // 切换清空活动规则
                  if (value === 'InstallmentVivoShopOrderDeduction') { // // 分期立减活动 处理 installmentVivoShop 的特殊场景
                    setRewardRuleType('partnerNotUnite');
                    form.setFieldValue('rewardRuleType', 'partnerNotUnite');
                  } else {
                    setRewardRuleType('partnerUnite');
                    form.setFieldValue('rewardRuleType', 'partnerUnite');
                  }
                  form.setFieldValue('rewardRules', [
                    { rewardAlarmConfigVO: { threshold: 20 } },
                  ]);
                  formRef.current.setFieldValue('rewardRules', [
                    { rewardAlarmConfigVO: { threshold: 20 } },
                  ]);
                  formRef.current.setFieldValue('strategy', strategyInit);
                  if (value === 'FaceIdentification') {
                    form.setFieldValue('audienceType', 'firstTime');
                  }
                },
                disabled: supportDelete !== 1 || formDisabled,
              };
            },
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
              ],
            },
          },
        ];
      },
    },
    {
      title: '活动用户',
      dataIndex: 'userType',
      key: 'userType',
      valueType: 'radio',
      fieldProps: (form) => {
        return {
          options: [
            { label: '按用户标签', value: 'dmpTag' },
            { label: '人群包', value: 'userPackage' },
            { label: '所有用户', value: 'allUsers' },
          ],
          onChange: (e) => {
            form.setFieldValue('userTypeName', '');
            form.setFieldValue('audienceTag', '');
            setFileList([]);
          },
        };
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
    },
    {
      valueType: 'dependency',
      name: ['userType'],
      columns: ({ userType }) => {
        return userType === 'userPackage'
          ? [
              {
                title: '上传文件(txt)',
                key: 'fileList',
                dataIndex: 'fileList',
                renderFormItem: (schema, config, form) => {
                  const props = {
                    // action: 'https://660d2bd96ddfa2943b33731c.mockapi.io/api/upload',
                    action: `${API_BASE_URL}/coupon/campaign/upload/back-up`,
                    maxCount: 1,
                    accept: '.txt',
                    onChange: handleChange,
                    multiple: true,
                  };
                  return (
                    <>
                      <Upload
                        {...props}
                        fileList={fileList}
                        withCredentials={true}
                      >
                        <Button icon={<UploadOutlined />}>上传文件</Button>
                      </Upload>
                      {formDisabled && (
                        <Space>
                          <ModalForm
                            trigger={
                              <Button type="link" disabled={false}>
                                查看预览
                              </Button>
                            }
                            title="人群包配置"
                            modalProps={{
                              destroyOnClose: true,
                              centered: true,
                            }}
                            submitter={false}
                          >
                            <div
                              style={{
                                height: 300,
                                width: '100%',
                                overflow: 'scroll',
                              }}
                            >
                              {userPackageData.map((item) => {
                                return <Row key={item}>{item}</Row>;
                              })}
                            </div>
                          </ModalForm>
                          <Button
                            type="link"
                            disabled={false}
                            onClick={() => {
                              downloadFile(fileList[0]?.response?.data?.fileId);
                            }}
                          >
                            下载文件
                          </Button>
                        </Space>
                      )}
                    </>
                  );
                },
                formItemProps: {
                  rules: [
                    {
                      required: true,
                      message: '此项为必填项',
                    },
                  ],
                },
              },
            ]
          : userType === 'dmpTag'
            ? [
              {
                title: '用户标签',
                dataIndex: 'audienceTag',
                key: 'audienceTag',
                valueType: 'select',
                fieldProps: (form) => {
                  return {
                    onSearch: debounce((value) => {
                      getDmpTagData({ tagName: value });
                    }, 500),
                    showSearch: true,
                    options: tagOptions,
                  };
                },
                formItemProps: {
                  rules: [
                    {
                      required: true,
                      message: '此项为必填项',
                    },
                  ],
                },
              },
            ]
            : [];
      },
    },
    {
      valueType: 'dependency',
      name: ['scene'],
      columns: ({ scene }) => {
        return [
          {
            title: '活动对象',
            dataIndex: 'audienceType',
            key: 'audienceType',
            valueType: 'select',
            width: 'L',
            valueEnum:
              scene === 'LimitUpgradeSuccess'
                ? audienceFilterTypeMap
                : audienceTypeMap,
            fieldProps: {
              disabled: scene === 'FaceIdentification' || formDisabled,
            },
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
              ],
            },
          },
        ];
      },
    },
    {
      title: '活动时间',
      valueType: 'dateTimeRange',
      dataIndex: 'activityTime',
      key: 'activityTime',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
          ({ getFieldValue }) => ({
            validator(_, value) {
              if (
                !value ||
                (value &&
                  dayjs(value[1]) > dayjs() &&
                  dayjs(value[0]) > dayjs())
              ) {
                return Promise.resolve();
              }
              return Promise.reject(
                new Error('开始时间/结束时间必须晚于当前时间'),
              );
            },
          }),
        ],
      },
    },
    {
      title: '活动限制',
      dataIndex: 'strategy',
      key: 'strategy',
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      renderFormItem: (schema, config, form) => {
        // return <ProCard bordered>
        //   <BetaSchemaForm layoutType="Embed" columns={strategyColumns} />
        // </ProCard>;
        // ----上面这样写有bug 不能有外层包裹-----
        return <BetaSchemaForm layoutType="Embed" columns={strategyColumns} />;
      },
    },
    {
      title: '营销文案',
      dataIndex: 'marketingText',
      key: 'marketingText',
      formItemProps: {
        rules: [{ max: 10, message: '限制10个字符' }],
        extra:
          '不超过10个字符，对客展示。仅分期订单立减活动可配置，其他活动配置不生效。',
      },
      fieldProps: (form, config) => {
        return {
          maxLength: 10,
          showCount: true,
        };
      },
    },
    {
      valueType: 'dependency',
      name: ['productType', 'scene'],
      columns: ({ productType, scene }) => {
        return [
          {
            title: '奖励方式',
            dataIndex: 'rewardRuleType',
            key: 'rewardRuleType',
            valueType: 'radio',
            initialValue: 'partnerUnite',
            fieldProps: (form) => {
              return {
                options: [
                  { label: '合作方统一规则', 
                    value: 'partnerUnite',  
                    disabled: productType === 'installmentVivoShop' && scene === 'InstallmentVivoShopOrderDeduction' },
                  {
                    label: '合作方不同规则',
                    value: 'partnerNotUnite',
                    disabled: [
                      'CreditSubmitSuccess',
                      'CreditRefused',
                      'FaceIdentification',
                    ].includes(scene),
                  },
                ],
                onChange: (e) => {
                  setRewardRuleType(e.target.value);
                  formRef.current.setFieldValue('rewardRules', [
                    { rewardAlarmConfigVO: {} },
                  ]);
                },
                disabled: productType === 'vivoCard' || formDisabled,
              };
            },
            formItemProps: {
              rules: [
                {
                  required: true,
                  message: '此项为必填项',
                },
              ],
            },
          },
        ];
      },
    },
  ];

  return (
    <Drawer
      title={titleMap[actionType]}
      width={'1000'}
      onClose={() => {
        resetDialog();
      }}
      destroyOnClose={true}
      open={modalVisible}
      styles={{
        body: {
          paddingBottom: 80,
        },
      }}
      footer={
        <Space>
          <Button
            onClick={() => {
              resetDialog();
            }}
          >
            {actionType === 'view' ? '关闭' : '取消'}
          </Button>
          {['edit', 'add', 'copy'].includes(actionType) && (
            <>
              <Button
                type="primary"
                onClick={() => {
                  handleSubmit();
                }}
              >
                提交
              </Button>
            </>
          )}
          {actionType === 'audit' && (
            <>
              <ModalForm
                trigger={
                  <Button type="primary" disabled={false}>
                    审核驳回
                  </Button>
                }
                title="请填写审核驳回意见"
                modalProps={{
                  destroyOnClose: true,
                  centered: true,
                }}
                onFinish={async (auditParams) => {
                  const tempAuditParams = {
                    ...auditParams,
                    id: initialConfig?.id,
                    auditOperation: 3,
                  };

                  const res = await auditActivity(tempAuditParams);

                  if (res?.code === '200') {
                    notification.success({
                      message: '操作成功',
                    });
                    resetDialog();
                    return true;
                  } else {
                    notification.error({
                      message: '操作失败',
                      description: res?.msg,
                    });
                  }
                }}
              >
                <BetaSchemaForm layoutType="Embed" columns={auditColumns} />
              </ModalForm>
              <Button
                type="primary"
                onClick={() => {
                  handleAudit({
                    id: initialConfig?.id,
                    auditOperation: 2,
                  });
                }}
              >
                审核通过
              </Button>
            </>
          )}
        </Space>
      }
    >
      <ProForm
        formRef={formRef}
        layout="horizontal"
        submitter={false}
        onValuesChange={(changedValues, allValues) => {
          setParams({ ...params, ...changedValues });
        }}
        initialValues={initialConfig}
        disabled={formDisabled}
      >
        <BetaSchemaForm layoutType="Embed" columns={columns} />
        <ProFormList
          name={['rewardRules']}
          label="奖励规则"
          initialValue={[{}]}
          onChange={(value) => {
            // console.log('奖励规则====value====', value);
          }}
          creatorButtonProps={
            rewardRuleType === 'partnerUnite'
              ? false
              : {
                creatorButtonText: '新增奖励配置',
                disabled: supportDelete !== 1,
              }
          }
          min={1}
          rules={[{ required: true, message: '这是必填项' }]}
          alwaysShowItemLabel={true}
          copyIconProps={false}
          itemRender={({ listDom, action }, { index }) => (
            <ProCard
              bordered
              style={{ marginBlockEnd: 8, width: '100%' }}
              title={
                rewardRuleType === 'partnerNotUnite'
                  ? `奖励配置${index + 1}`
                  : '奖励配置'
              }
            // extra={rewardRuleType === 'partnerNotUnite' ? action : ''} // 在这写切换不刷新
            >
              {listDom}
            </ProCard>
          )}
        >
          {(f, index, action) => {
            const currentRowData = action.getCurrentRowData();
            return (
              <>
                {rewardRuleType === 'partnerNotUnite' && (
                  <Row justify="end">
                    <Button
                      type="link"
                      onClick={() => action.remove(index)}
                      disabled={formDisabled || supportDelete !== 1}
                    >
                      删除
                    </Button>
                  </Row>
                )}
                {rewardRuleType === 'partnerNotUnite' && (
                  <ProForm.Group title="按资金方">
                    <ProFormDependency name={['productType', 'scene']}>
                    {(formData) => {
                      const productType = formRef.current?.getFieldValue('productType');
                      const scene = formRef.current?.getFieldValue('scene');
                      const isInstallmentVivoShopOrderDeduction = 
                        productType === 'installmentVivoShop' && 
                        scene === 'InstallmentVivoShopOrderDeduction';
                      
                      return isInstallmentVivoShopOrderDeduction ? (
                        <ProFormSelect
                          name={['partnerCodes', '60000']}
                          width="s"
                          mode='multiple'
                          label="分期"
                          options={InstallFundList}
                        />
                      ) : (
                        <>
                          <ProFormSelect
                            name={['partnerCodes', '10000']}
                            width="s"
                            mode='multiple'
                            label="小额现金贷"
                            options={cashFundList}
                          />
                          <ProFormSelect
                            name={['partnerCodes', '50000']}
                            width="s"
                            mode="multiple"
                            label="以贷办贷"
                            options={loanFundList}
                          />
                        </>
                      );
                    }}
                  </ProFormDependency>
                </ProForm.Group>
                )}
                <ProForm.Group title="奖励类型">
                  <ProFormDigit
                    width="xs"
                    name={['rewardAlarmConfigVO', 'threshold']}
                    label="库存预警"
                    initialValue={20}
                    min={1}
                    max={100}
                    fieldProps={{ precision: 0 }}
                    addonBefore="低于"
                    addonAfter="%， 则发起提醒"
                  />
                  <ProFormText
                    name={['rewardAlarmConfigVO', 'notifyUserIds']}
                    label="预警通知人"
                    placeholder={'通知人工号，多个以逗号隔开'}
                  />
                </ProForm.Group>
                <ProForm.Group>
                  <ProFormSelect
                    name="voucherType"
                    width="s"
                    label="奖励类型"
                    options={rewardTypeOptions}
                    rules={[{ required: true, message: '这是必填项' }]}
                    onChange={(value) => {
                      action.setCurrentRowData({ quotaId: undefined });
                      action.setCurrentRowData({ quotaDetailId: undefined });
                      action.setCurrentRowData({ templateId: undefined });
                      action.setCurrentRowData({ perPersonCount: undefined });
                      action.setCurrentRowData({ maxCount: undefined });
                    }}
                    disabled={formDisabled || supportDelete !== 1}
                  />
                  <ProFormDependency name={['voucherType']}>
                    {({ voucherType }) => {
                      if (voucherType === 'orderDeduction') {
                        return;
                      }
                      return (
                        <>
                          <ProForm.Item
                            name="quotaId"
                            label="库存id"
                            rules={[{ required: true, message: '这是必填项' }]}
                          >
                            <DebounceSelect
                              showSearch={true}
                              clearOnAddParamChange={true}
                              addParam={{
                                voucherType,
                                initialValue: currentRowData.quotaId,
                              }}
                              fetchOptions={fetchQuotaList}
                              disabled={formDisabled || supportDelete !== 1}
                              style={{
                                width: 200,
                              }}
                              allowClear={true}
                            />
                          </ProForm.Item>
                        </>
                      );
                    }}
                  </ProFormDependency>
                  <ProFormDependency name={['voucherType', 'quotaId']}>
                    {({ voucherType, quotaId }) => {
                      if (voucherType === 'coupon') {
                        return (
                          <ProForm.Item
                            name="quotaDetailId"
                            label="关联奖品ID"
                            rules={[{ required: true, message: '这是必填项' }]}
                          >
                            <DebounceSelect
                              addParam={{ voucherType, quotaId }}
                              debounceTimeout={500}
                              setOptionByAddParam={true}
                              fetchOptions={getRewardList}
                              style={{
                                width: 200,
                              }}
                              allowClear={true}
                              disabled={formDisabled || supportDelete !== 1}
                            />
                          </ProForm.Item>
                        );
                      }
                    }}
                  </ProFormDependency>
                  <ProFormDependency name={['voucherType', 'quotaId']}>
                    {({ voucherType }) => {
                      if (voucherType === 'redPacketCoupon' || voucherType === 'interestCoupon') {
                        return (
                          <ProForm.Item
                            name="templateId"
                            label="券规则ID"
                            rules={[{ required: true, message: '这是必填项' }]}
                          >
                            <DebounceSelect
                              addParam={{ voucherType }}
                              showSearch={true}
                              clearOnAddParamChange={true}
                              fetchOptions={getTemplateIdList}
                              style={{
                                width: 200,
                              }}
                              allowClear={true}
                              disabled={formDisabled || supportDelete !== 1}
                            />
                          </ProForm.Item>
                        );
                      }
                    }}
                  </ProFormDependency>
                  <ProFormDependency name={['voucherType']}>
                    {({ voucherType }) => {
                      if (
                        !['coupon', 'redPacketCoupon', 'interestCoupon'].includes(voucherType)
                      ) {
                        return;
                      }
                      return (
                        <ProFormDigit
                          width="xs"
                          name="validDaysAfterReceive"
                          label="优惠券有效期"
                          min={1}
                          fieldProps={{ precision: 0 }}
                          addonBefore="自领取后"
                          addonAfter="天内有效(填正整数)"
                          rules={[{ required: true, message: '这是必填项' }]}
                        />
                      );
                    }}
                  </ProFormDependency>
                  <ProFormDependency name={['voucherType']}>
                    {({ voucherType }) => {
                      if (voucherType === 'orderDeduction') {
                        return (
                          <>
                            <ProFormDigit
                              width="xs"
                              name="maxCount"
                              label="预算金额"
                              min={1}
                              addonAfter="元"
                              fieldProps={{ precision: 0 }}
                              rules={[
                                { required: true, message: '这是必填项' },
                              ]}
                            />
                            <ProFormList
                              name="orderDeductionRuleVOS"
                              label="订单立减配置"
                              creatorButtonProps={{
                                creatorButtonText: '增加一行订单金额',
                              }}
                              min={1}
                              copyIconProps={false}
                              itemRender={({ listDom, action }, { index }) => (
                                <ProCard
                                  bordered
                                  style={{ marginBlockEnd: 8 }}
                                  title={`立减配置${index + 1}`}
                                  extra={action}
                                  bodyStyle={{ paddingBlockEnd: 0 }}
                                >
                                  {listDom}
                                </ProCard>
                              )}
                              creatorRecord={{
                                maxOrderAmount: undefined,
                                minOrderAmount: undefined,
                                deductionProbabilityVOList: [
                                  {
                                    deductionAmount: undefined,
                                    probability: undefined,
                                  },
                                ],
                              }}
                              initialValue={[
                                {
                                  maxOrderAmount: undefined,
                                  minOrderAmount: undefined,
                                  deductionProbabilityVOList: [
                                    {
                                      deductionAmount: undefined,
                                      probability: undefined,
                                    },
                                  ],
                                },
                              ]}
                            >
                              <ProFormGroup>
                                <ProFormDigit
                                  width="xs"
                                  name="minOrderAmount"
                                  label="订单金额最小值"
                                  min={0.01}
                                  addonAfter="元"
                                  fieldProps={{ precision: 2 }}
                                  tooltip="订单最小值、最大值 至少必填一个"
                                />
                                <ProFormDigit
                                  width="xs"
                                  name="maxOrderAmount"
                                  label="订单金额最大值"
                                  min={0.01}
                                  addonAfter="元"
                                  fieldProps={{ precision: 2 }}
                                  tooltip="订单最小值、最大值 至少必填一个"
                                />
                                <ProForm.Item
                                  isListField
                                  style={{ marginBlockEnd: 0 }}
                                >
                                  <ProFormList
                                    label="中奖概率配置"
                                    tooltip="单组金额总概率必须为100%"
                                    name="deductionProbabilityVOList"
                                    creatorButtonProps={{
                                      creatorButtonText: '新增立减配置',
                                      icon: false,
                                      type: 'link',
                                      style: { width: 'unset' },
                                    }}
                                    min={1}
                                    rules={[
                                      { required: true, message: '这是必填项' },
                                    ]}
                                    copyIconProps={false}
                                    alwaysShowItemLabel={true}
                                    deleteIconProps={{ tooltipText: '删除' }}
                                    itemRender={({ listDom, action }) => (
                                      <div
                                        style={{
                                          display: 'flex',
                                          marginInlineEnd: 25,
                                        }}
                                      >
                                        {listDom}
                                        {action}
                                      </div>
                                    )}
                                  >
                                    {(meta2, index2, action2, count) => {
                                      return (
                                        <ProFormGroup>
                                          <ProFormDigit
                                            label={`立减金额${index2 + 1}`}
                                            allowClear={false}
                                            width="xs"
                                            name={['deductionAmount']}
                                            min={0.01}
                                            fieldProps={{ precision: 2 }}
                                            addonAfter="元"
                                            rules={[
                                              {
                                                required: true,
                                                message: '这是必填项',
                                              },
                                            ]}
                                          />
                                          <ProFormDigit
                                            label={`中奖概率${index2 + 1}`}
                                            allowClear={false}
                                            width="xs"
                                            name={['probability']}
                                            min={0.01}
                                            max={100}
                                            fieldProps={{ precision: 2 }}
                                            addonAfter="%"
                                            rules={[
                                              {
                                                required: true,
                                                message: '这是必填项',
                                              },
                                            ]}
                                          />
                                        </ProFormGroup>
                                      );
                                    }}
                                  </ProFormList>
                                </ProForm.Item>
                              </ProFormGroup>
                            </ProFormList>
                          </>
                        );
                      }
                    }}
                  </ProFormDependency>
                </ProForm.Group>
                <ProForm.Group>
                  <ProFormDependency name={['voucherType']}>
                    {({ voucherType }) => {
                      if (
                        ['cashCoupon', 'orderDeduction'].includes(voucherType)
                      ) {
                        return;
                      }
                      return (
                        <ProFormDigit
                          width="xs"
                          name="perPersonCount"
                          label="单用户发放"
                          min={1}
                          addonAfter="张"
                          fieldProps={{ precision: 0 }}
                          rules={[{ required: true, message: '这是必填项' }]}
                        />
                      );
                    }}
                  </ProFormDependency>
                  <ProFormDependency name={['voucherType']}>
                    {({ voucherType }) => {
                      if (
                        ['cashCoupon', 'orderDeduction'].includes(voucherType)
                      ) {
                        return;
                      }
                      return (
                        <ProFormDigit
                          width="xs"
                          name="maxCount"
                          label="活动上限发放总张数"
                          min={1}
                          fieldProps={{ precision: 0 }}
                          rules={[{ required: true, message: '这是必填项' }]}
                        />
                      );
                    }}
                  </ProFormDependency>

                  {
                    <ProFormDependency name={['voucherType', 'scene']}>
                      {({ voucherType }) => {
                        if (voucherType === 'cashCoupon') {
                          const tempOptions = cashTypeOptions.filter((item) => {
                            if (params.scene !== 'LoanSuccess')
                              return item.value !== 'condition';
                            return true;
                          });
                          return (
                            <>
                              <ProFormRadio.Group
                                name="cashType"
                                label="发放方式"
                                options={tempOptions}
                                rules={[
                                  { required: true, message: '这是必填项' },
                                ]}
                                onChange={(e) => {
                                  const initOptionMap = {
                                    fixed: [{}],
                                    gradient: [{}, {}],
                                    condition: [{}, {}, {}, {}, {}, {}],
                                  };
                                  action.setCurrentRowData({
                                    cashParamList:
                                      initOptionMap[e.target.value],
                                  });
                                  action.setCurrentRowData({
                                    loanAmountList: [{}, {}, {}, {}, {}, {}],
                                  });
                                  action.setCurrentRowData({
                                    maxCount: undefined,
                                  });
                                }}
                              />
                              <ProFormDependency name={['cashType']}>
                                {({ cashType }) => {
                                  if (
                                    cashType === 'fixed' ||
                                    cashType === 'gradient'
                                  ) {
                                    return (
                                      <ProFormList
                                        title="面额配置"
                                        key="fixedList"
                                        name="cashParamList"
                                        max={cashType === 'fixed' ? 1 : 5}
                                        min={cashType === 'fixed' ? 1 : 2}
                                        initialValue={
                                          cashType === 'fixed' ? [{}] : [{}, {}]
                                        }
                                        creatorButtonProps={
                                          cashType === 'fixed'
                                            ? false
                                            : {
                                              creatorButtonText: '新增额度',
                                            }
                                        }
                                        copyIconProps={false}
                                        alwaysShowItemLabel={true}
                                      >
                                        {(
                                          // 当前行的基本信息 {name: number; key: number}
                                          cashParamListMeta1,
                                          // 当前的行号
                                          cashParamListIndex1,
                                          cashParamListAction1,
                                          // 总行数
                                          cashParamListCount1,
                                        ) => {
                                          return (
                                            <>
                                              {cashType === 'gradient' && (
                                                <ProFormGroup>
                                                  <ProFormDigit
                                                    width="xs"
                                                    name="quota"
                                                    label={`面额${cashParamListIndex1 + 1
                                                      }`}
                                                    min={0.01}
                                                    fieldProps={{
                                                      precision: 2,
                                                    }}
                                                    addonAfter="元"
                                                    rules={[
                                                      {
                                                        required:
                                                          cashParamListIndex1 <
                                                            2
                                                            ? true
                                                            : false,
                                                        message: '这是必填项',
                                                      },
                                                    ]}
                                                  />
                                                  <ProFormDigit
                                                    width="xs"
                                                    name="count"
                                                    label="数量"
                                                    min={1}
                                                    fieldProps={{
                                                      precision: 0,
                                                    }}
                                                    rules={[
                                                      {
                                                        required:
                                                          cashParamListIndex1 <
                                                            2
                                                            ? true
                                                            : false,
                                                        message: '这是必填项',
                                                      },
                                                    ]}
                                                  />
                                                </ProFormGroup>
                                              )}
                                              {cashType === 'fixed' && (
                                                <ProFormGroup>
                                                  <ProFormDigit
                                                    width="xs"
                                                    name="quota"
                                                    label="单人发放面额"
                                                    min={0.01}
                                                    fieldProps={{
                                                      precision: 2,
                                                    }}
                                                    addonAfter="元"
                                                    rules={[
                                                      {
                                                        required: true,
                                                        message: '这是必填项',
                                                      },
                                                    ]}
                                                  />
                                                  <ProFormDigit
                                                    width="xs"
                                                    name="count"
                                                    label="活动上限总数量"
                                                    min={1}
                                                    fieldProps={{
                                                      precision: 0,
                                                    }}
                                                    addonAfter="个"
                                                    rules={[
                                                      {
                                                        required: true,
                                                        message: '这是必填项',
                                                      },
                                                    ]}
                                                  />
                                                </ProFormGroup>
                                              )}
                                            </>
                                          );
                                        }}
                                      </ProFormList>
                                    );
                                  }
                                  if (cashType === 'condition') {
                                    return (
                                      <div>
                                        <ProFormDigit
                                          width="xs"
                                          name="maxCount"
                                          label="活动发放金额上限"
                                          addonAfter="元"
                                          min={1}
                                          fieldProps={{ precision: 0 }}
                                          rules={[
                                            {
                                              required: true,
                                              message: '这是必填项',
                                            },
                                          ]}
                                        />
                                        <ProFormList
                                          name="loanAmountList"
                                          label="借款金额梯度"
                                          initialValue={[
                                            undefined,
                                            undefined,
                                            undefined,
                                            undefined,
                                            undefined,
                                            undefined,
                                          ]}
                                          copyIconProps={false}
                                          creatorButtonProps={false}
                                          min={1}
                                          max={6}
                                          rules={[
                                            {
                                              required: true,
                                              message: '这是必填项',
                                            },
                                          ]}
                                          itemRender={(
                                            { listDom, action },
                                            { index, fields },
                                          ) => (
                                            <div
                                              style={{ display: 'inline-flex' }}
                                            >
                                              {listDom}
                                              <div
                                                style={{ padding: '0 10px' }}
                                              >
                                                —
                                              </div>
                                              {index + 1 === fields.length && (
                                                <div style={{ paddingTop: 2 }}>
                                                  max
                                                </div>
                                              )}
                                            </div>
                                          )}
                                        >
                                          {(
                                            // 当前行的基本信息 {name: number; key: number}
                                            loanAmountMeta,
                                            // 当前的行号
                                            loanAmountIndex,
                                            loanAmountAction,
                                            // 总行数
                                            loanAmountCount,
                                          ) => {
                                            return (
                                              <ProFormDigit
                                                width="xs"
                                                name="loanAmount"
                                                addonAfter="元"
                                                min={1}
                                                fieldProps={{ precision: 0 }}
                                                onChange={(value) => { }}
                                              />
                                            );
                                          }}
                                        </ProFormList>
                                        <ProFormDependency
                                          name={['loanAmountList']}
                                        >
                                          {({ loanAmountList = [] }) => {
                                            return (
                                              <ProFormList
                                                name="cashParamList"
                                                label="返现配置"
                                                creatorButtonProps={{
                                                  creatorButtonText:
                                                    '添加梯度返现配置',
                                                }}
                                                rules={[
                                                  {
                                                    required: true,
                                                    message: '这是必填项',
                                                  },
                                                ]}
                                                min={1}
                                                max={6}
                                                copyIconProps={false}
                                                itemRender={(
                                                  { listDom, action },
                                                  { index },
                                                ) => (
                                                  <ProCard
                                                    bordered
                                                    style={{
                                                      marginBlockEnd: 8,
                                                    }}
                                                    bodyStyle={{
                                                      paddingBlockEnd: 0,
                                                    }}
                                                  >
                                                    {listDom}
                                                  </ProCard>
                                                )}
                                                creatorRecord={{
                                                  down: undefined,
                                                  up: undefined,
                                                  limit: undefined,
                                                  count: undefined,
                                                }}
                                              >
                                                {(
                                                  // 当前行的基本信息 {name: number; key: number}
                                                  cashParamListMeta2,
                                                  // 当前的行号
                                                  cashParamListIndex2,
                                                  cashParamListAction2,
                                                  // 总行数
                                                  cashParamListCount2,
                                                ) => {
                                                  return (
                                                    <ProFormGroup>
                                                      <Row>
                                                        <div
                                                          style={{
                                                            margin: '4px 5px 0',
                                                          }}
                                                        >
                                                          {cashParamListIndex2 +
                                                            1 <
                                                            cashParamListCount2 && (
                                                              <div>
                                                                {
                                                                  loanAmountList[
                                                                    cashParamListIndex2
                                                                  ]?.loanAmount
                                                                }
                                                                ≤借款金额＜
                                                                {
                                                                  loanAmountList[
                                                                    cashParamListIndex2 +
                                                                    1
                                                                  ]?.loanAmount
                                                                }
                                                              </div>
                                                            )}
                                                          {cashParamListIndex2 +
                                                            1 ===
                                                            cashParamListCount2 && (
                                                              <div>
                                                                {
                                                                  loanAmountList[
                                                                    cashParamListIndex2
                                                                  ]?.loanAmount
                                                                }
                                                                ≤借款金额＜ max
                                                              </div>
                                                            )}
                                                        </div>
                                                        <ProFormDigit
                                                          width="xs"
                                                          name="quota"
                                                          label={`返现金额${cashParamListIndex2 +
                                                            1
                                                            }`}
                                                          addonAfter="元"
                                                          min={0.01}
                                                          fieldProps={{
                                                            precision: 2,
                                                          }}
                                                          rules={[
                                                            {
                                                              required:
                                                                loanAmountList[
                                                                  cashParamListIndex2
                                                                ]?.loanAmount
                                                                  ? true
                                                                  : false,
                                                              message:
                                                                '这是必填项',
                                                            },
                                                          ]}
                                                        />
                                                        <ProFormRadio.Group
                                                          name="limit"
                                                          label="限制个数"
                                                          options={[
                                                            {
                                                              label: '是',
                                                              value: true,
                                                            },
                                                            {
                                                              label: '否',
                                                              value: false,
                                                            },
                                                          ]}
                                                          rules={[
                                                            {
                                                              required:
                                                                loanAmountList[
                                                                  cashParamListIndex2
                                                                ]?.loanAmount
                                                                  ? true
                                                                  : false,
                                                              message:
                                                                '这是必填项',
                                                            },
                                                          ]}
                                                        />
                                                        <ProFormDependency
                                                          name={['limit']}
                                                        >
                                                          {({ limit }) => {
                                                            return (
                                                              limit && (
                                                                <ProFormDigit
                                                                  width="xs"
                                                                  name="count"
                                                                  label="限制最多"
                                                                  addonAfter="个"
                                                                  min={1}
                                                                  fieldProps={{
                                                                    precision: 0,
                                                                  }}
                                                                  rules={[
                                                                    {
                                                                      required: true,
                                                                      message:
                                                                        '这是必填项',
                                                                    },
                                                                  ]}
                                                                />
                                                              )
                                                            );
                                                          }}
                                                        </ProFormDependency>
                                                      </Row>
                                                    </ProFormGroup>
                                                  );
                                                }}
                                              </ProFormList>
                                            );
                                          }}
                                        </ProFormDependency>
                                      </div>
                                    );
                                  }
                                }}
                              </ProFormDependency>
                            </>
                          );
                        }
                      }}
                    </ProFormDependency>
                  }
                </ProForm.Group>
              </>
            );
          }}
        </ProFormList>
      </ProForm>
      {/* 活动详情 */}
      {!noHistoryTable && (actionType === 'audit' || actionType === 'view') && (
        <div style={{ marginBottom: 20 }}>
          <ActivityRecord
            key="activityRecord"
            id={initialConfig?.id}
            productType={initialConfig?.productType}
            disabled={false}
          />
        </div>
      )}

      {/* 操作记录 */}
      {!noHistoryTable && (actionType === 'audit' || actionType === 'view') && (
        <OperateHistory
          key="operateHistory"
          id={initialConfig?.id}
          bizCode="voucherCampaign"
          disabled={false}
          sceneOptions={sceneOptions}
        />
      )}
    </Drawer>
  );
};

export default DetailModal;
