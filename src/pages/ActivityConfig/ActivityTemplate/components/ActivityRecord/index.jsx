import React, { memo, useEffect, useRef, useState } from 'react';
import CommonTableList from '@/components/CommonTableList';
import { activityStatistics } from '@/services/activityConfig/activityTemplate';
import { Button } from 'antd';
import { DownOutlined } from '@ant-design/icons';
import { exportExcel } from '@/utils/util';
import dayjs from 'dayjs';

const exposureCountTitleMap = {
  loan: '贷款首页曝光人数',
  vivoCard: 'vivocard首页曝光人数',
  '5G': '5G',
};

const ActivityRecord = ({ id, productType }) => {
  const ref = useRef();
  const [pageData, setPageData] = useState({ page: 1, pageSize: 10 });
  const columns = [
    {
      title: '活动时间',
      valueType: 'dateRange',
      dataIndex: 'activityTime',
      key: 'activityTime',
      hideInTable: true,
      fieldProps: {
        style: { width: '300px' },
      },
    },
    {
      title: '日期',
      dataIndex: 'date',
      key: 'date',
      align: 'center',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '活动查询人数',
      dataIndex: 'queryCount',
      key: 'queryCount',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '满足活动条件人数',
      dataIndex: 'matchCount',
      key: 'matchCount',
      align: 'center',
      hideInSearch: true,
      width: 120,
    },
    {
      title: exposureCountTitleMap[productType] || '贷款首页曝光人数',
      dataIndex: 'exposureCount',
      key: 'exposureCount',
      align: 'center',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '完成活动人数',
      dataIndex: 'finishCount',
      key: 'finishCount',
      align: 'center',
      hideInSearch: true,
      width: 120,
    },
    {
      title: '发放奖励人数',
      dataIndex: 'assignCount',
      key: 'assignCount',
      align: 'center',
      hideInSearch: true,
      width: 120,
    },
  ];

  const handleParams = (params) => {
    setPageData({
      page: params.current,
      pageSize: params.pageSize,
    });
    const tempParams = {
      page: params.current,
      pageSize: params.pageSize,
      id,
    };
    if (params.activityTime) {
      tempParams.startDate = dayjs(params.activityTime[0]).format('YYYY-MM-DD');
      tempParams.endDate = dayjs(params.activityTime[1]).format('YYYY-MM-DD');
      delete tempParams.activityTime;
    }
    return tempParams;
  };

  const getActivityRecord = async (params) => {
    const tempParams = handleParams(params);
    const res = await activityStatistics(tempParams);
    return {
      data: res.data.list,
      total: res.data.total,
    };
  };

  /**
   * 导出内容
   */
  const handleDownload = () => {
    const searchParams = ref.current?.getFieldsValue();
    let tempParams = handleParams(searchParams);
    tempParams.pageSize = pageData.pageSize;
    tempParams.page = pageData.page;
    exportExcel(tempParams, '/coupon/voucher/campaign/statistics/export');
  };

  return (
    <CommonTableList
      key="activityRecord"
      columns={columns}
      formRef={ref}
      bordered
      request={getActivityRecord}
      pagination={{
        pageSize: 10,
        showSizeChanger: false,
        showTotal: (total) => {
          return `共${total}项`;
        },
      }}
      headerTitle="活动详情"
      toolBarRender={() => [
        <Button key="out" onClick={handleDownload}>
          导出数据
          <DownOutlined />
        </Button>,
      ]}
    />
  );
};

export default memo(ActivityRecord);
