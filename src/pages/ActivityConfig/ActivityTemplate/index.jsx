import React, { useRef, useState, useEffect } from 'react';
import CommonTableList from '@/components/CommonTableList';
import {
  getActivitySettingList,
  activitySettingOnline,
  getActivitySceneList,
  getActivityDetails,
  deleteActivity,
} from '@/services/activityConfig/activityTemplate.js';
import {
  actProductTypeMap,
  audienceTypeMap,
  rewardTypeOptions,
} from '../constant.js';
import { Button, Dropdown, Space, Tag, Divider, Popconfirm, Spin } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import DetailModal from './components/DetailModal/index.jsx';
import { EDIT_PARAMS_INIT } from '../config.js';
import { padArray } from '@/utils/util.js';

const ActivityTemplate = () => {
  const actionRef = useRef();
  const [sceneOptions, setSceneOptions] = useState([]);
  const [modalVisible, setModalVisible] = useState(false); // 弹窗可见性
  const [actionType, setActionType] = useState('');
  const [initialConfig, setInitialConfig] = useState({});
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    getCampaignList();
  }, []);

  /**
   * 列表数据请求
   */
  const gainActivitySettingList = async (params) => {
    const param = {
      page: params.current,
      ...params,
    };

    if (params.activityTime) {
      param.startValidity = dayjs(params.activityTime[0]).format(
        'YYYY-MM-DD HH:mm:ss',
      );
      param.endValidity = dayjs(params.activityTime[1]).format(
        'YYYY-MM-DD HH:mm:ss',
      );
    }

    delete param.current;
    delete param.activityTime;

    const res = await getActivitySettingList(param);
    const { data } = res;
    return {
      data: data.list,
      total: data.total,
    };
  };

  /**
   * 活动上下线
   * onlineStatus 1 上线 2 下线
   */
  const changeOnlineStatus = async (record) => {
    const params = {
      id: record.id,
      onlineOperation: record.onlineStatus === 1 ? 2 : 1,
    };
    const res = await activitySettingOnline(params);
    if (res?.code === '200') {
      handleReload();
    }
  };

  /**
   * 获取活动场景列表
   */
  const getCampaignList = async () => {
    const params = {
      bizCode: 'COUPON',
      vendorId: 4,
    };
    const res = await getActivitySceneList(params);
    if (res?.code === '200' && res.data) {
      const tempOptions = res.data.map((item) => {
        return {
          value: item.scene,
          label: item.displayName,
        };
      });
      setSceneOptions(tempOptions);
    }
  };

  /**
   * 列表操作
   * actionType: view edit delete audit add
   */
  const handleAction = async (record, actionType) => {
    // 删除
    if (actionType === 'delete') {
      const res = await deleteActivity({ id: record.id });
      if (res?.code === '200') {
        handleReload();
      }
      return;
    }
    // 编辑详情
    if (record.id) {
      // 请求详情信息
      setLoading(true);
      const res = await getActivityDetails({ id: record.id });
      if (res?.code === '200' && res?.data) {
        const {
          startValidity,
          endValidity,
          rewardRules,
          userType,
          fileName,
          audienceTag,
          strategy,
        } = res.data;
        const tempRewardRules = rewardRules.map((rewardRule) => {
          if (rewardRule.cashType === 'condition') {
            const tempCashParamList = padArray(rewardRule.cashParamList, 6, {}); // 补充到6个
            const tempLoanAmountList = padArray(
              (rewardRule.loanAmountList || []).map((loanAmount) => ({
                loanAmount,
              })),
              6,
              {},
            );
            if (rewardRule.cashType === 'condition') {
              rewardRule.maxCount = rewardRule.maxCount / 100;
            }
            return {
              ...rewardRule,
              cashParamList: tempCashParamList,
              loanAmountList: tempLoanAmountList,
            };
          }
          if (rewardRule.voucherType === 'orderDeduction') {
            rewardRule.maxCount = rewardRule.maxCount / 100;
          }
          return rewardRule;
        });
        let fileList = [];
        if (userType === 'userPackage') {
          fileList = [
            {
              uid: '-1',
              name: fileName,
              status: 'done',
              response: {
                data: {
                  fileName: fileName,
                  fileId: audienceTag,
                },
              },
            },
          ];
        }
        if (strategy?.equipmentRestriction?.enable === false) {
          strategy.equipmentRestriction.enable = true;
          strategy.equipmentRestriction.equipmentParam = 'repeatable';
        }
        const tempRecord = {
          ...res.data,
          activityTime: [startValidity, endValidity],
          rewardRules: tempRewardRules,
          fileList,
          supportDelete: actionType === 'copy' ? 1 : record.supportDelete, // 控制部分字段上线后不可编辑 supportDelete === 1不禁用
        };

        setInitialConfig(tempRecord);
        setLoading(false);
        setModalVisible(true);
      }
    } else {
      // 设置初始值
      setInitialConfig(EDIT_PARAMS_INIT);
      setLoading(false);
      setModalVisible(true);
    }
    setActionType(actionType);
  };

  /**
   * 表格刷新
   */
  const handleReload = () => {
    actionRef.current?.reload();
  };

  const tableColumns = [
    {
      title: '活动id',
      dataIndex: 'id',
      key: 'id',
      valueType: 'digit',
      fieldProps: {
        min: 1,
        precision: 0,
      },
      width: 120,
      fixed: 'left',
      align: 'center',
      render: (text, record) => {
        return <span>{record.id}</span>;
      },
    },
    {
      title: '活动名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: 'center',
    },
    {
      title: '产品类型',
      dataIndex: 'productType',
      key: 'productType',
      valueType: 'select',
      valueEnum: actProductTypeMap,
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
      width: 120,
      align: 'center',
    },
    {
      title: '活动场景',
      dataIndex: 'scene',
      key: 'scene',
      valueType: 'select',
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
      width: 120,
      align: 'center',
      fieldProps: {
        options: sceneOptions,
      },
    },
    {
      title: '奖励类型',
      dataIndex: 'voucherType',
      key: 'voucherType',
      valueType: 'select',
      width: 120,
      align: 'center',
      hideInTable: true,
      formItemProps: {
        rules: [
          {
            required: true,
            message: '此项为必填项',
          },
        ],
      },
      fieldProps: {
        showSearch: true,
        allowClear: true,
        options: rewardTypeOptions,
      },
    },
    {
      title: '库存id',
      dataIndex: 'quotaId',
      key: 'quotaId',
      width: 200,
      align: 'center',
      hideInTable: true,
    },
    {
      title: '活动用户',
      dataIndex: 'userTypeName',
      key: 'userTypeName',
      hideInSearch: true,

      width: 120,
      align: 'center',
    },
    {
      title: '活动对象',
      dataIndex: 'audienceType',
      key: 'audienceType',
      valueType: 'select',
      hideInSearch: true,
      valueEnum: audienceTypeMap,
      width: 120,
      align: 'center',
    },
    {
      title: '开始时间',
      dataIndex: 'startValidity',
      key: 'startValidity',
      hideInSearch: true,
      valueType: 'dateTime',
      width: 120,
      align: 'center',
    },
    {
      title: '结束时间',
      dataIndex: 'endValidity',
      key: 'endValidity',
      hideInSearch: true,
      valueType: 'dateTime',
      width: 120,
      align: 'center',
    },
    {
      title: '活动时间',
      valueType: 'dateRange',
      dataIndex: 'activityTime',
      key: 'activityTime',
      hideInTable: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      hideInSearch: true,
      valueType: 'dateTime',
      width: 140,
      align: 'center',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      valueType: 'dateTime',
      hideInSearch: true,
      width: 140,
      align: 'center',
    },
    {
      title: '操作员',
      dataIndex: 'operatorName',
      key: 'operatorName',
      hideInSearch: true,
      width: 120,
      align: 'center',
    },
    {
      title: '审核员',
      dataIndex: 'auditorName',
      key: 'auditorName',
      hideInSearch: true,
      width: 120,
      align: 'center',
    },
    {
      title: '状态',
      dataIndex: 'onlineStatus',
      key: 'onlineStatus',
      valueType: 'select',
      valueEnum: {
        1: {
          text: '禁用',
          status: 'Error',
        },
        2: {
          text: '启用',
          status: 'Success',
        },
        3: {
          text: '已失效',
          status: 'Default',
        },
      },
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
      width: 120,
      align: 'center',
    },
    {
      title: '审核状态',
      dataIndex: 'auditStatus',
      key: 'auditStatus',
      valueType: 'select',
      valueEnum: {
        1: '待审核',
        2: '审核通过',
        3: '审核驳回',
      },
      fieldProps: {
        showSearch: true,
        allowClear: true,
      },
      render: (_, record) => {
        const auditStatusColorMap = {
          1: { text: '待审核', color: '#faad14' },
          2: { text: '审核通过', color: '#1890ff' },
          3: { text: '审核驳回', color: 'f5222d' },
        };
        return (
          <Space>
            {
              <Tag
                color={auditStatusColorMap[record.auditStatus]?.color}
                key={_}
              >
                {auditStatusColorMap[record.auditStatus]?.text}
              </Tag>
            }
          </Space>
        );
      },
      width: 120,
      align: 'center',
    },
    {
      title: '审核备注',
      dataIndex: 'auditRemark',
      key: 'auditRemark',
      hideInSearch: true,
      width: 120,
      align: 'center',
    },
    {
      title: '操作',
      dataIndex: 'action',
      key: 'action',
      hideInSearch: true,
      width: 400,
      align: 'center',
      fixed: 'right',
      render: (text, record) => {
        return (
          <Space split={<Divider type="vertical" />} key={record.id}>
            {record.supportEdit === 1 && (
              <Button
                key="edit"
                type="link"
                size="small"
                onClick={() => {
                  handleAction(record, 'edit');
                }}
              >
                编辑
              </Button>
            )}
            {record.supportDelete === 1 && (
              <Popconfirm
                title="删除后无法恢复，确认删除吗？"
                onConfirm={() => {
                  handleAction(record, 'delete');
                }}
                key="del"
                onCancel={() => {}}
                okText="确定"
                cancelText="取消"
              >
                <Button key="delete" type="link" size="small">
                  删除
                </Button>
              </Popconfirm>
            )}
            {record.supportApprove === 1 && (
              <Button
                key="audit"
                type="link"
                size="small"
                onClick={() => {
                  handleAction(record, 'audit');
                }}
              >
                审核
              </Button>
            )}
            {record.supportView === 1 && (
              <Button
                key="view"
                type="link"
                size="small"
                onClick={() => {
                  handleAction(record, 'view');
                }}
              >
                详情
              </Button>
            )}
            {record.supportOffline === 1 && (
              <Button
                key="offline"
                type="link"
                size="small"
                onClick={() => {
                  changeOnlineStatus(record);
                }}
              >
                下线
              </Button>
            )}
            {record.supportOnline === 1 && (
              <Button
                key="online"
                type="link"
                size="small"
                onClick={() => {
                  changeOnlineStatus(record);
                }}
              >
                上线
              </Button>
            )}
            {record.productType !== 2 && record.productType !== 6 && (
              <Button
                key="copy"
                type="link"
                size="small"
                onClick={() => {
                  handleAction(record, 'copy');
                }}
              >
                复制
              </Button>
            )}
          </Space>
        );
      },
    },
  ];

  return (
    <>
      <Spin spinning={loading}>
        <CommonTableList
          search={{ defaultCollapsed: false }}
          toolBarRender={() => [
            <Button
              type="primary"
              icon={<PlusOutlined />}
              onClick={() => {
                handleAction({}, 'add');
              }}
            >
              新增
            </Button>,
          ]}
          columns={tableColumns}
          request={gainActivitySettingList}
          actionRef={actionRef}
        />
        {/* 详情弹窗 */}
        <DetailModal
          modalVisible={modalVisible}
          actionType={actionType}
          setActionType={setActionType}
          setModalVisible={setModalVisible}
          handleReload={handleReload}
          setInitialConfig={setInitialConfig}
          initialConfig={initialConfig}
          sceneOptions={sceneOptions}
        />
      </Spin>
    </>
  );
};

export default ActivityTemplate;
