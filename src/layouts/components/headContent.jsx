import React, { Fragment } from 'react';
import Dropdown from './dropdown';
import { ProBreadcrumb } from '@ant-design/pro-components';
import { Link } from 'react-router-dom';
import style from '../style.less';

const headContent = (props) => {
  const itemRender = function (route, params, routes, paths) {
    const last = routes.indexOf(route) === routes.length - 1;
    return !route.component || last ? (
      <span>{route.breadcrumbName}</span>
    ) : (
      <Link to={route.path}>{route.breadcrumbName}</Link>
    );
  };
  return (
    <div className={style.headContent}>
      <ProBreadcrumb itemRender={itemRender} style={{ float: 'left' }} />
      <Dropdown />
    </div>
  );
};

export default headContent;
