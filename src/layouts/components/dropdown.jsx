import React, { Fragment } from 'react';
import { <PERSON><PERSON>, Menu, Spin, Dropdown } from 'antd';
import { LogoutOutlined, SmileOutlined } from '@ant-design/icons';
import style from '../style.less';

const dropdown = (props) => {
  const items = [
    {
      key: '1',
      label: (
        <Fragment>
          <LogoutOutlined />
          <span>&nbsp;&nbsp;&nbsp;退出登录</span>
        </Fragment>
      ),
    },
  ];
  return (
    <Dropdown menu={{ items }} trigger={['hover']}>
      <div className={style.action}>你好，xxx小伙伴</div>
    </Dropdown>
  );
};

export default dropdown;
