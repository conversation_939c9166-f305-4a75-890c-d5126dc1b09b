import {
  PageContainer,
  ProLayout,
  ProBreadcrumb,
} from '@ant-design/pro-components';
import { Fragment, useState } from 'react';
import { Link } from 'react-router-dom';
import routes from '@/routes/dev';
import Logo from './components/logo';
import HeadContent from './components/headContent';

export default function Layout(props) {
  return (
    <ProLayout
      layout="mix"
      title="金融运营平台"
      logo={<Logo />}
      route={routes[0]}
      location={{ pathname: props.location.pathname }}
      headerContentRender={() => <HeadContent />}
      menuItemRender={(item, dom) => {
        return <Link to={item.path}>{dom}</Link>;
      }}
    >
      <section style={{ background: '#f0f2f5', minHeight: 'calc(100vh - 75px)' }}>
        <PageContainer header={{ title: null }} breadcrumb={null}>
          {props.children}
        </PageContainer>
      </section>
    </ProLayout>
  );
}
