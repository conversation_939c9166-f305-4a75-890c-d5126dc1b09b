@pro-header-hover-bg: rgba(0, 0, 0, 0.025);

.action {
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 12px;
  cursor: pointer;
  transition: all 0.3s;

  > span {
    vertical-align: middle;
  }

  &:hover {
    background: @pro-header-hover-bg;
  }

  &:global(.opened) {
    background: @pro-header-hover-bg;
  }
}

.headContent {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-left: 25px;
}


.singlePageContainer {
  background-color: #f0f2f5;
}
