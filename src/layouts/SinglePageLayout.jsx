import React, { useEffect } from 'react';
import { Link, connect } from 'umi';
import { Button, ConfigProvider, FloatButton, Layout, Result } from 'antd';
import zhCN from 'antd/es/locale/zh_CN';

const { Content, Footer } = Layout;

const SinglePageLayout = (props) => {
  const {
    dispatch,
    children,
    location = {
      pathname: '/',
    },
  } = props;
  return (
    (<ConfigProvider locale={zhCN}>
      <Layout className="operation-admin__container">
        <Content style={{ minHeight: 'calc(100vh - 68px)' }}>
          <div style={{ minHeight: 'calc(100vh - 108px)' }}>
            {children}
          </div>
        </Content>
        <Footer style={{ textAlign: 'center' }}>
          Copyright &#169; 2022 互联网数字金服事业部 Made with by ❤ Finance F2e
        </Footer>
        <FloatButton.BackTop visibilityHeight={150} />
      </Layout>
    </ConfigProvider>)
  );
};

export default SinglePageLayout;
