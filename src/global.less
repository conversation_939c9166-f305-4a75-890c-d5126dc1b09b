@import './public/reset.css';

html,
body,
#root {
  height: 100%;
}

.colorWeak {
  filter: invert(80%);
}

.ant-layout {
  min-height: 100vh;
}

canvas {
  display: block;
}

body {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  // font-weight: 500;
}

ul,
ol {
  list-style: none;
}

.ant-layout .ant-pro-basicLayout-content {
  min-height: calc(100vh - 190px);
}

.ant-empty {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-content: center;
}
body {
  .ant-image-preview-root .ant-image-preview-mask, .ant-image-preview-root .ant-image-preview-wrap {
    z-index: 3001!important;
  }
}



.ant-back-top {
  .ant-back-top-content {
    width: 60px;
    height: 60px;
    border-radius: 60px;
  }

  .ant-back-top-icon {
    line-height: 60px;
    font-size: 32px;
  }
}