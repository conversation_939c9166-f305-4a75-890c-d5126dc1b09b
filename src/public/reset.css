html {
  -ms-text-size-adjust: 100%;
  -webkit-text-size-adjust: 100%;
  background: #FFF;
}

body {
  width: 100%;
  min-height: 100%;
  overflow-x: hidden;
  line-height: 1.4;
  font-family: "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue";
  /* -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none; */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

a img {
  border: 0;
}

/** env = windows **/
::-webkit-input-placeholder {
  font-family: "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue";
}

/** prevent default menu callout **/
a {
  -webkit-touch-callout: none;
}

/* ::-webkit-scrollbar {
      width: 0;
      height: 0;
      color: transparent;
      display: none;
  } */

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.f-fd-r {
  flex-direction: row;
}

.f-fd-rr {
  flex-direction: row-reverse;
}

.f-fd-c {
  flex-direction: column;
}

.f-fd-cr {
  flex-direction: column-reverse;
}

.f-jc-fs {
  justify-content: flex-start;
}

.f-jc-fe {
  justify-content: flex-end;
}

.f-jc-sb {
  justify-content: space-between;
}

.f-jc-c {
  justify-content: center;
}

.f-jc-sa {
  justify-content: space-around;
}

.f-ai-fs {
  align-items: flex-start;
}

.f-ai-fe {
  align-items: flex-end;
}

.f-ai-c {
  align-items: center;
}

.f-ai-b {
  align-items: baseline;
}

.f-ai-s {
  align-items: stretch;
}

.f-fw-w {
  flex-wrap: wrap;
}

.f-fw-n {
  flex-wrap: nowrap;
}

.f-fw-wr {
  flex-wrap: wrap-reverse;
}

.f-ff-rn {
  flex-flow: row nowrap;
}

.f-ff-cn {
  flex-flow: column nowrap;
}

.f-ac-c {
  align-content: center;
}

.f-ac-sb {
  align-content: space-between;
}

.f-ac-fs {
  align-content: flex-start;
}

.f-ac-fe {
  align-content: flex-end;
}

.f-ac-s {
  align-content: stretch;
}

.f-ac-sa {
  align-content: space-around;
}

.f-fg-1 {
  flex-grow: 1;
}

.f-fs-0 {
  flex-shrink: 0;
}

.items_flex {
  flex: 1;
}

.f-as-b {
  align-self: baseline;
}

.f-as-c {
  align-self: center;
}

.f-as-fs {
  align-self: flex-start;
}

.f-as-fe {
  align-self: flex-end;
}

.f-as-a {
  align-self: auto;
}

.f-as-sh {
  align-self: stretch;
}

.f-lr {
  flex-direction: row;
  justify-content: space-between;
}

.f-scroll {
  flex-wrap: nowrap;
  justify-content: space-between;
  overflow-x: scroll;
}

.f-item-sb {
  flex-wrap: wrap;
  flex-direction: row;
  align-content: space-between;
  justify-content: space-between;
}

.fl {
  float: left;
}

.fr {
  float: right;
}

.clearfix:after {
  display: block;
  content: '';
  clear: both;
  visibility: hidden;
}

.ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: normal;
}

.ellipsisLn {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
}

.text_wrap {
  word-wrap: break-word;
  word-break: break-all;
}

.operation-admin__container .ant-table-wrapper .ant-table-thead>tr>th {
  background: #677df4;
  color: #FFFFFF;
  font-size: 15px;
  font-weight: 900;
  overflow: hidden;
}

.ant-table-wrapper .ant-table-thead>tr>th {
  background: #677df4;
  color: #FFFFFF;
  font-size: 15px;
  font-weight: 900;
  overflow: hidden;
}