export default [
  {
    path: '/',
    component: '@/layouts/SinglePageLayout',
    name: '金融运营平台',
    title: '金融运营平台',
    routes: [
      {
        path: '/MoneyBeanCenter',
        title: '钱豆配置',
        name: '钱豆配置',
        routes: [
          {
            path: '/MoneyBeanCenter/CommonConfig',
            component: '@/pages/MoneyBeanCenter/CommonConfig',
            title: '通用配置',
            name: '通用配置',
            breadcrumbName: '通用配置',
          },
          {
            path: '/MoneyBeanCenter/TaskConfig',
            component: '@/pages/MoneyBeanCenter/TaskConfig',
            title: '任务配置',
            name: '任务配置',
            breadcrumbName: '任务配置',
          },
          {
            path: '/MoneyBeanCenter/ClearanceConfig',
            component: '@/pages/MoneyBeanCenter/ClearanceConfig',
            title: '核销配置',
            name: '核销配置',
            breadcrumbName: '核销配置',
          },
          {
            path: '/MoneyBeanCenter/AuditDetail/:scene',
            component: '@/pages/MoneyBeanCenter/AuditDetail',
            title: '审核详情',
            name: '审核详情',
            breadcrumbName: '审核详情',
          },
        ],
      },
      {
        path: '/SignActivity',
        title: '签到挑战活动',
        name: '签到挑战活动',
        routes: [
          {
            path: '/SignActivity/ActivityRule',
            component: '@/pages/SignActivity/ActivityRule',
            title: '活动规则配置',
            name: '活动规则配置',
            breadcrumbName: '活动规则配置',
          },
        ],
      },
      {
        path: '/ActivityConfig',
        title: '活动配置',
        name: '活动配置',
        routes: [
          {
            path: '/ActivityConfig/ActivityTemplate',
            component: '@/pages/ActivityConfig/ActivityTemplate',
            title: '活动模版配置',
            name: '活动模版配置',
            breadcrumbName: '活动模版配置',
          },
        ],
      },
      {
        path: '/IncreaseQuotaActivity',
        title: '提额活动',
        name: '提额活动',
        routes: [
          {
            path: '/IncreaseQuotaActivity/InitiativeApply',
            component: '@/pages/IncreaseQuotaActivity/InitiativeApply',
            title: '主动提额',
            name: '主动提额',
            breadcrumbName: '主动提额',
          },
          {
            path: '/IncreaseQuotaActivity/MultiScenario',
            component: '@/pages/IncreaseQuotaActivity/MultiScenario',
            title: '多场景提额配置',
            name: '多场景提额配置',
            breadcrumbName: '多场景提额配置',
          },
        ],
      },
      {
        path: '/MemberCenter',
        title: '会员配置',
        name: '会员配置',
        routes: [
          {
            path: '/MemberCenter/LoanDiscountConfig',
            component: '@/pages/MemberCenter/LoanDiscountConfig',
            title: '超级会员借款折扣配置',
            name: '超级会员借款折扣配置',
            breadcrumbName: '超级会员借款折扣配置',
          },
          {
            path: '/MemberCenter/AuditDetail/:scene',
            component: '@/pages/MemberCenter/AuditDetail',
            title: '审核详情',
            name: '审核详情',
            breadcrumbName: '审核详情',
          },
        ],
      },
      {
        path: '/BenefitCenter',
        title: '福利中心',
        name: '福利中心',
        routes: [
          {
            path: '/BenefitCenter/ConfigManagement',
            component: '@/pages/BenefitCenter/ConfigManagement',
            title: '福利配置管理',
            name: '福利配置管理',
            breadcrumbName: '福利配置管理',
          },
          {
            path: '/BenefitCenter/AuditDetail/Commission',
            component: '@/pages/BenefitCenter/CommissionAudit',
            title: '福利佣金配置审核',
            name: '福利佣金配置审核',
            breadcrumbName: '福利佣金配置审核',
          },
          {
            path: '/BenefitCenter/PageManagement',
            component: '@/pages/BenefitCenter/PageManagement',
            title: '福利页面管理',
            name: '福利页面管理',
            breadcrumbName: '福利页面管理',
          },
          {
            path: '/BenefitCenter/ContentManagement',
            component: '@/pages/BenefitCenter/ContentManagement',
            title: '福利内容管理',
            name: '福利内容管理',
            breadcrumbName: '福利内容管理',
          },
          {
            path: '/BenefitCenter/SpiderGoods',
            component: '@/pages/BenefitCenter/SpiderGoods',
            title: '福利内容选品池(初筛)',
            name: '福利内容选品池(初筛)',
            breadcrumbName: '福利内容选品池(初筛)',
          },
        ],
      },
      {
        path: '/RewardsConfig',
        title: '金融奖励管理',
        name: '金融奖励管理',
        routes: [
          {
            path: '/RewardsConfig/PhysicalPrizes',
            component: '@/pages/RewardsConfig/PhysicalPrizes',
            title: '实物奖品',
            name: '实物奖品',
            breadcrumbName: '实物奖品',
          },
          {
            path: '/RewardsConfig/VirtualPrizes',
            component: '@/pages/RewardsConfig/VirtualPrizes',
            title: '虚拟奖品库存配置',
            name: '虚拟奖品库存配置',
            breadcrumbName: '虚拟奖品库存配置',
          },
          {
            path: '/RewardsConfig/InterestCoupon',
            // component: '@/pages/RewardsConfig/InterestCoupon',
            title: '贷款利息优惠券',
            name: '贷款利息优惠券',
            // breadcrumbName: '贷款利息优惠券',
            routes: [
              {
                path: '/RewardsConfig/InterestCoupon/InterestCouponAllocation',
                component: '@/pages/RewardsConfig/InterestCoupon/InterestCouponAllocation',
                title: '利息优惠券发放规则配置',
                name: '利息优惠券发放规则配置',
                breadcrumbName: '利息优惠券发放规则配置',
              },
              {
                path: '/RewardsConfig/InterestCoupon/InterestDetailsQuery',
                component: '@/pages/RewardsConfig/InterestCoupon/InterestDetailsQuery',
                title: '利息优惠券发放明细查询',
                name: '利息优惠券发放明细查询',
                breadcrumbName: '利息优惠券发放明细查询',
              },
            ],
          },
        ],
      },
      {
        path: '/ActivityManagement',
        title: '活动管理',
        name: '活动管理',
        routes: [
          {
            path: '/ActivityManagement/RedEnvelopTaskConfig',
            component: '@/pages/ActivityManagement/RedEnvelopTaskConfig',
            title: '下载应用赚红包',
            name: '下载应用赚红包',
            breadcrumbName: '下载应用赚红包',
          },
          {
            path: '/ActivityManagement/RedEnvelopTaskConfigNew',
            component: '@/pages/ActivityManagement/RedEnvelopTaskConfigNew',
            title: '下载应用赚红包-新',
            name: '下载应用赚红包-新',
            breadcrumbName: '下载应用赚红包-新',
          },
          {
            path: '/ActivityManagement/AuditDetail/:scene',
            component: '@/pages/ActivityManagement/AuditDetail',
            title: '审核详情',
            name: '审核详情',
            breadcrumbName: '审核详情',
          },
        ],
      },
      {
        path: '/FlowCanvasManagement',
        title: '流程画布管理',
        name: '流程画布管理',
        routes: [
          {
            path: '/FlowCanvasManagement/CanvasList',
            component: '@/pages/FlowCanvasManagement/CanvasList',
            title: '贷款营销流程画布',
            name: '贷款营销流程画布',
            breadcrumbName: '贷款营销流程画布',
          },
          {
            path: '/FlowCanvasManagement/CanvasDetail/:optType?/:canvasId?',
            component: '@/pages/FlowCanvasManagement/CanvasDetail',
          },
        ],
      },
      {
        path: '/MarketingActivity',
        title: '营销活动配置',
        name: '营销活动配置',
        routes: [{
          path: '/MarketingActivity/ActivityRule',
          component: '@/pages/SignActivity/ActivityRule',
          title: '签到玩法',
          name: '签到玩法',
          breadcrumbName: '签到玩法',
        },
        {
          path: '/MarketingActivity/MechanicList',
          component: '@/pages/MarketingActivity/MechanicList',
          title: '玩法配置',
          name: '玩法配置',
          breadcrumbName: '玩法配置',
        },
        {
          path: '/MarketingActivity/ActivityConfig',
          component: '@/pages/MarketingActivity/ActivityConfig',
          title: '活动配置',
          name: '活动配置',
          breadcrumbName: '活动配置',
        },
        ],
      },
      // 客服平台页面
      {
        path: '/CustomerService',
        title: '客服平台',
        name: '客服平台',
        routes: [
          {
            path: '/CustomerService/DebtTransfer',
            component: '@/pages/CustomerService/DebtTransfer',
            title: '通用审核',
            name: '通用审核',
            breadcrumbName: '通用审核',
          },
          {
            path: '/CustomerService/CreditCancellation',
            component: '@/pages/CustomerService/CreditCancellation',
            title: '额度注销',
            name: '额度注销',
            breadcrumbName: '额度注销',
          },
        ],
      },
      {
        component: '@/pages/404',
      },
    ],
  },
];
