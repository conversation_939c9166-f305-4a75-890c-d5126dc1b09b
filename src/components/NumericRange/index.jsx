import React, { useState } from 'react';
import { InputNumber } from 'antd';

const NumericRange = (props) => {
  const { id, value = {}, onChange } = props;
  const [min, setMin] = useState(undefined);
  const [max, setMax] = useState(undefined);
  const triggerChange = (changedValue) => {
    onChange?.({
      max,
      min,
      ...value,
      ...changedValue,
    });
  };

  const onNumberChange = (val, type) => {
    // 确保空值时传递 undefined 而不是 null
    const newValue = val === null ? undefined : val;
    type === 'min' ? setMin(newValue) : setMax(newValue);
    triggerChange({
      [type]: newValue,
    });
  };
  return (
    <span id={id}>
      <InputNumber
        value={value.min || min}
        onChange={(num) => {
          onNumberChange(num, 'min');
        }}
        precision={0}
        min={0}
        placeholder="min"
      />
      <span style={{ display: 'inline-block', margin: '0 10px' }}> —— </span>
      <InputNumber
        value={value.max || max}
        onChange={(num) => {
          onNumberChange(num, 'max');
        }}
        precision={0}
        min={0}
        placeholder="max"
      />
    </span>
  );
};
export default NumericRange;
