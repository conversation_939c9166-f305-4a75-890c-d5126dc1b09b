import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Select, Spin } from 'antd';
import debounce from 'lodash/debounce';

const DebounceSelect = ({
  fetchOptions,
  debounceTimeout = 800,
  addParam,
  clearOnAddParamChange,
  setOptionByAddParam,
  ...props
}) => {
  const [fetching, setFetching] = useState(false);
  const [options, setOptions] = useState([]);
  const fetchRef = useRef(0);

  useEffect(() => {
    debounceFetcher();
  }, []);

  // 相关参数变化 选项置空
  useEffect(() => {
    clearOnAddParamChange && setOptions([]);
    setOptionByAddParam && debounceFetcher();
  }, [addParam]);

  const debounceFetcher = useMemo(() => {
    const loadOptions = (value) => {
      fetchRef.current += 1;
      const fetchId = fetchRef.current;
      setOptions([]);
      setFetching(true);
      const param = value || addParam?.initialValue;

      fetchOptions(param, addParam).then((newOptions) => {
        if (fetchId !== fetchRef.current) {
          // for fetch callback order
          return;
        }
        setOptions(newOptions);
        setFetching(false);
      });
    };
    return debounce(loadOptions, debounceTimeout);
  }, [fetchOptions, debounceTimeout, addParam]);
  return (
    <Select
      // labelInValue
      filterOption={false}
      onSearch={debounceFetcher}
      onChange={(value) => {}}
      notFoundContent={fetching ? <Spin size="small" /> : null}
      {...props}
      options={options}
    />
  );
};

export default DebounceSelect;
