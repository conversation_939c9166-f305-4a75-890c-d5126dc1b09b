import { useRef } from 'react';
import { DrawerForm } from '@ant-design/pro-components';

export default (props) => {
  const ref = useRef();
  const {
    formRef = ref,
    title,
    trigger,
    formRender,
    params,
    request,
    submitTimeout = 1000,
    submitter,
    onFinish = async (values) => {
      return true;
    },
    drawerProps,
    width = 1000,
    onVisibleChange,
    open,
    onOpenChange,
    onValuesChange = () => {},
    layout = 'vertical',
    formLayoutType = {},
    initialValues,
    disabled,
    readonly = false,
  } = props;
  return (
    <DrawerForm
      title={title}
      width={width}
      formRef={formRef}
      trigger={trigger}
      params={params}
      request={request}
      autoFocusFirstInput
      initialValues={initialValues}
      disabled={disabled}
      onVisibleChange={onVisibleChange}
      drawerProps={{
        ...drawerProps,
        destroyOnClose: true,
      }}
      submitTimeout={submitTimeout}
      onFinish={onFinish}
      submitter={submitter}
      open={open}
      onOpenChange={(open) => {
        onOpenChange && onOpenChange(open);
      }}
      onValuesChange={(changeValues) => onValuesChange(changeValues)}
      layout={layout}
      {...formLayoutType}
      readonly={readonly}
    >
      {formRender}
    </DrawerForm>
  );
};
