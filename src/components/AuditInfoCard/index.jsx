import { ProDescriptions, ProCard } from '@ant-design/pro-components';
import { parseDate } from '@/utils/util';

const AuditInfoCard = ({ itemOperationLog }) => {
  return (
    <ProCard>
      <ProDescriptions
        title="操作详情"
        dataSource={itemOperationLog}
        columns={[
          {
            title: '操作编号',
            dataIndex: 'id',
            ellipsis: true,
          },
          {
            title: '操作员',
            dataIndex: 'operatorName',
            ellipsis: true,
          },
          {
            title: '操作内容',
            dataIndex: 'operatorType',
            ellipsis: true,
          },
          {
            title: '操作时间',
            dataIndex: 'createTime',
            ellipsis: true,
            render: (text, record) => (
              <span>{record.createTime && parseDate(record.createTime)}</span>
            ),
          },
          {
            title: '操作备注',
            dataIndex: 'auditRemark',
            ellipsis: true,
          },
        ]}
      />
    </ProCard>
  );
};

export default AuditInfoCard;
