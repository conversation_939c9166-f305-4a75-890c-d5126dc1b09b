import React, { useImperativeHandle, forwardRef, useRef } from 'react';
import CommonTableList from '@/components/CommonTableList';
import { queryLogList } from '@/services/common.js';
import { queryWalletLogList } from '@/services/benefitCenter.js';
import { Button } from 'antd';
import { operateBasicColumn } from './columns';

// 注意 此组件适用于页面 不适用于弹窗嵌套方式审核详情
const OperateHistory = forwardRef(({ id, bizCode, handleDetailView }, ref) => {
  const actionRef = useRef();
  // 操作记录列表数据请求
  const getOperationHistory = async (params) => {
    const param = {
      page: params.current,
      pageSize: params.pageSize,
      operationId: id,
      bizCode,
    };
    let res = {};
    if (['welfareCommissionConfig', 'welfarePage', 'welfareContent'].indexOf(bizCode) !== -1) {
      res = await queryWalletLogList(param);
    } else {
      res = await queryLogList(param);
    }

    if (res?.code === '200' && res.data) {
      return {
        data: res.data.list,
        total: res.data.total,
      };
    }
  };

  // 父组件调用刷新
  const upDateHistory = () => {
    actionRef.current?.reload();
  };
  // 需要将暴露的接口返回出去
  if (ref) {
    useImperativeHandle(ref, () => ({
      upDateHistory,
    }));
  }

  const columns = [
    ...operateBasicColumn,
    {
      title: '操作详情',
      dataIndex: 'actionType',
      key: 'actionType',
      align: 'center',
      fixed: 'right',
      hideInSearch: true,
      render: (text, record) => (
        <Button
          key="edit"
          type="link"
          size="small"
          onClick={() => {
            handleDetailView && handleDetailView(record);
          }}
          disabled={false}
        >
          查看详情
        </Button>
      ),
    },
  ];
  return (
    <>
      <CommonTableList
        key="OperateHistory"
        columns={columns}
        search={false}
        bordered
        actionRef={actionRef}
        request={getOperationHistory}
        pagination={{
          pageSize: 10,
          showSizeChanger: false,
          showTotal: (total) => `共${total}项`,
        }}
        headerTitle="操作记录"
      />

    </>
  );
});

export default OperateHistory;
