import React, { Fragment, useRef } from 'react';
import { ConfigProvider } from 'finance-busi-components-toB';
import { ProTable } from '@ant-design/pro-components';

const CommonTableList = (props) => {
  const ref = useRef();
  const {
    actionRef = ref,
    formRef = ref,
    request,
    headerTitle,
    toolbar = {},
    toolBarRender = () => [<span></span>],
    rowKey = 'id',
    columns = [],
    // pageSize = 10,
    search = { labelWidth: 'auto' },
    pagination,
    form,
    manualRequest,
    options = false,
    title,
    expandable,
    dataSource,
    className
  } = props;
  return (
    <Fragment>
      <ConfigProvider>
        <ProTable
          bordered
          cardBordered
          className={className || 'common-task-list'}
          key="commonTableList"
          defaultSize={'large'}
          size={'large'}
          search={search}
          rowKey={rowKey}
          options={options}
          columns={columns}
          headerTitle={headerTitle}
          title={title}
          actionRef={actionRef}
          formRef={formRef}
          form={form}
          toolbar={toolbar}
          toolBarRender={toolBarRender}
          manualRequest={manualRequest}
          expandable={expandable}
          dataSource={dataSource}
          request={async (params = {}, sort, filter) => {
            if (!request) {
              return null;
            }
            return request(params, sort, filter);
          }}
          pagination={
            pagination
              ? {
                size: 'default',
                showSizeChanger: true,
                showTotal: (total) => {
                  return `共${total}项`;
                },
                ...pagination,
              }
              : pagination
          }
          scroll={{ x: '100%', y: '80vh' }}
        />
      </ConfigProvider>
    </Fragment>
  );
};

export default CommonTableList;
