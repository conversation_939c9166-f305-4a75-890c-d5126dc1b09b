import { useRef } from 'react';
import { Input, Button } from 'antd';
import { ProDescriptions, ProCard } from '@ant-design/pro-components';
import { Link } from 'umi';

const AuditCard = ({
  id, title, onSubmit, type, scene, auditData,
}) => {
  const actionRef = useRef();
  /* eslint-disable */
  return (
    <ProCard>
      <ProDescriptions
        actionRef={actionRef}
        formProps={{
          onValuesChange: (e, f) => console.log(f),
        }}
        dataSource={auditData}
        title={title || '审核模块'}
        request={async () => {
          return Promise.resolve({
            success: true,
            data: {
              rate: 5,
              id: '这是一段文本columns',
              date: '20200809',
              money: '1212100',
              state: 'all',
              state2: 'open',
            },
          });
        }}
        columns={[
          {
            title: '审核状态',
            key: 'state',
            dataIndex: 'auditStatus',
            valueType: 'select',
            valueEnum: {
              // 0: 待提交 1:待审核 2:审核通过 3:审核驳回
              0: {
                text: '待提交',
                status: 'Warning',
              },
              1: {
                text: '待审核',
                status: 'Warning',
              },
              2: {
                text: '审核通过（自动上线）',
                status: 'Success',
              },
              3: {
                text: '审核驳回',
                status: 'Error',
              },
            },
          },
          {
            title: '审核人',
            key: 'state2',
            dataIndex: 'operatorName',
            renderFormItem: () => {
              return <Input placeholder="输入 Success 切换分值" />;
            },
          },
          {
            title: '操作',
            valueType: 'option',

            render: () => [
              <Button type="primary" key="submit" onClick={onSubmit}>
                提交审核
              </Button>,
              <Link to={!id ? `/${type}/AuditDetail/${scene}` : `/${type}/AuditDetail/${scene}?id=${id}`} key={0}>
                <Button key="button" type="primary">
                  查看详情
                </Button>
              </Link>,
            ],
          },
        ]}
      />
    </ProCard>
  );
  /* eslint-disable */
};

export default AuditCard;
