import '@wangeditor/editor/dist/css/style.css'; // 引入 css
import React, { useState, useEffect } from 'react';
import { Editor, Toolbar } from '@wangeditor/editor-for-react';
import {
  IDomEditor,
  IEditorConfig,
  IToolbarConfig,
  DomEditor,
} from '@wangeditor/editor';

const RichTextEditor = (props) => {
  const { id, value = null, onChange, disabled } = props;

  const [editor, setEditor] = useState(null); // 存储 editor 实例
  // 编辑器内容
  const [html, setHtml] = useState(value);
  const toolbarConfig = {
    excludeKeys: [
      // 排除部分工具
      'group-image', // 图片
      'group-video', // 视频
      'insertTable', // 表格
      'codeBlock', // 代码块
      'code', //行内代码
      'group-more-style',
      'fullScreen',
      'fontFamily', // 字体
      'bulletedList', // 无序列表
      'numberedList', // 有序列表
      'todo',
      'bgColor', // 文字背景色
      'insertLink', // 插入链接
    ],
  };
  const editorConfig = {
    placeholder: '请输入内容...',
    maxHeight: '100px',
    readOnly: disabled, // 控制只读 不可编辑
  };

  // 及时销毁 editor ，重要！
  useEffect(() => {
    // 获取工具栏配置
    // if (editor) {
    // const toolbar = DomEditor.getToolbar(editor);
    // const curToolbarConfig = toolbar?.getConfig();
    // console.log(curToolbarConfig?.toolbarKeys);
    // }

    return () => {
      if (editor == null) return;
      editor.destroy();
      setEditor(null);
    };
  }, [editor]);

  // 更新表单
  useEffect(() => {
    onChange(html);
  }, [html]);

  useEffect(() => {
    setHtml(value);
  }, [value]);

  return (
    <>
      <div style={{ border: '1px solid #ccc', zIndex: 100 }} id={id}>
        <Toolbar
          editor={editor}
          defaultConfig={toolbarConfig}
          mode="default"
          style={{ borderBottom: '1px solid #ccc' }}
        />
        <Editor
          defaultConfig={editorConfig}
          value={html}
          onCreated={setEditor}
          onChange={(editor) => {
            setHtml(editor.getHtml());
          }}
          mode="default"
          style={{ height: '200px', overflowY: 'hidden' }}
        />
      </div>
      {/* <div style={{ marginTop: '15px' }}>
        {html}
      </div> */}
    </>
  );
};

export default RichTextEditor;
