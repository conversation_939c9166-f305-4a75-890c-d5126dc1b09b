import React, { Fragment } from 'react';
import { UploadOutlined } from '@ant-design/icons';
import { Button, Upload, message } from 'antd';

const FileUpload = (props) => {
  console.log('props===', props);
  const {
    normalResponseCode,
    listType,
    accept,
    maxCount,
    action,
    headers,
    beforeUpload,
    onChange,
    fileList,
    setFileList,
    data,
  } = props;
  const initProps = {
    listType,
    accept,
    maxCount,
    action,
    headers,
    data,
  };

  // const beforeFileUpload = (file, fileList) => {
  //   console.log('beforeFileUpload====', file, fileList)
  //   return true
  // }

  const onFileChange = (fileInfo) => {
    console.log('onFileChange====', fileInfo);
    if (fileInfo.file.status !== 'uploading') {
      console.log(fileInfo.file, fileInfo.fileList);
    }
    if (fileInfo.file.status === 'done') {
      message.success('人群包上传成功');
    } else if (fileInfo.file.status === 'error') {
      message.success('人群包上传失败');
    }
    const tempFileList = fileInfo.fileList.filter((item) => {
      // 过滤异常上传
      if (item.response && item?.response.code === normalResponseCode) {
        return true;
      } else {
        return false;
      }
    });
    console.log('tempFileList===', tempFileList);
    setFileList(tempFileList);
    // onChange(tempFileList)
  };

  return (
    <Fragment>
      <Upload
        withCredentials
        {...initProps}
        onChange={onFileChange}
        fileList={fileList}
      >
        <Button icon={<UploadOutlined />}>上传文件</Button>
      </Upload>
    </Fragment>
  );
};

export default FileUpload;
