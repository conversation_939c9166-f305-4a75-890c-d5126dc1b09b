export function parseDate(params) {
  const time = new Date(params);
  const y = time.getFullYear();
  let m = time.getMonth() + 1;
  m = m < 10 ? `0${m}` : m;
  let d = time.getDate();
  d = d < 10 ? `0${d}` : d;
  let h = time.getHours();
  h = h < 10 ? `0${h}` : h;
  let minute = time.getMinutes();
  minute = minute < 10 ? `0${minute}` : minute;
  let second = time.getSeconds();
  second = second < 10 ? `0${second}` : second;
  return `${y}-${m}-${d} ${h}:${minute}:${second}`;
}

export const debounce = (func, delay) => {
  let timer;
  return (...args) => {
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(this, args);
    }, delay);
  };
};

export const copyContent = (val) => {
  const input = document.createElement('input');
  document.body.appendChild(input);
  input.setAttribute('value', val);
  input.select();
  if (document.execCommand('copy')) {
    document.execCommand('copy');
  }
};
export const padArray = (arr, length, replace) => {
  while (arr.length < length) {
    arr.push(replace);
  }
  return arr;
};

export const downloadFile = (fileId) => {
  window.location.href = `${API_BASE_URL}/user/file/download?fileId=${fileId}`;
};

//导出接口通用方法
export function exportExcel(params, urlPath) {
  let str = '';
  for (let key in params) {
    if (params[key]) {
      str += key + '=' + params[key] + '&';
    }
  }
  str = str.slice(0, -1);
  location.href = `${API_BASE_URL}/`.concat(urlPath, '?', str);
}

/**
 * 富文本转文字
 */

export const extractPlainTextFromRichText = (htmlString) => {
  let tempDiv = document.createElement('div');
  tempDiv.innerHTML = htmlString;
  return tempDiv.innerText || tempDiv.textContent;
};
