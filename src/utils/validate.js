export const validateTrialDuration = (rule, value, callback) => {
  if (
    value
    && (Number.isNaN(value) || String(value).indexOf('.') > -1 || value < 0)
  ) {
    callback('只支持非负整数哦');
  } else {
    callback();
  }
};

// 正整数 不包括0
export const validatePositiveInteger = (rule, value, callback) => {
  if (
    value
    && (Number.isNaN(value) || value <= 0 || value.toString().includes('.'))
  ) {
    callback('只支持正整数');
  } else {
    callback();
  }
};

// 非负整数 包括0
export const validateNotNegativeInteger = (rule, value, callback) => {
  if (
    value
    && (Number.isNaN(value) || value < 0 || value.toString().includes('.'))
  ) {
    callback('只支持非负整数');
  } else {
    callback();
  }
};

export const validateMoney = (rule, value, callback) => {
  if (value && (Number.isNaN(value) || value <= 0)) {
    callback('金额只支持正数');
  } else {
    callback();
  }
};

export const validatePositiveMoney = (rule, value, callback) => {
  if (Number.isNaN(value) || value <= 0) {
    callback('金额只支持正数');
  } else {
    callback();
  }
};

export const validateMoneyNotNegativeInteger = (rule, value, callback) => {
  if (value && (Number.isNaN(value) || value < 0)) {
    callback('金额只支持非负数');
  } else {
    callback();
  }
};

export const validateAlphanumeric = (number) => (rule, value, callback) => {
  if (value && value.length > number) {
    callback('不能超出字数');
  } else {
    callback();
  }
};

export const checkNumericRange = (_, value) => {
  if (
    !Number.isNaN(value?.max)
    && value?.max !== null
    && !Number.isNaN(value?.min)
    && value?.min !== null
    && value?.min > value?.max
  ) {
    return Promise.reject(new Error('区间最小值不能大于区间最大值'));
  }
  return Promise.resolve();
};

// 添加这个校验函数
export const checkNumericRangeRequired = (_, value, options = { requireMin: true, requireMax: true }) => {
  const { requireMin = true, requireMax = true } = options;

  if (!value) {
    return Promise.reject(new Error('请输入频次门槛范围'));
  }

  // 如果两个都需要校验，但都为空
  if (requireMin && requireMax && value.min === undefined && value.max === undefined) {
    return Promise.reject(new Error('请输入频次门槛范围'));
  }

  // 校验最小值
  if (requireMin && value.min === undefined) {
    return Promise.reject(new Error('请输入最小值'));
  }

  // 校验最大值
  if (requireMax && value.max === undefined) {
    return Promise.reject(new Error('请输入最大值'));
  }

  return Promise.resolve();
};

/**
 * 链接校验
 * @param {*} _
 * @param {*} value
 * @returns
 */

export const validateLink = (_, value) => {
  if (!value || value.includes('://')) {
    return Promise.resolve();
  }
  return Promise.reject(new Error('链接格式错误'));
};

/**
 * 16进制颜色 6位数
 * 如 #ffffff
 */
export const colorRule = {
  pattern: /^#([a-fA-F0-9]{6})$/,
  message: '请输入6位的16进制颜色，如#ffffff',
};
/**
 * 颜色校验
 */
export const mainColorRule = {
  pattern:
    /^#([a-fA-F0-9]{6})$|^rgba?\(\d{1,3},\s?\d{1,3},\s?\d{1,3}(,\s?(1|0?\.\d+))?\)$|^rgb\(\d{1,3},\s?\d{1,3},\s?\d{1,3}\)$/,
  message:
    '请输入正确的颜色格式，如#ffffff、rgba(255, 255, 255, 0.5) 或 rgb(255, 255, 255)',
};

/**
 * 简单校验dpLink
 */

export const dpRule = {
  pattern: /^(?!(http|https):\/\/).*/,
  message: '请输入deepLink链接',
};

/**
 * 身份证号码校验
 */

export const idCardRule = {
  pattern: /^(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
  message: '请输入正确的身份证号码',
};
