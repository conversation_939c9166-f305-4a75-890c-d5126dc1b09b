/* eslint-disable */
/**
 * request 网络请求工具
 * 更详细的 api 文档: https://github.com/umijs/umi-request
 */
import { extend } from 'umi-request';
import { notification, Modal } from 'antd';
import cookie from 'react-cookies';

let updateModel = null;
const codeMessage = {
  200: '服务器成功返回请求的数据。',
  201: '新建或修改数据成功。',
  202: '一个请求已经进入后台排队（异步任务）。',
  204: '删除数据成功。',
  400: '发出的请求有错误，服务器没有进行新建或修改数据的操作。',
  401: '用户没有权限（令牌、用户名、密码错误）。',
  403: '用户得到授权，但是访问是被禁止的。',
  404: '发出的请求针对的是不存在的记录，服务器没有进行操作。',
  406: '请求的格式不可得。',
  410: '请求的资源被永久删除，且不会再得到的。',
  422: '当创建一个对象时，发生一个验证错误。',
  500: '服务器发生错误，请检查服务器。',
  502: '网关错误。',
  503: '服务不可用，服务器暂时过载或维护。',
  504: '网关超时。',
};
const codeSuccessed = [0, '0', 200, '200'];
const tc_fd = cookie.load('tc_fd');

const errorHandler = (error) => {
  const { response } = error;
  if (!response) {
    notification.error({
      description: '您的网络发生异常，无法连接服务器',
      message: '网络异常',
    });
    return;
  }
  const errorText =
    response.msg ||
    response.errorMessage ||
    response.errorMsg || 
    codeMessage[response.status] ||
    response.statusText;
  if (response.status) {
    const { status, url } = response;
    notification.error({
      message: `请求错误 ${status}: ${url}`,
      description: errorText,
    });
  }
  if (response.msg || response.errorMessage || response.errorMsg) {
    notification.error({
      message: '出错了',
      description: errorText,
    });
    return;
  }
};

// 配置request请求时的默认参数
const request = extend({
  errorHandler, // 默认错误处理
  credentials: 'include', // 请求携带cookie
  prefix: API_BASE_URL,
});

request.interceptors.request.use(async (url, options) => {
  const headers = {};
  if (UMI_ENV === 'testbase' && tc_fd && tc_fd !== 'undefined') {
    headers.tc_fd = tc_fd;
  }
  return {
    url: url,
    options: { ...options, headers },
  };
});

// 拦截返回登录态
request.interceptors.response.use(async (response) => {
  response
    .clone()
    .json()
    .then((res) => {
      // 判断版本不一致，弹窗刷新
      if (res && res.data && res.code === 40400) {
        if (updateModel) {
          return;
        }
        updateModel = Modal.error({
          centered: true,
          title: '版本更新',
          content: '当前平台已有新版本发布，请点击立即刷新',
          onOk: () => {
            location.reload();
          },
        });
      }
    });

  return response;
});

// 请求结果统一处理
request.use(async (ctx, next) => {
  await next();
  const { res } = ctx;
  if (codeSuccessed.includes(res.code)) {
    return res;
  } else {
    return Promise.reject({ response: res });
  }
});

export default request;
