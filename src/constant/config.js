import dayjs from 'dayjs';
export const operateBasicColumn = [
  {
    title: '操作编号',
    key: 'id',
    dataIndex: 'id',
    align: 'center',
    hideInSearch: true,
    render: (text, record) => (
      <span style={{ color: '#1890ff' }}>{record.id}</span>
    ),
  },
  {
    title: '操作员',
    key: 'operatorName',
    dataIndex: 'operatorName',
    hideInSearch: true,
    align: 'center',
  },
  {
    title: '操作内容',
    key: 'operatorType',
    dataIndex: 'operatorType',
    hideInSearch: true,
    align: 'center',
  },
  {
    title: '操作时间',
    key: 'createTime',
    dataIndex: 'createTime',
    hideInSearch: true,
    align: 'center',
    renderText: (text) => {
      return text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '';
    },
  },
  {
    title: '操作备注',
    key: 'auditRemark',
    dataIndex: 'auditRemark',
    hideInSearch: true,
    align: 'center',
  },
];

export const titleMap = {
  edit: '编辑',
  audit: '审核',
  view: '查看详情',
  add: '新建',
  copy: '复制',
};

export const btnMap = {
  edit: '提交',
  audit: '审核通过',
  view: '确认',
  add: '提交',
  copy: '提交',
};

export const auditColumns = [
  {
    title: '审核意见',
    dataIndex: 'auditRemark',
    key: 'auditRemark',
    formItemProps: {
      extra: '限制20个字符',
    },
    fieldProps: {
      maxLength: 20,
      showCount: true,
    },
  },
];
