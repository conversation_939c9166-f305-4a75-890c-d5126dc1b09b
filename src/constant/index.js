export const rewardTypeOptions = [
  {
    value: 'cashCoupon',
    label: '现金红包',
  },
  {
    value: 'points',
    label: '积分',
  },
  {
    value: 'payCoupon',
    label: '支付券',
  },
  {
    value: 'qd',
    label: '钱豆',
  },
  {
    value: 'redPacketCoupon',
    label: '借款返现券',
  },
  {
    value: 'cashCard',
    label: '提现卡',
  },
  {
    value: 'virtual',
    label: '虚拟奖品',
  },
  {
    value: 'activityCoin',
    label: '活动货币',
  },
];

export const rewardAmountExtraMap = {
  cashCoupon: '填红包发放面额，单位是“分”，如发放1元，则填100',
  points: '填积分发放面额，如发积分1个，则填1',
  payCoupon: '填发券数量，如发券1张，则填1',
  qd: '填钱豆发放面额，如发豆1个，则填1',
  redPacketCoupon: '填发券数量，如发券1张，则填1',
  cashCard: '填提现卡发放面额，单位【分】，如发放1元，则填写100',
  virtual: '填虚拟奖品数量',
  activityCoin: '填活动货币数量',
};

export const sendLimitExtraMap = {
  cashCoupon: '填红包上限总金额，不是上限数量',
  points: '填积分上限总量',
  payCoupon: '填该券发放上限数量',
  qd: '填钱豆发放面额，如发豆1个，则填1',
  redPacketCoupon: '填钱豆上限总量',
  cashCard: '填该券发放上限数量',
  virtual: '填虚拟奖品上限总量',
  activityCoin: '填活动货币上限总量',
};
