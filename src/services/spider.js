import request from '@/utils/request';

/**
 * 获取爬虫内容列表
 * @param {*} data
 * @returns
 */
export function getSpiderContentList(data) {
  return request('/fop-active-center/welfare/admin/crawler/content/list', {
    prefix: API_BASE_URL_LOBSTER,
    method: 'post',
    data,
  });
}

/**
 * 修改爬虫初筛列表选品状态
 * @param {*} data
 * @returns
 */
export function changeSpiderStatus(data) {
  return request('/fop-active-center/welfare/admin/crawler/content/select', {
    prefix: API_BASE_URL_LOBSTER,
    method: 'post',
    data,
  });
}

/**
 * 获取自动推送信息详情
 * @param {*} data
 * @returns
 */
export function getAutoPushDetail(data) {
  return request(
    '/fop-active-center/welfare/admin/crawler/content/auto-push/detail',
    {
      prefix: API_BASE_URL_LOBSTER,
      method: 'post',
      data,
    },
  );
}
