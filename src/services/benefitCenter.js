/* eslint-disable */
import request from "@/utils/request";

export function getBenefitContentList(data) {
  return request("/fop-active-center/welfare/admin/content/list", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

export function saveBenefitContent(data) {
  return request("/fop-active-center/welfare/admin/content/save", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

export function saveBenefitPriority(data) {
  return request("/fop-active-center/welfare/admin/content/priority", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

export function getBenefitContentDetail(data) {
  return request("/fop-active-center/welfare/admin/content/detail/list", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

export function auditBenefitContent(data) {
  return request("/fop-active-center/welfare/admin/content/audit", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

export function benefitContentOnline(data) {
  return request("/fop-active-center/welfare/admin/content/online", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

export function getBenefitPageList(data) {
  return request("/fop-active-center/welfare/admin/page/list", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

export function saveBenefitPage(data) {
  return request("/fop-active-center/welfare/admin/page/save", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

export function getBenefitPageDetail(data) {
  return request("/fop-active-center/welfare/admin/page/detail", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

export function auditBenefitPage(data) {
  return request("/fop-active-center/welfare/admin/page/audit", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

export function benefitPageOnline(data) {
  return request("/fop-active-center/welfare/admin/page/online", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

export function addContentTags(data) {
  return request("/fop-active-center/welfare/admin/content/tag", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

export function getTags() {
  return request("/fop-active-center/welfare/admin/query/tag/list", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
  });
}

export function getCategories() {
  return request("/fop-active-center/welfare/admin/query/category/list", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
  });
}
export function updateTagOrCategory({ type = "tag", data }) {
  return request(`/fop-active-center/welfare/admin/save/${type}`, {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}
export function updateTagOrCategoryPriority({ type = "tag", data }) {
  return request(`/fop-active-center/welfare/admin/${type}/priority`, {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}
export function getHomepageConfig() {
  return request("/fop-active-center/welfare/admin/home/<USER>/query", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
  });
}

export function saveHomepageConfig(data) {
  return request("/fop-active-center/welfare/admin/home/<USER>/save", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

export function getProductContentList(data) {
  return request("/fop-active-center/welfare/admin/content/query", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

export function getCategoryInfo(data) {
  return request("/fop-active-center/welfare/admin/home/<USER>/query", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

export function getCategoryIdsInfo(data) {
  return request("/fop-active-center/welfare/admin/query/category", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

export function getTagIdsInfo(data) {
  return request("/fop-active-center/welfare/admin/query/tag", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

/**
 * 获取什么值得买商品信息
 * @param {*} data
 * @returns
 */
export function getProductInfo(data) {
  return request("/api/favour/cheerio/index", {
    prefix: "https://mait.vmic.xyz",
    method: "post",
    data,
  });
}

/**
 * 查询平台列表信息
 * @param {*} data
 * @returns
 */
export function getPlatformInfo(data) {
  return request("/fop-active-center/welfare/admin/platform/query", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

/**
 * 清空页面缓存
 * @param {*} data
 * @returns
 */
export function pageCacheClear(data) {
  return request("/fop-active-center/welfare/admin/page/cache/clear", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

/**
 * 佣金配置查询(线上生效数据)
 */
export function getOnlineCommissionConfig() {
  return request("/fop-active-center/welfare/admin/commission/config/query", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
  });
}

/**
 * 佣金配置查询(审核数据)
 */
export function getAuditCommissionConfig() {
  return request(
    "/fop-active-center/welfare/admin/commission/config/audit/detail",
    {
      prefix: API_BASE_URL_LOBSTER,
      method: "post",
    }
  );
}

/**
 * 佣金配置提交审核
 * @param {*} data
 * @returns
 */
export function submitCommissionConfig(data) {
  return request("/fop-active-center/welfare/admin/commission/config/save", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

/**
 * 佣金配置审核通过/驳回
 * @param {*} data
 * @returns
 */
export function commissionConfigAudit(data) {
  return request("/fop-active-center/welfare/admin/commission/config/audit", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}

/**
 * 查询福利中心相关的操作日志
 * @param {*} data
 * @returns
 */
export function queryWalletLogList(data) {
  return request("/fop-active-center/operation/log/list", {
    prefix: API_BASE_URL_LOBSTER,
    method: "post",
    data,
  });
}
