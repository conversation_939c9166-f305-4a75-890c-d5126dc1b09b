import request from '@/utils/request';

/**
 * 获取合作资金方列表
 */
export function getPartnerList(data) {
  return request('/fundingChannel/partner/list', {
    method: 'post',
    data,
  });
}
/**
 * 获取利息优惠券配置列表
 */

export function getInterestCouponList(data) {
  return request('/coupon/template/list', {
    method: 'post',
    data,
  });
}

/**
 * 配置上下架
 * @param {*} data
 * @returns
 */
export function settingOnline(data) {
  return request('/coupon/template/online', {
    method: 'post',
    data,
  });
}

/**
 * 删除配置
 * @param {*} data
 * @returns
 */
export function deleteInterestCoupon(data) {
  return request('/coupon/template/delete', {
    method: 'post',
    data,
  });
}

/**
 * 利息优惠券详情
 * @param {*} data
 * @returns
 */
export function getInterestCouponDetails(data) {
  return request('/coupon/template/view', {
    method: 'get',
    params: data,
  });
}

/**
 * 审核操作
 * @param {*} data
 * @returns
 */
export function auditInterestCoupon(data) {
  return request('/coupon/template/audit', {
    method: 'post',
    data,
  });
}

/**
 * 新增或编辑虚拟库存
 * @param {*} data
 * @returns
 */
export function saveInterestCoupon(data) {
  return request('/coupon/template/save', {
    method: 'post',
    data,
  });
}
