import request from '@/utils/request';

export function getContinuousCampaignList(data) {
  return request('/bm/continuousCampaign/list', {
    method: 'post',
    data,
  });
}

export function saveContinuousCampaign(data) {
  return request('/bm/continuousCampaign/save', {
    method: 'post',
    data,
  });
}

export function getContinuousCampaignDetail(params) {
  return request('/bm/continuousCampaign/view', {
    method: 'get',
    params,
  });
}

export function auditContinuousCampaign(data) {
  return request('/bm/continuousCampaign/audit', {
    method: 'post',
    data,
  });
}

export function continuousCampaignOnline(data) {
  return request('/bm/continuousCampaign/online', {
    method: 'post',
    data,
  });
}

export function deleteContinuousCampaign(data) {
  return request('/bm/continuousCampaign/delete', {
    method: 'post',
    data,
  });
}

// 获取奖励明细iframe链接
export function getBiUrl(data) {
  return request('/bm/continuousCampaign/bi/url', {
    method: 'post',
    data,
  });
}

// 获取dmp人群标签
export function getDmpTag(data) {
  return request('/dmp/dmpTag', {
    method: 'post',
    data,
  });
}

// 获取审核操作记录
export function queryLogList(data) {
  return request('/operation/log/list', {
    method: 'get',
    params: data,
  });
}
