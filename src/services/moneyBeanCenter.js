import request from '@/utils/request';

// /bm/cpd/admin/query/qd/commonConfig
// 查询通用配置（详情）
export function getCommonConfig(data) {
  return request.post('/bm/cpd/admin/qd/commonConfig/query', {
    data,
  });
}

// 查询通用配置detail，审核页
/**
 * 获取通用详情
 *
 * @param {object} data - 输入的数据
 * @returns {Promise} 包含通用配置详情的Promise对象
 */
export function getCommonDetail(data) {
  return request.post('/bm/cpd/admin/qd/commonConfig/detail', {
    data,
  });
}

// 提交通用配置
export function submitCommonConfig(data) {
  return request.post('/bm/cpd/admin/qd/commonConfig/save', {
    data,
  });
}

// 审核通用配置
export function auditCommonConfig(data) {
  return request.post('/bm/cpd/admin/qd/commonConfig/audit', {
    data,
  });
}

// /bm/cpd/admin/query/qd/taskConfig
// 查询任务配置（详情）
export function getTaskConfig(data) {
  return request.post('/bm/cpd/admin/qd/taskConfig/query', {
    data,
  });
}

// 查询任务配置detail，审核页
export function getTaskDetail(data) {
  return request.post('/bm/cpd/admin/qd/taskConfig/detail', {
    data,
  });
}

export function getTaskConfigList(data) {
  return request.post('/bm/cpd/admin/qd/taskConfig/list', {
    data,
  });
}

// 保存任务配置
export function saveTaskConfig(data) {
  return request.post('/bm/cpd/admin/qd/taskConfig/save', {
    data,
  });
}

// 提交任务配置
export function submitTaskConfig(data) {
  return request.post('/bm/cpd/admin/qd/taskConfig/submit', {
    data,
  });
}

// 审核任务配置
export function auditTaskConfig(data) {
  return request.post('/bm/cpd/admin/qd/taskConfig/audit', {
    data,
  });
}

// 查询核销配置（详情）
export function getExchangeConfig(data) {
  return request.post('/bm/cpd/admin/qd/exchangeConfig/query', {
    data,
  });
}

// 查询核销配置detail，审核页
export function getExchangeDetail(data) {
  return request.post('/bm/cpd/admin/qd/exchangeConfig/detail', {
    data,
  });
}

// 提交核销配置
export function submitExchangeConfig(data) {
  return request.post('/bm/cpd/admin/qd/exchangeConfig/save', {
    data,
  });
}

// 审核核销配置
export function auditExchangeConfig(data) {
  return request.post('/bm/cpd/admin/qd/exchangeConfig/audit', {
    data,
  });
}

// 操作记录列表
export function getOperationLogList(data) {
  const { bizCode, operationId, channelType, page, pageSize } = data;
  return request.get(
    `/operation/log/list?bizCode=${bizCode}&operationId=${operationId}&channelType=${channelType}&page=${page}&pageSize=${pageSize}`,
  );
}

// 获取dmp人群标签
export function getDmpTag(data) {
  return request.post('/dmp/dmpTag', {
    data,
  });
}

// 获取库存ID
export function getQuotaId(data) {
  return request.post('/coupon/common/quota/query', {
    data,
  });
}
