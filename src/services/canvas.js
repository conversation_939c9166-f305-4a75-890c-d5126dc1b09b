import request from '@/utils/request';

export function getCanvasList(data) {
  return request('/lobster/marketing-automation/marketing/canvas/list', {
    method: 'post',
    data,
  });
}

export function auditCanvas(data) {
  return request('/lobster/marketing-automation/marketing/canvas/audit', {
    method: 'post',
    data,
  });
}

export function cancelAuditCanvas(data) {
  return request('/lobster/marketing-automation/marketing/canvas/cancel/audit', {
    method: 'post',
    data,
  });
}

export function deleteCanvas(data) {
  return request('/lobster/marketing-automation/marketing/canvas/delete', {
    method: 'post',
    data,
  });
}

export function operateLineCanvas(data) {
  return request('/lobster/marketing-automation/marketing/canvas/online', {
    method: 'post',
    data,
  });
}

export function saveCanvas(data) {
  return request('/lobster/marketing-automation/marketing/canvas/save', {
    method: 'post',
    data,
  });
}

export function getCanvasDetail(data) {
  return request('/lobster/marketing-automation/marketing/canvas/view', {
    method: 'post',
    data,
  });
}

export function getBusinessTypeList() {
  return request('/finance/businessType/list', {
    method: 'post',
  });
}
export function getProductTypeList(data) {
  return request('/finance/productType/list', {
    method: 'post',
    params: data,
    // requestType: 'form',
  });
}

export function getFundChannel(data) {
  return request('/fundingChannel/partner/list', {
    method: 'post',
    data,
  });
}
export function experimentView(data) {
  return request('/lobster/finance-experiment/experiment/view', {
    method: 'post',
    data,
    // requestType: 'form',
  });
}

export function getSMSTemplate(data) {
  return request('/msg/getSMSTemplate', {
    method: 'get',
    params: {
      ...data,
    },
    // requestType: 'form',
  });
}

export function getNotifyScene(data) {
  return request('/lobster/marketing-automation/msg/getNotifyScene', {
    method: 'post',
    data,
  });
}

export function getSmartMessageTemplate(data) {
  return request('/lobster/marketing-automation/msg/getSmartMessageTemplate', {
    method: 'post',
    data,
  });
}

export function quotaQuery(data) {
  return request('/coupon/common/quota/query', {
    method: 'post',
    data,
  });
}

export function viewBrief(data) {
  return request('/redPackageCoupon/template/view/brief', {
    method: 'post',
    params: data,
  });
}

export function queryLogList(data) {
  return request('/lobster/marketing-automation/operation/log/list', {
    method: 'post',
    data,
  });
}

export function terminateDelay(data) {
  return request('/lobster/marketing-automation/marketing/canvas/terminateDelay', {
    method: 'post',
    data,
  });
}
