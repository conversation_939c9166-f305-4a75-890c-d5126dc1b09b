import request from '@/utils/request';

/**
 * 虚拟奖品库存列表
 */
export function getVirtualList(data) {
  return request('/virtual/quota/list', {
    method: 'post',
    data,
  });
}

/**
 * 活动上下架
 * @param {*} data
 * @returns
 */
export function settingOnline(data) {
  return request('/virtual/quota/online', {
    method: 'post',
    data,
  });
}

/**
 * 删除活动
 * @param {*} data
 * @returns
 */
export function deleteVirtual(data) {
  return request('/virtual/quota/delete', {
    method: 'post',
    data,
  });
}

/**
 * 虚拟奖品库存详情
 * @param {*} data
 * @returns
 */
export function getVirtualDetails(data) {
  return request('/virtual/quota/view', {
    method: 'get',
    params: data,
  });
}

/**
 * 审核操作
 * @param {*} data
 * @returns
 */
export function auditVirtual(data) {
  return request('/virtual/quota/audit', {
    method: 'post',
    data,
  });
}

/**
 * 新增或编辑虚拟库存
 * @param {*} data
 * @returns
 */
export function saveVirtual(data) {
  return request('/virtual/quota/save', {
    method: 'post',
    data,
  });
}
