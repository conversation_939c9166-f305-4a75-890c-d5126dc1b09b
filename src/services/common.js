import request from '@/utils/request';

// 获取dmp人群标签
export function getDmpTag(data) {
  return request('/dmp/dmpTag', {
    method: 'post',
    data,
  });
}

// 人群包查看预览
export function getUsePackageView(data) {
  return request('/user/file/preview', {
    method: 'get',
    params: data,
  });
}

// 获取审核操作记录
export function queryLogList(data) {
  return request('/operation/log/list', {
    method: 'get',
    params: data,
  });
}

// 获取库存ID
export function getQuotaId(data) {
  return request('/coupon/common/quota/query', {
    method: 'post',
    data,
  });
}

// 查询奖品列表
export function getRewards(data) {
  return request('/coupon/quota/rewards', {
    method: 'get',
    params: data,
  });
}

// 返现券活动相关
// 返现券发放规则模版-获取详情
export function getTemplateBrief(params) {
  return request('/redPackageCoupon/template/view/brief', {
    method: 'get',
    params,
  });
}
