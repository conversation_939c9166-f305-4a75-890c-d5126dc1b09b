import request from '@/utils/request';

// 提交资格配置
export function submitLoanDiscountConfig(data) {
  return request.post('/qualification/save', {
    data: { ...data, type: 'discount' },
  });
}
// 查询资格配置（详情）
export function getLoanDiscountConfig(data) {
  const type = 'discount';
  return request.get(`/qualification/queryLatest?type=${type}`);
}

// 审核页 - 审核资格配置
export function auditLoanDiscountConfig(data) {
  return request.post('/qualification/audit?', {
    data: { ...data, type: 'discount' },
  });
}

// 审核页 - 查看资格详情配置审核
export function getLoanDiscountDetail(data) {
  const type = 'discount';
  return request.get(`/qualification/audit/detail?type=${type}`, {});
}

// 操作记录列表
export function getOperationLogList(data) {
  const { bizCode, operationId, page, pageSize } = data;
  return request.get(
    `/operation/log/list?bizCode=${bizCode}&operationId=${operationId}&page=${page}&pageSize=${pageSize}`,
  );
}
