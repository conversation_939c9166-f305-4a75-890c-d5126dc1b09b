import request from '@/utils/request';

/**
 * 玩法列表
 * @param {*} data
 * @returns
 */
export function getActivityPlayList(data) {
  return request('/activity/play/list', {
    method: 'post',
    data,
  });
}

/**
 * 玩法删除
 * @param {*} data
 * @returns
 */
export function deleteActivityPlay(data) {
  return request('/activity/play/delete', {
    method: 'post',
    data,
  });
}

/**
 * 玩法上下架
 * @param {*} data
 * @returns
 */
export function activityPlayOnline(data) {
  return request('/activity/play/online', {
    method: 'post',
    data,
  });
}

/**
 * 玩法详情
 * @param {*} data
 * @returns
 */
export function getActivityPlayDetail(data) {
  return request('/activity/play/view', {
    method: 'get',
    params: data,
  });
}

/**
 * 玩法提交
 * @param {*} data
 * @returns
 */
export function saveActivityPlay(data) {
  return request('/activity/play/save', {
    method: 'post',
    data,
  });
}

/**
 * 玩法审核
 * @param {*} data
 * @returns
 */
export function auditActivityPlay(data) {
  return request('/activity/play/audit', {
    method: 'post',
    data,
  });
}

/**
 * 虚拟奖品库存详情
 * @param {*} data
 * @returns
 */
export function getVirtualList(data) {
  return request('/virtual/quota/listByIds', {
    method: 'post',
    requestType: 'form',
    data,
  });
}

/**
 * 获取code列表
 * type: 1-活动，2-订阅
 */
export function getCodeList(data) {
  return request('/activity/play/info', {
    method: 'post',
    data,
  });
}
