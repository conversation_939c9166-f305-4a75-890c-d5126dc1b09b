import request from '@/utils/request';

/**
 * 获取玩法列表
 */
export function getActivityPlayInfo(data) {
  return request('/activity/play/info', {
    method: 'post',
    data,
  });
}

/**
 * 活动列表
 */
export function getActivityList(data) {
  return request('/marketing/activity/list', {
    method: 'post',
    data,
  });
}

/**
 * 活动上下架
 * @param {*} data
 * @returns
 */
export function settingOnline(data) {
  return request('/marketing/activity/online', {
    method: 'post',
    data,
  });
}

/**
 * 删除活动
 * @param {*} data
 * @returns
 */
export function deleteActivity(data) {
  return request('/marketing/activity/delete', {
    method: 'post',
    data,
  });
}

/**
 * 获取详情接口
 * @param {*} data
 * @returns
 */
export function getActivityDetails(data) {
  return request('/marketing/activity/view', {
    method: 'get',
    params: data,
  });
}

/**
 * 审核
 * @param {*} data
 * @returns
 */
export function auditActivity(data) {
  return request('/marketing/activity/audit', {
    method: 'post',
    data,
  });
}

/**
 * 提交保存
 * @param {*} data
 * @returns
 */
export function saveActivityDetails(data) {
  return request('/marketing/activity/save', {
    method: 'post',
    data,
  });
}
