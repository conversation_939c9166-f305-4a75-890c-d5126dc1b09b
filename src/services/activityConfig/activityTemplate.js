import request from '@/utils/request';

/**
 * 获取活动列表
 */
export function getActivitySettingList(data) {
  return request('/coupon/voucher/campaign/list', {
    method: 'post',
    data,
  });
}

/**
 * 活动上下架
 * @param {*} data
 * @returns
 */
export function activitySettingOnline(data) {
  return request('/coupon/voucher/campaign/online', {
    method: 'post',
    data,
  });
}

/**
 * 获取活动场景列表
 * @param {*} data
 * @returns
 */
export function getActivitySceneList(data) {
  return request('/scene/list', {
    method: 'get',
    params: data,
  });
}

/**
 * 删除活动
 * @param {*} data
 * @returns
 */
export function deleteActivity(data) {
  return request('/coupon/voucher/campaign/delete', {
    method: 'post',
    data,
  });
}

/**
 * 获取详情接口
 * @param {*} data
 * @returns
 */
export function getActivityDetails(data) {
  return request('/coupon/voucher/campaign/detail', {
    method: 'get',
    params: data,
  });
}

/**
 * 获取活动限制渠道列表
 * @param {*} campaignType
 * @returns
 */
export function getActivityChannel(campaignType) {
  return request('/coupon/common/channelList', {
    method: 'get',
    params: {
      campaignType,
    },
  });
}

/**
 * 审核
 * @param {*} data
 * @returns
 */
export function auditActivity(data) {
  return request('/coupon/voucher/campaign/audit', {
    method: 'post',
    data,
  });
}

/**
 * 提交保存
 * @param {*} data
 * @returns
 */
export function saveActivityDetails(data) {
  return request('/coupon/voucher/campaign/save', {
    method: 'post',
    data,
  });
}

/**
 * 活动信息统计
 * @param {*} data
 * @returns
 */
export function activityStatistics(data) {
  return request('/coupon/voucher/campaign/statistics/list', {
    method: 'post',
    data,
  });
}
