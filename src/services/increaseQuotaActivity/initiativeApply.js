import request from '@/utils/request';

/**
 * 主动提额配置列表查询
 */
export function getBatchIncreaseLimitList(data) {
  return request('/batchIncreaseLimit/list', {
    method: 'post',
    data: {
      ...data,
      page: data.current,
    },
  });
}

/**
 * 主动提额配置上线
 * @param {*} data
 * @returns
 */
export function batchIncreaseLimitOnline(data) {
  return request('/batchIncreaseLimit/online', {
    method: 'post',
    data,
  });
}

/**
 * 主动提额配置删除操作
 * @param {*} data
 * @returns
 */
export function deleteBatchIncreaseLimit(data) {
  return request('/batchIncreaseLimit/delete', {
    method: 'get',
    data,
  });
}

/**
 * 主动提额配置详情
 * @param {*} data
 * @returns
 */
export function getBatchIncreaseLimitDetails(data) {
  return request('/batchIncreaseLimit/view', {
    method: 'get',
    params: data,
  });
}

/**
 * 主动提额配置审核
 * @param {*} data
 * @returns
 */
export function auditBatchIncreaseLimit(data) {
  return request('/batchIncreaseLimit/audit', {
    method: 'post',
    data,
  });
}

/**
 * 创建/更新主动提额配置
 * @param {*} data
 * @returns
 */
export function saveBatchIncreaseLimit(data) {
  return request('/batchIncreaseLimit/save', {
    method: 'post',
    data,
  });
}
