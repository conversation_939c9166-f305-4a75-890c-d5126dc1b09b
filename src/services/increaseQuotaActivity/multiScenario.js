import request from '@/utils/request';

/**
 * 多场景提额配置列表查询
 */
export function getAutoIncreaseLimitList(data) {
  return request('/autoIncreaseLimit/list', {
    method: 'post',
    data: {
      ...data,
      page: data.current,
    },
  });
}

/**
 * 多场景提额配置上线
 * @param {*} data
 * @returns
 */
export function autoIncreaseLimitOnline(data) {
  return request('/autoIncreaseLimit/online', {
    method: 'post',
    data,
  });
}

/**
 * 多场景提额配置删除
 * @param {*} data
 * @returns
 */
export function deleteAutoIncreaseLimit(data) {
  return request('/autoIncreaseLimit/delete', {
    method: 'get',
    data,
  });
}

/**
 * 多场景提额配置详情
 * @param {*} data
 * @returns
 */
export function getAutoIncreaseLimitDetails(data) {
  return request('/autoIncreaseLimit/view', {
    method: 'get',
    params: data,
  });
}

/**
 * 多场景提额配置审核
 * @param {*} data
 * @returns
 */
export function auditAutoIncreaseLimit(data) {
  return request('/autoIncreaseLimit/audit', {
    method: 'post',
    data,
  });
}

/**
 * 创建/更新多场景提额配置
 * @param {*} data
 * @returns
 */
export function saveAutoIncreaseLimit(data) {
  return request('/autoIncreaseLimit/save', {
    method: 'post',
    data,
  });
}

/**
 * 触达消息场景 字典值
 * @param
 * @returns
 */
export function sceneListByViewGroupName() {
  return request('/cod/dutyScene/sceneListByViewGroupName', {
    method: 'get',
    params: {
      viewGroupName: '消费贷-提额',
      bizCode: 'cod',
    },
  });
}
