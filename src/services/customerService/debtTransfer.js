import request from '@/utils/request';

/**
 * 债转复核查询列表
 * @param {*} data
 * @returns
 */
export function getDebtTransferList(data) {
  return request('/approve/repayReview/list', {
    prefix: API_BASE_URL_CUSTOMER, // eslint-disable-line
    method: 'post',
    data,
  });
}

/**
 * 债转复核详情
 * @param {*} data
 * @returns
 */
export function getDebtTransferDetail(data) {
  return request('/approve/repayReview/detail', {
    prefix: API_BASE_URL_CUSTOMER, // eslint-disable-line
    method: 'post',
    data,
  });
}
