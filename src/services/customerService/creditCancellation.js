import request from '@/utils/request';

/**
 * 审批提交
 * @param {*} data
 * @returns
 */
export function approveSubmit(data) {
  return request('/approve/submit', {
    prefix: API_BASE_URL_CUSTOMER, // eslint-disable-line
    method: 'post',
    data,
  });
}

/**
 * 查询可注销额度列表
 * @param {*} data
 * @returns
 */
export function getLogOffLimitList(data) {
  return request('/loan/canLogOffLimitList', {
    prefix: API_BASE_URL_CUSTOMER, // eslint-disable-line
    method: 'post',
    data,
  });
}
