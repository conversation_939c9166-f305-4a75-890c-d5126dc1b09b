// 客服平台通用接口
import request from '@/utils/request';

/**
 * 查询用户信息
 * @returns
 */
export function getUser() {
  return request('/permission/user', {
    prefix: API_BASE_URL_CUSTOMER, // eslint-disable-line
    method: 'get',
  });
}

/**
 * 复核通过
 * @param {*} data
 * @returns
 */
export function auditPass(data) {
  return request('/approve/repayReview/pass', {
    prefix: API_BASE_URL_CUSTOMER, // eslint-disable-line
    method: 'post',
    data,
  });
}

/**
 * 复核驳回
 * @param {*} data
 * @returns
 */
export function auditRefuse(data) {
  return request('/approve/repayReview/refuse', {
    prefix: API_BASE_URL_CUSTOMER, // eslint-disable-line
    method: 'post',
    data,
  });
}

// 获取图片
export function getImages(data) {
  return request('/transferAccounts/getImg', {
    prefix: API_BASE_URL_CUSTOMER, // eslint-disable-line
    method: 'post',
    data,
  });
}

/**
 * 返回复核人列表
 * @param {*} data
 * @returns
 */
export function getReviewerList(data) {
  return request('/transferAccounts/reviewerList', {
    prefix: API_BASE_URL_CUSTOMER, // eslint-disable-line
    method: 'post',
    data,
  });
}
