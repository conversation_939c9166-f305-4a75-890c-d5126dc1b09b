import request from '@/utils/request';

// 查询核销配置（详情）
export function getRedEnvelopTaskConfig(data) {
  return request.post('/bm/cpd/admin/DOWNLOAD_RED/taskConfig/query', {
    data,
  });
}
// 查询任务配置detail，审核页
export function getRedEnvelopTaskDetail(data) {
  return request.post('/bm/cpd/admin/DOWNLOAD_RED/taskConfig/detail', {
    data,
  });
}
// 提交核销配置
export function submitRedEnvelopTaskConfig(data) {
  return request.post('/bm/cpd/admin/DOWNLOAD_RED/taskConfig/save', {
    data,
  });
}
// 审核核销配置
export function auditRedEnvelopTaskConfig(data) {
  return request.post('/bm/cpd/admin/DOWNLOAD_RED/taskConfig/audit', {
    data,
  });
}

// 查询核销配置V2（详情）
export function getRedEnvelopTaskConfigV2(data) {
  return request.post('/bm/cpd/admin/DOWNLOAD_RED_V2/taskConfig/query', {
    data,
  });
}
// 查询任务配置detail，审核页V2
export function getRedEnvelopTaskDetailV2(data) {
  return request.post('/bm/cpd/admin/DOWNLOAD_RED_V2/taskConfig/detail', {
    data,
  });
}
// 提交核销配置V2
export function submitRedEnvelopTaskConfigV2(data) {
  return request.post('/bm/cpd/admin/DOWNLOAD_RED_V2/taskConfig/save', {
    data,
  });
}
// 审核核销配置V2
export function auditRedEnvelopTaskConfigV2(data) {
  return request.post('/bm/cpd/admin/DOWNLOAD_RED_V2/taskConfig/audit', {
    data,
  });
}
// 操作记录列表
export function getOperationLogList(data) {
  const { bizCode, operationId, page, pageSize } = data;
  return request.get(
    `/operation/log/list?bizCode=${bizCode}&operationId=${operationId}&page=${page}&pageSize=${pageSize}`,
  );
}