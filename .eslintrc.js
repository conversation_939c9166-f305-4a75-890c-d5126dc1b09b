const path = require('path');

module.exports = {
  env: {
    browser: true,
    es2021: true,
  },
  extends: 'airbnb',
  overrides: [
    {
      env: {
        node: true,
      },
      files: [
        '.eslintrc.{js,cjs}',
      ],
      parserOptions: {
        sourceType: 'script',
      },
    },
  ],
  parserOptions: {
    ecmaVersion: 2021,
    sourceType: 'module',
  },
  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-continue': 'off',
    'func-names': 'off',
    'no-await-in-loop': 'off',
    'import/prefer-default-export': 'off',
    'no-plusplus': 'off',
    'no-unused-expressions': ['error', { allowShortCircuit: true, allowTernary: true }],
    'jsx-a11y/no-static-element-interactions': 'off',
    'jsx-a11y/click-events-have-key-events': 'off',
    'react/no-array-index-key': 'warn',
    'jsx-a11y/anchor-is-valid': 'off',
    'no-param-reassign': 'warn',
    'react/prop-types': 'warn',
    'prefer-destructuring': 'off',
    'consistent-return': 'warn',
    'prefer-destructuring': 'off',
    'consistent-return': 'warn',
    'max-len': ['error', { code: 200 }],
    complexity: ['error', { max: 40 }],
    "import/extensions": [
        "error",
        {
            "js": "ignorePackages"
        }
    ],
    "react/jsx-props-no-spreading": ["error", {"custom": "ignore"}]
  },
  settings: {
    'import/resolver': {
      alias: { // eslint-import-resolver-alias 插件
        map: [
          ['src', path.resolve(__dirname, 'src')],
          ['@', path.resolve(__dirname, 'src')],
          ['@@', path.resolve(__dirname, 'src/.umi')],
        ],
        extensions: ['.ts', '.js', '.jsx', '.tsx'],
      },
    },
  },
};
