import { defineConfig } from 'umi';
import routes from '../src/routes/dev';

export default defineConfig({
  routes,
  define: {
    // API_BASE_URL: 'http://finance-operation-admin-test1.vmic.xyz/api', // 测试1
    // API_BASE_URL: 'http://finance-operation-admin-test2.vmic.xyz/api', // 测试2
    // API_BASE_URL: 'http://finance-operation-admin-test3.vmic.xyz/api', // 测试3
    // API_BASE_URL: 'https://fapi.vmic.xyz/mock/696',
    // API_BASE_URL: 'http://10.15.49.10:8080', // 高锋本地
    API_BASE_URL: '/api', // 2.1项目环境
    API_BASE_URL_LOBSTER: '/mmm', //
    // API_BASE_URL_CUSTOMER: 'http://vivo-loan-customer-test.vmic.xyz',
    API_BASE_URL_CUSTOMER: '/customer', // 客服平台
  },
  proxy: {
    '/customer': {
      // target: 'http://vivo-loan-customer-test.vmic.xyz', //
      target: 'http://vivo-loan-customer-testbase.vmic.xyz', //
      changeOrigin: true,
      pathRewrite: { '^/customer': '' },
    },
    '/mmm': {
      target: 'http://finance-operation-admin-test.vmic.xyz',
      // 'target': 'http://10.15.51.4:8080', // 高锋本地
      changeOrigin: true,
      pathRewrite: { '/mmm': '/api/lobster' },
    },
    '/api': {
      // target: 'http://10.13.184.234:8080',
      // target: 'https://fapi.vmic.xyz/mock/696',
      'target': 'http://finance-operation-admin-test.vmic.xyz',
      changeOrigin: true,
      // pathRewrite: { '^/api': '' },
    }
  },
  devServer: {
    hmr: { overlay: false }, // 确保 HMR 开启
  },
});
