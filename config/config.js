import { defineConfig } from 'umi';

const { UMI_ENV } = process.env;

const defaultConfig = {
  nodeModulesTransform: {
    type: 'none',
  },
  dynamicImport: {
    loading: '@/components/PageLoading/index',
  },
  title: '金融运营平台',
  define: {
    UMI_ENV
  },
  qiankun: {
    slave: {},
  },
  fastRefresh: {},
  mfsu: {},
  locale: {
    default: 'zh-CN',
    antd: true,
    title: false,
    baseNavigator: true,
    baseSeparator: '-',
  },
  favicon:
    'https://gaia-vivofs.vivo.com.cn/qEiqKoT2I4DBQpyI/cb8fc5b0-d07c-11e9-bab6-892c42bf9a57.png',
};

// mfsu只能添加在config.js 当中
// 打包的时候，这个配置不能配，会报错
if (UMI_ENV == 'dev') {
  defaultConfig.mfsu = false;
}

export default defineConfig(defaultConfig);
