import { defineConfig } from 'umi';
import routes from '../src/routes/prod';

export default defineConfig({
  routes,
  base: 'h5',
  define: {
    API_BASE_URL: 'http://finance-operation-admin-test.vmic.xyz/api',
    API_BASE_URL_LOBSTER: 'http://finance-operation-admin-test.vmic.xyz/api/lobster',
    API_BASE_URL_CUSTOMER: 'http://vivo-loan-customer-testbase.vmic.xyz',
  },
  chainWebpack(config) {
    // 修改js输出目录
    config.output
      .filename(`static/js/[name].[contenthash:8].js`)
      .chunkFilename(`static/js/[name].[contenthash:8].async.js`);

    // 修改css输出目录
    config.plugin('extract-css').tap((args) => [
      {
        ...args[0],
        filename: `static/css/[name].[contenthash:8].css`,
        chunkFilename: `static/css/[name].[contenthash:8].chunk.css`,
      },
    ]);
  },
});
