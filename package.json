{"name": "finance-operation-admin", "private": true, "scripts": {"dev": "cross-env UMI_ENV=dev umi dev", "dev:test": "cross-env UMI_ENV=test umi dev", "build:testbase": "cross-env UMI_ENV=testbase umi build", "build:test": "cross-env UMI_ENV=test umi build", "build:test2": "cross-env UMI_ENV=test2 umi build", "build:test3": "cross-env UMI_ENV=test3 umi build", "build:pre": "cross-env UMI_ENV=pre umi build", "build:prod": "cross-env UMI_ENV=prod umi build", "postinstall": "umi generate tmp", "prettier": "prettier --write '**/*.{js,jsx,tsx,ts,less,md,json}'", "lint:style": "stylelint --fix \"src/**/*.less\" --syntax less", "lint:fix": "eslint --fix --cache --ext .js,.jsx,.ts,.tsx --format=pretty ./src && npm run lint:style", "lint-staged:js": "eslint --ext .js,.jsx,.ts,.tsx ", "lint-staged": "lint-staged", "tsc": "tsc --noEmit"}, "gitHooks": {"pre-commit": "lint-staged"}, "lint-staged": {"./**/*.{js,jsx,vue}": ["eslint --fix"]}, "dependencies": {"@ant-design/icons": "^4.8.0", "@ant-design/pro-components": "2.7.0", "@ant-design/pro-layout": "^7.14.7", "@logicflow/core": "^2.0.12", "@logicflow/react-node-registry": "^1.0.14", "@umijs/plugin-qiankun": "^2.43.3", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-react": "^1.0.6", "antd": "5.16.1", "braft-editor": "^2.3.9", "classnames": "^2.5.1", "dayjs": "^1.11.7", "finance-busi-components-toB": "0.0.33-beta.9", "immutability-helper": "^3.1.1", "lodash": "^4.17.21", "lodash-es": "^4.17.21", "nano-id": "^1.1.0", "nanoid": "^5.0.7", "prop-types": "^15.8.1", "react": "^18.2.0", "react-cookies": "^0.1.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-redux": "^8.1.3", "react-router-dom": "^6.27.0", "redux": "^4.2.1", "umi": "^3.5.36"}, "devDependencies": {"@commitlint/cli": "^17.8.1", "@commitlint/config-conventional": "^17.8.1", "@types/react": "^18.2.14", "@types/react-dom": "^18.2.6", "@umijs/fabric": "^2.9.0", "@umijs/preset-react": "1.x", "@umijs/test": "^3.5.36", "@umijs/yorkie": "^2.0.5", "cross-env": "^7.0.3", "eslint": "^7.32.0", "eslint-config-airbnb": "^18.2.1", "eslint-config-prettier": "^6.15.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-import-resolver-webpack": "^0.13.9", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.0", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-hooks": "^4.6.2", "glob": "^8.0.3", "husky": "^8.0.3", "lint-staged": "^14.0.1", "prettier": "^2.8.8", "typescript": "^4.1.2", "vw-fed-cli": "^0.0.4-beta.113"}}